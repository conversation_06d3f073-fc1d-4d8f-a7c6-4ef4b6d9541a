SET SESSION FOREIGN_KEY_CHECKS = 0;

/* Drop Tables */
SET GROUP_CONCAT_MAX_LEN = 32768;

SET @tables = NULL;
SELECT GROUP_CONCAT('`', table_name, '`')
INTO @tables
FROM information_schema.tables
WHERE table_schema = (SELECT DATABASE());
SELECT IFNULL(@tables, 'dummy')
INTO @tables;

SET @tables = CONCAT('DROP TABLE IF EXISTS ', @tables);
PREPARE stmt FROM @tables;
EXECUTE stmt;
SET FOREIGN_KEY_CHECKS = 1;

/* Create Tables */
-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
CREATE TABLE `sys_user`
(
    `user_id`    varchar(48)  NOT NULL COMMENT '用户ID',
    `user_name`  varchar(64)  NOT NULL COMMENT '用户姓名',
    `password`   varchar(64)  NOT NULL COMMENT '密码',
    `nick_name`  varchar(64)  NULL COMMENT '昵称',
    `remark`     varchar(128) NULL     DEFAULT '' COMMENT '备注',
    `login_time` varchar(36)  NULL     DEFAULT NULL COMMENT '最后登录时间',
    `login_ip`   varchar(36)  NULL     DEFAULT NULL COMMENT '最后登录IP',
    `created_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `updated_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by` varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '用户表'
  ROW_FORMAT = Compact;


-- ----------------------------
-- Table structure for sys_solid_user
-- ----------------------------
CREATE TABLE `sys_solid_user`
(
    `user_id`    varchar(48)  NOT NULL COMMENT '用户ID',
    `user_name`  varchar(64)  NOT NULL COMMENT '用户姓名',
    `password`   varchar(64)  NOT NULL COMMENT '密码',
    `nick_name`  varchar(64)  NULL COMMENT '昵称',
    `remark`     varchar(128) NULL     DEFAULT '' COMMENT '备注',
    `login_time` varchar(36)  NULL     DEFAULT NULL COMMENT '最后登录时间',
    `login_ip`   varchar(36)  NULL     DEFAULT NULL COMMENT '最后登录IP',
    `created_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `updated_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by` varchar(30)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '固化用户表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
CREATE TABLE `sys_role`
(
    `id`           int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_name`    varchar(30) NOT NULL COMMENT '角色名称',
    `ident_key`    varchar(64) NOT NULL COMMENT '标识字段',
    `role_del_flg` char(1)     NOT NULL DEFAULT '0' COMMENT '删除标识',
    `created_at`   timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `updated_at`   timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`   varchar(30) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '角色表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_auth
-- ----------------------------
CREATE TABLE `sys_auth`
(
    `id`            varchar(10) NOT NULL COMMENT '主键',
    `auth_code`     varchar(50) NOT NULL COMMENT '权限编码',
    `auth_name`     varchar(50) NOT NULL COMMENT '权限名称',
    `parent_id`     varchar(10) NOT NULL COMMENT '父级权限主键',
    `show_sequence` int(11)     NOT NULL COMMENT '显示顺序',
    `group_key`     tinyint(1)  NOT NULL DEFAULT 0 COMMENT 'GROUP用',
    `created_at`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `updated_at`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`    varchar(30) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`),
    UNIQUE (`auth_code`, `auth_name`)
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '权限表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_role_auth
-- ----------------------------
CREATE TABLE `sys_role_auth`
(
    `id`              int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_id`         int(11)     NOT NULL COMMENT '角色ID',
    `auth_id`         varchar(10) NOT NULL COMMENT '权限ID',
    `role_auth_value` char(1)     NOT NULL comment '有无权限',
    `created_at`      timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `updated_at`      timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`      varchar(30) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`),
    UNIQUE (`role_id`, `auth_id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '角色权限关系表'
  ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
CREATE TABLE `sys_user_role`
(
    `id`         int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id`    varchar(48) NOT NULL COMMENT '用户ID',
    `role_id`    int(11)     NOT NULL COMMENT '角色ID',
    `created_at` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '插入时间',
    `updated_at` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by` varchar(30) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`),
    UNIQUE (`user_id`, `role_id`)
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci COMMENT = '用户角色关系表'
  ROW_FORMAT = Compact;


CREATE OR REPLACE VIEW `view_sys_use_auth` AS
SELECT a.user_id,
       a.user_name,
       a.password,
       'R_ADMIN'              as role_name,
       (select GROUP_CONCAT(b.auth_code, '_W') as auth_code
        from sys_auth b
        group by b.group_key) as auth_code
FROM sys_solid_user a
UNION
SELECT a.user_id,
       a.user_name,
       a.password,
       GROUP_CONCAT(DISTINCT (c.role_name)) AS role_name,
       GROUP_CONCAT(DISTINCT (
           CASE
               d.role_auth_value
               WHEN '1' THEN CONCAT(e.auth_code, '_R')
               WHEN '2' THEN CONCAT(e.auth_code, '_W')
               END
           ))                               AS auth_code
FROM `sys_user` a
         INNER JOIN `sys_user_role` b ON a.user_id = b.user_id
         INNER JOIN `sys_role` c ON b.role_id = c.id AND c.role_del_flg = '0'
         INNER JOIN `sys_role_auth` d ON c.id = d.role_id AND d.role_auth_value != '0'
         INNER JOIN `sys_auth` e ON d.auth_id = e.id
GROUP BY a.user_id;


-- seats_share.adopt_seat_apply definition

CREATE TABLE `adopt_seat_apply`
(
    `id`            int(6)      NOT NULL AUTO_INCREMENT COMMENT '认养ID',
    `seat_id`       int(6)      NOT NULL COMMENT '可认养座椅ID',
    `open_id`       varchar(50) NOT NULL COMMENT '认养人open_id',
    `name`          varchar(20) NOT NULL COMMENT '姓名',
    `id_card_no`    varchar(20) NOT NULL COMMENT '身份证号',
    `sex`           int(1)               DEFAULT NULL COMMENT '性别',
    `age`           int(3)               DEFAULT NULL COMMENT '年龄',
    `tel`           varchar(20)          DEFAULT NULL COMMENT '手机号',
    `province`      varchar(10)          DEFAULT NULL COMMENT '所在省',
    `city`          varchar(10)          DEFAULT NULL COMMENT '所在城市',
    `strict`        varchar(10)          DEFAULT NULL COMMENT '所在区',
    `street`        varchar(20)          DEFAULT NULL COMMENT '所在街道',
    `reason`        varchar(500)         DEFAULT NULL COMMENT '认养公共座椅原因',
    `channel`       int(2)               DEFAULT NULL COMMENT '了解渠道 1:',
    `donation_word` varchar(500)         DEFAULT NULL COMMENT '认养寄语',
    `valid_time`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '认养有效期（默认一年）',
    `status`        int(1)      NOT NULL DEFAULT '0' COMMENT '认养状态 1:申请中 2:审核通过 3:已过期',
    `created_at`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `updated_by`    varchar(50) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3
  DEFAULT CHARSET = utf8 COMMENT ='座椅认养反馈表';


-- seats_share.adopt_seat_info definition

CREATE TABLE `adopt_seat_info`
(
    `id`           int(6)         NOT NULL AUTO_INCREMENT COMMENT '可认养座椅ID',
    `seat_id`      int(6)         NOT NULL COMMENT '座椅ID',
    `province`     varchar(10)             DEFAULT NULL COMMENT '所在省',
    `city`         varchar(10)             DEFAULT NULL COMMENT '所在城市',
    `strict`       varchar(10)             DEFAULT NULL COMMENT '所在区',
    `street`       varchar(10)             DEFAULT NULL COMMENT '所在街道',
    `adress`       varchar(200)            DEFAULT NULL COMMENT '详细地址',
    `gps_lng`      double(30, 20) NOT NULL COMMENT 'GPS经度',
    `gps_lat`      double(30, 20) NOT NULL COMMENT 'GPS维度',
    `gps_lng_bmap` double(30, 20) NOT NULL COMMENT 'GPS经度-baidu',
    `gps_lat_bmap` double(30, 20) NOT NULL COMMENT 'GPS维度-baidu',
    `status`       int(1)         NOT NULL DEFAULT '-1' COMMENT '捐赠状态 1:未发布 2:已发布(待认养) 3:被认养',
    `created_at`   timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `updated_by`   varchar(50)    NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 7
  DEFAULT CHARSET = utf8 COMMENT ='座椅认养信息表';


-- seats_share.adopt_upkeep definition

CREATE TABLE `adopt_upkeep`
(
    `id`                  int(6)         NOT NULL AUTO_INCREMENT COMMENT '维护ID',
    `seat_id`             int(6)         NOT NULL COMMENT '认养ID',
    `open_id`             varchar(50)    NOT NULL COMMENT '认养人open_id',
    `upkeep_time`         timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '维护时间',
    `upkeep_image_before` varchar(200)            DEFAULT NULL COMMENT '维护前图片',
    `upkeep_image_after`  varchar(200)            DEFAULT NULL COMMENT '维护后图片',
    `memo`                varchar(200)            DEFAULT NULL COMMENT '维护备注',
    `province`            varchar(10)             DEFAULT NULL COMMENT '所在省',
    `city`                varchar(10)             DEFAULT NULL COMMENT '所在城市',
    `strict`              varchar(10)             DEFAULT NULL COMMENT '所在区',
    `street`              varchar(10)             DEFAULT NULL COMMENT '所在街道',
    `adress`              varchar(200)            DEFAULT NULL COMMENT '详细地址',
    `gps_lng`             double(30, 20) NOT NULL COMMENT 'GPS经度',
    `gps_lat`             double(30, 20) NOT NULL COMMENT 'GPS维度',
    `gps_lng_bmap`        double(30, 20) NOT NULL COMMENT 'GPS经度-baidu',
    `gps_lat_bmap`        double(30, 20) NOT NULL COMMENT 'GPS维度-baidu',
    `created_at`          timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`          timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `updated_by`          varchar(50)    NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='座椅认养维护表';


-- seats_share.donate_seat_apply definition

CREATE TABLE `donate_seat_apply`
(
    `id`                         int(6)      NOT NULL AUTO_INCREMENT COMMENT '捐赠ID',
    `donate_id`                  int(6)      NOT NULL COMMENT '可捐赠座椅ID',
    `open_id`                    varchar(50) NOT NULL COMMENT '捐赠人open_id',
    `share_num`                  int(3)      NOT NULL COMMENT '捐赠份额（1-100）',
    `name`                       varchar(20) NOT NULL COMMENT '姓名',
    `id_card_no`                 varchar(20) NOT NULL COMMENT '身份证号',
    `sex`                        int(1)               DEFAULT NULL COMMENT '性别',
    `age`                        int(3)               DEFAULT NULL COMMENT '年龄',
    `tel`                        varchar(20)          DEFAULT NULL COMMENT '手机号',
    `province`                   varchar(10)          DEFAULT NULL COMMENT '所在省',
    `city`                       varchar(10)          DEFAULT NULL COMMENT '所在城市',
    `strict`                     varchar(10)          DEFAULT NULL COMMENT '所在区',
    `street`                     varchar(20)          DEFAULT NULL COMMENT '所在街道',
    `type`                       int(1)      NOT NULL DEFAULT '-1' COMMENT '捐赠属性 1:街道 2:个人',
    `person_certificate_image`   varchar(200)         DEFAULT NULL COMMENT '身份证明图片URL',
    `aptitude_certificate_image` varchar(200)         DEFAULT NULL COMMENT '资质证明图片URL',
    `channel`                    varchar(100)         DEFAULT NULL COMMENT '了解渠道 1:',
    `reason`                     varchar(500)         DEFAULT NULL COMMENT '捐赠公共座椅原因',
    `donation_word`              varchar(500)         DEFAULT NULL COMMENT '寄语',
    `status`                     int(1)      NOT NULL DEFAULT '-1' COMMENT '捐赠状态 1:申请中 2:捐赠审核通过 3:不通过 4:无效',
    `created_at`                 timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`                 timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `updated_by`                 varchar(50) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3
  DEFAULT CHARSET = utf8 COMMENT ='座椅捐赠反馈表';


-- seats_share.donate_seat_info definition

CREATE TABLE `donate_seat_info`
(
    `id`           int(6)         NOT NULL AUTO_INCREMENT COMMENT '可捐赠座椅ID',
    `donate_no`    varchar(20)    NOT NULL COMMENT '捐赠/认养座椅NO',
    `seat_id`      int(6)         NOT NULL COMMENT '座椅ID',
    `province`     varchar(10)             DEFAULT NULL COMMENT '所在省',
    `city`         varchar(10)             DEFAULT NULL COMMENT '所在城市',
    `strict`       varchar(10)             DEFAULT NULL COMMENT '所在区',
    `street`       varchar(10)             DEFAULT NULL COMMENT '所在街道',
    `adress`       varchar(200)            DEFAULT NULL COMMENT '详细地址',
    `gps_lng`      double(30, 20) NOT NULL COMMENT 'GPS经度',
    `gps_lat`      double(30, 20) NOT NULL COMMENT 'GPS维度',
    `gps_lng_bmap` double(30, 20) NOT NULL COMMENT 'GPS经度-baidu',
    `gps_lat_bmap` double(30, 20) NOT NULL COMMENT 'GPS维度-baidu',
    `valid_time`   timestamp      NOT NULL COMMENT '认捐截止时间（到达截止时间 且 认捐份额>=100 ）',
    `status`       int(1)         NOT NULL DEFAULT '-1' COMMENT '捐赠状态 0:已取消 1:未发布 2:已发布 3:捐赠中 4:捐赠完成 5:建造中 6:建造完成',
    `created_at`   timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `updated_by`   varchar(50)    NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  DEFAULT CHARSET = utf8 COMMENT ='座椅捐赠信息表';


-- seats_share.seat_image definition

CREATE TABLE `seat_image`
(
    `id`         int(6)       NOT NULL AUTO_INCREMENT COMMENT '投票ID',
    `seat_id`    int(6)       NOT NULL COMMENT '座椅ID',
    `image`      varchar(200) NOT NULL COMMENT '座椅图片',
    `remarks`    varchar(200) NOT NULL COMMENT '图片描述',
    `created_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `updated_by` varchar(50)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 7
  DEFAULT CHARSET = utf8 COMMENT ='座椅图片';


-- seats_share.seat_info definition

CREATE TABLE `seat_info`
(
    `id`         int(6)       NOT NULL AUTO_INCREMENT COMMENT '座椅ID',
    `seat_no`    varchar(20)           DEFAULT NULL COMMENT '座椅编号',
    `name`       varchar(100) NOT NULL COMMENT '座椅名称',
    `size`       varchar(20)           DEFAULT NULL COMMENT '座椅大小',
    `material`   varchar(20)           DEFAULT NULL COMMENT '座椅材质',
    `image`      varchar(200)          DEFAULT NULL COMMENT '座椅主图URL',
    `introduce`  varchar(500)          DEFAULT NULL COMMENT '座椅介绍',
    `status`     int(1)       NOT NULL DEFAULT '-1' COMMENT '座椅状态 0:未发布 1:发布 2:下架',
    `price`      int(11)      NOT NULL COMMENT '座椅价格 单位(分)',
    `start_time` timestamp    NULL     DEFAULT NULL COMMENT '发布开始时间',
    `end_time`   timestamp    NULL     DEFAULT NULL COMMENT '发布结束时间',
    `created_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `updated_by` varchar(50)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 7
  DEFAULT CHARSET = utf8 COMMENT ='座椅信息表';


-- seats_share.vote_info definition

CREATE TABLE `vote_info`
(
    `id`          int(6)       NOT NULL AUTO_INCREMENT COMMENT '投票ID',
    `title`       varchar(500) NOT NULL COMMENT '投票名称',
    `title_image` mediumtext            DEFAULT NULL COMMENT '投票标题图片',
    `status`      int(1)       NOT NULL DEFAULT '-1' COMMENT '投票状态 -1:编辑中  0:未发布 1:已发布 2:已结束 9:已删除',
    `start_time`  timestamp    NULL     DEFAULT NULL COMMENT '投票开始时间',
    `end_time`    timestamp    NULL     DEFAULT NULL COMMENT '投票结束时间',
    `introduce`   varchar(500)          DEFAULT NULL COMMENT '投票介绍',
    `created_at`  timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `updated_by`  varchar(50)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 3
  DEFAULT CHARSET = utf8 COMMENT ='投票问卷信息';


-- seats_share.vote_question definition

CREATE TABLE `vote_question`
(
    `id`            int(6)       NOT NULL AUTO_INCREMENT COMMENT '投票问题ID',
    `vote_id`       int(6)       NOT NULL COMMENT '投票ID',
    `question_no`   varchar(5)   NOT NULL COMMENT '投票编号(表示用)',
    `uuid`          varchar(64)           DEFAULT NULL COMMENT '唯一标识UUID',
    `type`          varchar(10)  NOT NULL COMMENT '问题种类 1-单选 2-多选',
    `title`         varchar(500) NOT NULL COMMENT '投票问题名称',
    `title_css`     varchar(20)           DEFAULT NULL COMMENT '问题样式',
    `title_image`   mediumtext COMMENT '问题图片',
    `title_image2`  mediumtext COMMENT '问题图片2',
    `title_image3`  mediumtext COMMENT '问题图片3',
    `details`       varchar(100)          DEFAULT NULL COMMENT '描述',
    `display_order` int(2)       NOT NULL COMMENT '题目表示顺序',
    `display`       int(1)       NOT NULL DEFAULT '1' COMMENT '是否表示 0-非表示 1-表示',
    `created_at`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `updated_by`    varchar(50)  NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='投票问题';


-- seats_share.vote_question_options definition

CREATE TABLE `vote_question_options`
(
    `id`          int(6)      NOT NULL AUTO_INCREMENT COMMENT '投票问题ID',
    `vote_id`     int(6)      NOT NULL COMMENT '投票ID',
    `question_id` int(5)      NOT NULL COMMENT '投票问题ID',
    `answer_id`   int(2)      NOT NULL COMMENT '投票选项ID',
    `text`        varchar(200)         DEFAULT NULL COMMENT '投票选项内容',
    `image`       mediumtext           DEFAULT NULL COMMENT '投票选项图片',
    `created_at`  timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `updated_by`  varchar(50) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='投票问题选项';


-- seats_share.vote_response_details definition

CREATE TABLE `vote_response_details`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT COMMENT '投票回答详情ID',
    `vote_id`     int(6)      NOT NULL COMMENT '投票反馈ID',
    `question_id` int(5)      NOT NULL COMMENT '题目ID',
    `type`        varchar(10) NOT NULL COMMENT '问题种类',
    `user_option` int(2)      NOT NULL COMMENT '用户回答_选项',
    `created_at`  timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by`  varchar(50) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='投票反馈详细';


-- seats_share.vote_response_info definition

CREATE TABLE `vote_response_info`
(
    `id`         int(8)      NOT NULL AUTO_INCREMENT COMMENT '投票反馈ID',
    `vote_id`    int(6)      NOT NULL COMMENT '问卷ID',
    `open_id`    varchar(50) NOT NULL COMMENT '用户openId',
    `start_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '投票填写开始时间',
    `end_time`   timestamp   NULL     DEFAULT NULL COMMENT '投票填写结束时间',
    `value_flg`  int(1)               DEFAULT NULL COMMENT '是否有效 0-无效 1-有效',
    `created_at` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `updated_by` varchar(50) NOT NULL DEFAULT 'System' COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='投票反馈信息';


-- seats_share.wx_user_info definition

CREATE TABLE `wx_user_info`
(
    `id`         int(10)     NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `open_id`    varchar(50) NOT NULL COMMENT '微信ID',
    `name`       varchar(50)      DEFAULT NULL COMMENT '微信昵名',
    `sex`        int(1)           DEFAULT NULL COMMENT '性别',
    `age`        int(3)           DEFAULT NULL COMMENT '年龄',
    `avatar_url` varchar(200)     DEFAULT NULL COMMENT '微信头像',
    `tel`        varchar(20)      DEFAULT NULL COMMENT '手机号',
    `province`   varchar(10)      DEFAULT NULL COMMENT '所在省',
    `city`       varchar(10)      DEFAULT NULL COMMENT '所在城市',
    `strict`     varchar(10)      DEFAULT NULL COMMENT '所在区',
    `street`     varchar(20)      DEFAULT NULL COMMENT '所在街道',
    `send_flg`   int(1)           DEFAULT NULL COMMENT '是否可以推送',
    `wx_status`  int(1)           DEFAULT NULL COMMENT '用户状态',
    `updated_by` varchar(50)      DEFAULT NULL COMMENT '更新者',
    `updated_at` timestamp   NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_at` timestamp   NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='微信用户表';
