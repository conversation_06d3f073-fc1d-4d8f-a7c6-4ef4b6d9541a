const Utils = {

  isEmpty: function(text) {
    return !text || text.trim().length === 0
  },
  guid: function() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      // const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8)
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  },
  uuid: function() {
    const s = []
    const hexDigits = '0123456789abcdef'
    for (let i = 0; i < 36; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
    }
    s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23] = '-'
    return s.join('')
  },

  /**
   * @param {string} url
   * @returns {Object}
   */
  param2Obj: function(url) {
    const search = url.split('?')[1]
    if (!search) {
      return {}
    }
    return JSON.parse(
      '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"')
        .replace(/\+/g, ' ') +
      '"}'
    )
  },
  sort: function(column, mapping) {
    if (column === null || !column.order || column.order === 'null') return ''
    let prop = column.property
    if (mapping && mapping[column.property]) {
      prop = mapping[column.property]
    }
    return prop + ':' + column.order
  },
  formatTimestamp: function(timestamp) {
    function f(t) {
      const date = new Date(t * 1000)
      return date.toLocaleTimeString()
    }
    return f(timestamp)
  },
  formatDate: function(date, fmt) {
    const o = {
      'M+': date.getMonth() + 1, // 月份
      'd+': date.getDate(), // 日
      'h+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds(),
      'q+': Math.floor((date.getMonth() + 3) / 3),
      S: date.getMilliseconds()
    }
    if (/(y+)/.test(fmt)) { fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length)) }
    for (const k in o) {
      if (new RegExp('(' + k + ')').test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
      }
    }
    return fmt
  },
  fillFile: function(param, formData, file) {
    if (file) {
      formData.append(param, {
        type: '',
        files: [file]
      })
    }
  },
  keepFourDecimal: function(num) {
    const result = parseFloat(num)
    if (isNaN(result)) {
      return 0
    }
    return Math.round(num * 100000000000) / 100000000000
  },
  download: function(response) {
    const contentType = response.headers['content-type']
    const ct = contentType.split(';')
    const fileType = ct[0] || null
    let fileName = ct[1].split('=')[1]
    const blob = new Blob([response.data], {
      type: fileType
    })

    fileName = decodeURI(fileName)
    if (window.navigator.msSaveOrOpenBlob) {
      navigator.msSaveBlob(blob, fileName)
    } else {
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = fileName
      link.click()
      window.URL.revokeObjectURL(link.href)
    }
  },
  utf8ArrayToString: function(array) {
    let out = ''
    let char2, char3, c
    const len = array.length
    let i = 0
    while (i < len) {
      c = array[i++]
      switch (c >> 4) {
        case 0: case 1: case 2: case 3: case 4: case 5: case 6: case 7:
          // 0xxxxxxx
          out += String.fromCharCode(c)
          break
        case 12: case 13:
          // 110x xxxx   10xx xxxx
          char2 = array[i++]
          out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F))
          break
        case 14:
          // 1110 xxxx  10xx xxxx  10xx xxxx
          char2 = array[i++]
          char3 = array[i++]
          out += String.fromCharCode(((c & 0x0F) << 12) |
          ((char2 & 0x3F) << 6) |
          ((char3 & 0x3F) << 0))
          break
      }
    }
    return out
  },
  formData: function(data) {
    function process(fd, key, object) {
      const keys = Object.keys(object)
      const pkey = key ? key + '.' : ''
      keys.forEach(k => {
        const value = object[k]
        if (Array.isArray(value)) {
          value.forEach((e, index) => {
            if (e instanceof Object) {
              process(fd, pkey + k + '[' + index + ']', e)
            } else {
              fd.append(pkey + k + '[' + index + ']', e)
            }
          })
        } else if (value instanceof Object) {
          const fileType = Object.prototype.toString.call(value)
          if (fileType === '[object File]' || fileType === '[object Blob]') {
            fd.append(pkey + k, value)
          } else {
            process(fd, pkey + k, value)
          }
        } else {
          if (value != null) {
            fd.append(pkey + k, value)
          }
        }
      })
    }
    const fd = new FormData()
    process(fd, null, data)
    return fd
  },
  swapArray: function(arr, index1, index2) {
    arr[index1] = arr.splice(index2, 1, arr[index1])[0]
    return arr
  },
  clone: function(json) {
    if (json instanceof Array) {
      const targetArray = []
      json.forEach(e => {
        targetArray.push(JSON.parse(JSON.stringify(e)))
      })
      return targetArray
    }
    return JSON.parse(JSON.stringify(json))
  },
  getAreaText(self, val) {
    function find(t, options, index, length) {
      if (options === null || options === undefined) {
        return
      }
      options.forEach(o => {
        if (String(o.value) === String(val[index])) {
          t.push(o.label)
          if (index + 1 < length) {
            find(t, o.children, index + 1, length)
          }
        }
      })
    }
    const t = []
    find(t, self.$area, 0, val.length)
    return t.join(' / ')
  },
  isScaleType(type) {
    return type === 'scaleRating' || type === 'npsScaleRating'
  },
  on: (function() {
    if (document.addEventListener) {
      return function(element, event, handler) {
        if (element && event && handler) {
          element.addEventListener(event, handler, false)
        }
      }
    } else {
      return function(element, event, handler) {
        if (element && event && handler) {
          element.attachEvent('on' + event, handler)
        }
      }
    }
  })(),
  off: (function() {
    if (document.removeEventListener) {
      return function(element, event, handler) {
        if (element && event) {
          element.removeEventListener(event, handler, false)
        }
      }
    } else {
      return function(element, event, handler) {
        if (element && event) {
          element.detachEvent('on' + event, handler)
        }
      }
    }
  })()
}

export default Utils
