export default {
    //腾讯地图加载
    init: function (){
      const AK = "Z4TBZ-5V73U-CNQV5-BCPTB-THZFK-M7FK7";
      const TMap_URL = "https://map.qq.com/api/gljs?v=1.exp&key="+ AK +"&callback=onMapCallback";
      return new Promise((resolve, reject) => {
        // 如果已加载直接返回
        if(typeof TMap !== "undefined") {
          resolve(TMap);
          return true;
        }
        // 地图异步加载回调处理
        window.onMapCallback = function () {
            console.log(TMap)
          resolve(TMap);
        };
  
        // 插入script脚本
        let scriptNode = document.createElement("script");
        scriptNode.setAttribute("type", "text/javascript");
        scriptNode.setAttribute("src", TMap_URL);
        document.body.appendChild(scriptNode);
      });
    }
  }  