//to reset element-ui default css
.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.el-main {
  padding: 10px;
}

//https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  .el-dialog__header {
    height: 45px;
    line-height: 43px;
    padding-top: 4px;
    background: #f5f7fa;
    box-shadow: 0 1px 1px rgba(0,21,41,.08);
  }
  .el-dialog__headerbtn {
    padding-top: 8px;
    top: 8px;
  }
  .el-dialog__title {
    font-size: 16px;
  }
  .el-dialog__body {
    padding: 20px 20px;
    color: #606266;
    font-size: 12px;
  }
}

.dialog__width_1000 {
  width: 1000px;
}
.dialog__width_950 {
  width: 950px;
}
.dialog__width_900 {
  width: 900px;
}
.dialog__width_850 {
  width: 850px;
}
.dialog__width_800 {
  width: 800px;
}
.dialog__width_700 {
  width: 700px;
}
.dialog__width_650 {
  width: 650px;
}
.dialog__width_600 {
  width: 600px;
}
.dialog__width_550 {
  width: 550px;
}
.dialog__width_500 {
  width: 500px;
}

.el-message-box {
  .el-message-box__header {
    background: #f5f7fa;
    box-shadow: 0 1px 1px rgba(0,21,41,.08);
  }
  .el-message-box__message{
    word-break:break-all;
    word-wrap:break-word;
  }
}

//element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

.el-pagination .el-select .el-input {
  width: 123px;
}

.has-gutter {
  background-color: #1f2d3d;
}

@media screen and (min-height: 901px) {
  .iot-table-size {
    height: 534px;
  }
}

@media screen and (max-height: 900px) {
  .iot-table-size {
    height: 400px;
  }
}

.div-group {
  border-radius:5px;
  padding: 5px 5px;
  margin-bottom: 15px;
  border: 1px solid #cbcdd3;
}
.div-group-title{
  display: block;
  /*font-weight: bolder;*/
  width: 130px;
  position: relative;
  top: -15px;
  text-align: center;
  background: white;
}
.div-group-title-auto-width{
  display:inline-block;
  /*font-weight: bolder;*/
  width: auto;
  position: relative;
  top: -15px;
  background: white;
  padding-left:5px;
  padding-right:5px;
}

.el-container {
  flex-direction: column !important;
}

.el-table {
  border: 1px solid #d9dbe1;
}

.el-table .el-table__inner{
  border: none !important;
  td {
    border-bottom: 1px dashed #EBEEF5 !important;
  }
}

.el-table--fit {
  border: 1px solid #d9dbe1 !important;
}

.el-table th {
  background-color: #FDFDFD;
  line-height: 26px;
  background-size: cover;
}

.el-table__fixed-right-patch {
  background-color: #FDFDFD;
  background-size: cover;
}

.el-table__expand-column {
  width: 50px !important;
}
.el-table__expanded-cell[class*=cell] {
  padding: 0px 0px 0px 50px;
}

.input-custom-width {
  width: 200px;
}

.input-custom-width__250 {
  width: 250px;
}

.input-custom-width__300 {
  width: 300px;
}

.input-custom-width__400 {
  width: 400px;
}

.input-custom-width__500 {
  width: 500px;
}

.input-custom-width__600 {
  width: 600px;
}

.el-input.is-disabled .el-input__inner {
  cursor: text !important;
}

.el-form__table-inner .el-form-item {
  margin-bottom: 2px !important;
}

.el-tooltip__popper {
  max-width: 800px !important;
}

.el-button--input-clipboard {
  background-color: #FFF !important;
}

.el-button--input-clipboard:hover {
  background-color: transparent !important;
}

.el-button--input-clipboard:active {
  background-color: #FFF !important;
}

.el-form-item__no-error {
  margin-bottom: 5px !important;
}

.el-card {
  border: 1px solid #dddddd !important;
}
.el-card__header {
  padding: 10px 10px;
  border-bottom: 1px solid #dddddd !important;
}

.el-table thead.is-group th {
  background: #FDFDFD !important;
}

.el-select-dropdown {
  max-width: 480px !important;
}
.el-collapse-item__content {
  padding-bottom: 10px !important;
}
