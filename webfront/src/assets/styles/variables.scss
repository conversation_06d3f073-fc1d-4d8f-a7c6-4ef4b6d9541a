//sidebar
$menuText: #3a5779;
$menuActiveText:#409EFF;
$subMenuActiveText:#409EFF; //https://github.com/ElemeFE/element/issues/12951

$menuHeadBg: #b5c6d4;

$menuBg: #e1e9ef;
$menuHover: #d5e4f1;

$subMenuBg: aliceblue;
$subMenuHover: #d5e4f1;

$sideBarWidth: 220px;

// the :export directive is the magic sauce for webpack
:export {
  menuHeadBg: $menuHeadBg;
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
