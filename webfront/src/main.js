import Vue from 'vue'

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/zh-CN' // lang i18n

import '@/assets/styles/index.scss' // global css
import '@/assets/iconfont/iconfont.css'
import '@/common/icons' // icon
import '@/common/router/permission'
import '@/common/directive'
import '@/components/Control'
import '@/components'
import Utils from '@/utils/index'
// import BaiduMap from 'vue-baidu-map'


let echarts = require("echarts")

import App from './App.vue'

import router from '@/common/router'
import store from '@/common/store'

Vue.use(ElementUI, { locale: locale, size: 'small' })

Vue.config.productionTip = false
Vue.prototype.$utils = Utils
Vue.prototype.$ak = '2KLKHUZWsaYfbYXQZe2QFdbc1kGHtUkW'
Vue.prototype.$echarts = echarts
// Vue.use(BaiduMap, {
//   ak: '2KLKHUZWsaYfbYXQZe2QFdbc1kGHtUkW'
// })

new Vue({
  router,
  store,
  beforeCreate(){
    //全局事件总线
    Vue.prototype.$bus = this
  },
  render: h => h(App)
}).$mount('#app')
