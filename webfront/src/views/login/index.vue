<template>
  <div class="login-container">
    <div class="p-login">
      <h1 class="p-login__logo">
        随坐后台管理系统
      </h1>
      <div class="c-card p-card-login">
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules">
          <div class="p-form-group">
            <label class="p-label-block">用户名</label>
            <el-form-item prop="username">
              <el-input
                id="userid"
                v-model="loginForm.username"
                :clearable="false"
                prefix-icon="el-icon-user"
                class="u-w280"
                size="medium"
                @change="loginForm.username = loginForm.username.trim()"/>
            </el-form-item>
          </div>
          <div class="p-form-group u-mt10" style="margin-top: 20px">
            <label class="p-label-block">密码</label>
            <el-form-item prop="password">
              <el-input
                id="password"
                v-model="loginForm.password"
                :clearable="false"
                show-password
                prefix-icon="el-icon-lock"
                size="medium"
                class="u-w280"
                type="password"
                name="password"
                @change="loginForm.password = loginForm.password.trim()"/>
            </el-form-item>
          </div>
          <div class="p-btn-login-div">
            <el-button class="p-btn-login" size="medium" @click="handleLogin">登录</el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { isEmpty } from '@/utils/validate'

export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (isEmpty(value)) {
        callback(new Error('请输入用户名'))
      } else {
        callback()
      }
    }
    const validatePass = (rule, value, callback) => {
      if (isEmpty(value)) {
        callback(new Error('请输入密码'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePass }]
      },
      loading: false,
      pwdType: 'password',
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  methods: {
    showPwd() {
      if (this.pwdType === 'password') {
        this.pwdType = ''
      } else {
        this.pwdType = 'password'
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store.dispatch('Login', this.loginForm).then((user) => {
            this.loading = false
            this.$router.push({ path: this.redirect || '/' })
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

.login-container {
  display: flex;
  justify-content: center;
  min-height: 100vh;

  height: 100%;
  width: 100%;
  overflow-y: hidden;
  background: url(../../assets/image/bg_login.png) repeat center top/cover;
  background-size: cover;

  .p-login {
    margin-top: 136px;
  }

  .p-login__logo {
    margin-bottom: 28px;
    text-align: center;
  }

  .c-card {
    border-radius: 5px;
    background: #fff;
  }

  .p-card-login {
    padding: 35px 40px 45px;
  }

  .p-label-block {
    display: block;
    margin-bottom: 2px;
    font-size: 12px;
    font-weight: bold;
  }

  .u-w280 {
    width: 280px !important;
  }

  .p-btn-login-div {
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .p-btn-login {
    width: 160px;
    padding-top: 11px;
    padding-bottom: 11px;
  }

  .p-btn-login {
    border-color: #009494;
    background-color: #009494;
    color: #fff;
  }

  .p-btn-login:disabled {
    border-color: #bbbbbb;
    background-color: #bbbbbb;
    color: #fff;
    pointer-events: none;
  }

}
</style>
