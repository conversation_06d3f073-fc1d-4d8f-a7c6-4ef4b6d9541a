<template>
    <el-container>
        <el-panel-header show-back @goBack="handleBack" />
        <el-main v-loading="loading">
            <el-form ref="form" :model="form" :rules="validRules" label-width="100px">
                <el-card shadow="never" style="width: 1000px">
                    <div slot="header" class="clearfix">
                        <span>基本信息</span>
                    </div>
                    <div>
                        <el-row>
                            <el-col :span="16">
                                <el-form-item prop="telephone" label="手机号">
                                    <el-input v-model="form.telephone">
                                    </el-input>
                                </el-form-item>
                                <el-form-item prop="name" label="姓名">
                                    <el-input v-model="form.name">
                                    </el-input>
                                </el-form-item>
                                <el-form-item prop="company" label="单位">
                                    <el-input v-model="form.company">
                                    </el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </el-card>
                <el-card shadow="never" style="width: 1000px; margin-top: 10px">
                    <div style="text-align: right">
                        <el-loading-button type="" style="width: 100px" @click="handleBack">取消</el-loading-button>
                        <el-loading-button style="width: 100px" @click="handleSave">保存</el-loading-button>
                    </div>
                </el-card>
            </el-form>
        </el-main>
    </el-container>
</template>

<script>
import { add_upkeep_user } from "@/api/import"
export default {
    methods: {
        handleBack() {
            this.$router.go(-1)
        },
        handleSave(done) {
            this.loading = true;
            add_upkeep_user(this.form).then(() => {
                this.$message({
                    type: 'success',
                    message: '添加成功!'
                })
                done();
                this.handleBack()
            }).catch(() => { }).finally(() => {
                done();
                this.loading = false;
            })
        }
    },
    data() {
        return {
            loading: false,
            form: {
                telephone: '',
                name: "",
                company: ""
            },
            validRules: {
                telephone: [
                    { required: true, trigger: 'blur', message: '手机号必须输入' },
                    { pattern: /^1\d{10}$/, trigger: 'blur', message: '请输入正确的手机号' }],
                name: [{ required: true, trigger: 'blur', message: '姓名必须输入' }],
            },

        }
    }
}
</script>

<style lang="scss" scoped></style>
