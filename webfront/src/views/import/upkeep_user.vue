<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-row>
          <el-col :span="6">
            <el-form-item prop="telephone" label="手机号">
              <el-input v-model="form.telephone" style="width: 150px" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">

      <div class="line">
        <el-upload :action="'/rest/import/user'" :show-file-list="false" class="upload-demo" accept=".xlsx,.xls"
          :before-upload="beforeUpload" :on-success="handleSuccess" :on-error="handleError">
          <el-button size="small" type="success">Excel导入</el-button>
        </el-upload>
        <el-button style="margin-left: 10px" size="small" type="success" @click="handleAdd">新增</el-button>
      </div>
      <el-pagination-table :data="dataList" :pagination.sync="pagination" :sort-column.sync="sortColumn"
        :search-function="handleSearch" :search-loading.sync="loading" style="margin-top: 10px">
        <!-- <el-table-column type="selection" width="45" /> -->
        <el-table-column prop="telephone" label="手机号" width="160" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="company" label="养护单位" />
        <el-table-column label="操作" fixed="right" width="200px">
          <template slot-scope="scope">
            <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-pagination-table>
    </el-main>
  </el-container>
</template>

<script>
import { list_init_api ,delete_upkeep_user } from '@/api/import'
export default {
  name: 'ImportUpkeepUser',
  data() {
    return {
      loading: false,
      form: {
        telephone: '',
        file: null
      },
      dataList: [],
      sortColumn: {},
      sortMapping: {
      },
      pagination: {
        pageNumber: 1
      }
    }
  },
  created() {
    this.handleSearch()
  },
  methods: {
    handleSearch() {
      this.loading = true
      list_init_api(this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        this.dataList = res.value
        this.pagination = res.pagination
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
    },
    handleChange() {
    },
    beforeUpload() {
      this.loading = true
    },
    handleSuccess() {
      this.loading = false
      const self = this
      self.$message.success('养护人员导入成功。')
      self.handleSearch()
    },
    handleError() {
      this.loading = false
      const self = this
      self.$message.warning('养护人员导入失败。')
    },
    handleAdd(){
      //新增人员
      this.$router.push('/import/user/add')
    },
    handleDelete(index, row) {
      const self = this
      this.$confirm(`是否删除该养护人员【${row.name}】`).then(res => {
        delete_upkeep_user(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
  }
}
</script>

<style lang="scss">
.line {
  display: flex;
}
</style>
