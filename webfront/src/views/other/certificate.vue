<template>
    <el-container>
        <el-panel-header show-back @goBack="handleBack" />
        <div class="btn">
            <el-button type="primary" icon="el-icon-refresh-left" @click="refresh">刷新</el-button>
        </div>
        <el-main v-loading="loading">
            <!-- <el-table-toolbar :add-show="true" :delete-show="false" title-show @handleAddItem="handleAdd" /> -->
            <el-pagination-table :data="dataList" :pagination.sync="pagination" :sort-column.sync="sortColumn"
                :search-function="handleSearch" :search-loading.sync="loading">
                <el-table-column prop="id" label="证书编号" width="100" />
                <el-table-column prop="openId" label="用户" width="400" />
                <!-- <el-table-column prop="applyId" label="座椅编号" width="200" /> -->
                <el-table-column prop="seatType" label="座椅类型" width="120">
                    <template slot-scope="scope">
                        <el-tag v-if="pagination.seatType == 1" type="info">捐赠</el-tag>
                        <el-tag v-else-if="pagination.seatType == 2" type="info">认养</el-tag>
                        <el-tag v-else type="danger">未知</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="createdAt" width="200" label="创建时间" />
                <el-table-column prop="manage">
                    <template slot-scope="scope">
                        <el-button size="mini" icon="el-icon-edit" class="mini-btn"
                            @click="handleEdit(scope.$index, scope.row)">详情</el-button>
                        <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn"
                            @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-pagination-table>
        </el-main>
    </el-container>
</template>

<script>
import { certificate_list_api, certificate_delete_api } from "@/api/other"
export default {
    data() {
        return {
            loading: false,
            pagination: {
                openId: "",
                applyId: "",
                seatType: "",
                pageNumber: 1
            },
            sortColumn: {},
            dataList: []
        }
    },
    methods: {
        handleBack() {
            this.$router.go(-1)
        },
        handleAdd() {
            this.$router.push('/other/certificate/add')
        },
        refresh() {
            //刷新列表
            this.handleSearch();
        },
        handleSearch() {
            certificate_list_api(this.pagination).then(res => {
                this.dataList = res.value;
            }).catch(() => { })
        },
        handleDelete(index, row) {
            const self = this
            this.$confirm('是否要删除证书').then(res => {
                certificate_delete_api(row.id).then(res => {
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    })
                    self.handleSearch()
                }).catch(e => {
                })
            }).catch(e => {
            })
        },
        handleEdit(index, row) {
            this.$router.push('/other/certificate/edit/' + row.id)
        },
    },
    mounted() {
        let info = this.$route.query.seatInfo;
        if (info.openId != undefined) {
            sessionStorage.setItem('certificateInfo', JSON.stringify(info))
        } else {
            info =  JSON.parse(sessionStorage.getItem("certificateInfo"));
        }

        this.pagination.openId = info && info.openId;
        this.pagination.applyId = info && info.applyId;
        this.pagination.seatType = info && info.seatType;
        this.refresh()
    }
}
</script>

<style lang="scss" scoped>
.btn {
    padding: 10px;
    display: flex;
    justify-content: flex-end;
}
</style>
