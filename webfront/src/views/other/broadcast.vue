<template>
    <el-container>
        <div class="btn">
            <el-button type="primary" icon="el-icon-refresh-left" @click="refresh">刷新</el-button>
        </div>
        <el-main v-loading="loading">
            <el-table-toolbar :add-show="true" :delete-show="false" title-show @handleAddItem="handleAdd" />
            <el-pagination-table :data="dataList" :pagination.sync="pagination" :sort-column.sync="sortColumn"
                :search-function="handleSearch" :search-loading.sync="loading">
                <el-table-column prop="id" label="广播编号" width="100" />
                <el-table-column prop="content" label="内容" width="500" />
                <el-table-column prop="status" label="状态" width="120">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.status == 0" type="info">{{ scope.row.statusName }}</el-tag>
                        <el-tag v-else-if="scope.row.status == 1" type="success">{{ scope.row.statusName }}</el-tag>
                        <el-tag v-else-if="scope.row.status == 2" type="danger">{{ scope.row.statusName }}</el-tag>
                        <el-tag v-else type="danger">未知</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="createdAt" width="200" label="创建时间" />
                <el-table-column prop="manage">
                    <template slot-scope="scope">
                        <el-button size="mini" icon="el-icon-edit" class="mini-btn"
                            @click="handleEdit(scope.$index, scope.row)">详情</el-button>
                        <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn"
                            @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-pagination-table>
        </el-main>
    </el-container>
</template>

<script>
import { broadcast_list_api, broadcast_delete_api, broadcast_init_api } from "@/api/other"
export default {
    data() {
        return {
            loading: false,
            pagination: {
                pageNumber: 1
            },
            sortColumn: {},
            dataList: []
        }
    },
    methods: {
        handleAdd() {
            this.$router.push('/other/broadcast/add')
        },
        refresh() {
            //刷新列表
            this.handleSearch();
        },
        handleSearch() {
            broadcast_list_api(this.pagination).then(res => {
                console.log(res)
                this.dataList = res.value;
            }).catch(() => { })
        },
        handleDelete(index, row) {
            const self = this
            this.$confirm('是否要删除广播').then(res => {
                broadcast_delete_api(row.id).then(res => {
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    })
                    self.handleSearch()
                }).catch(e => {
                })
            }).catch(e => {
            })
        },
        handleEdit(index, row) {
            this.$router.push('/other/broadcast/edit/' + row.id)
        },
    },
    mounted() {
        this.refresh()
    }
}
</script>

<style lang="scss" scoped>
.btn {
    padding: 10px;
    display: flex;
    justify-content: flex-end;
}
</style>
