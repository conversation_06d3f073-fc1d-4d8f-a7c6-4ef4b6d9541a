<template>
    <el-container>
        <div class="btn">
            <el-button type="primary" icon="el-icon-refresh-left" @click="refresh">刷新</el-button>
        </div>
        <el-main v-loading="loading">
            <el-table-toolbar :add-show="true" :delete-show="false" title-show @handleAddItem="handleAdd" />
            <el-pagination-table :data="dataList" :pagination.sync="pagination" :sort-column.sync="sortColumn"
                :search-function="handleSearch" :search-loading.sync="loading">
                <el-table-column prop="id" label="资讯编号" width="100" />
                <el-table-column prop="linkUrl" label="资讯链接" width="500" />
                <el-table-column prop="createdAt" width="200" label="创建时间" />
                <el-table-column prop="manage">
                    <template slot-scope="scope">
                        <el-button size="mini" icon="el-icon-edit" class="mini-btn"
                            @click="handleEdit(scope.$index, scope.row)">详情</el-button>
                        <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn"
                            @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-pagination-table>
        </el-main>
    </el-container>
</template>

<script>
import { information_list_api,information_delete_api } from "@/api/other"
export default {
    data() {
        return {
            loading: false,
            pagination: {
                pageNumber: 1
            },
            sortColumn: {},
            dataList: []
        }
    },
    methods: {
        handleAdd() {
            this.$router.push('/other/info/add')
        },
        refresh() {
            //刷新列表
            this.handleSearch();
        },
        handleSearch() {
            information_list_api(this.pagination).then(res => {
                this.dataList = res.value;
            }).catch(() => { })
        },
        handleDelete(index, row) {
            const self = this
            this.$confirm('是否要删除资讯').then(res => {
                information_delete_api(row.id).then(res => {
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    })
                    self.handleSearch()
                }).catch(e => {
                })
            }).catch(e => {
            })
        },
        handleEdit(index, row) {
            this.$router.push('/other/info/edit/' + row.id)
        },
    },
    mounted() {
        this.refresh()
    }
}
</script>

<style lang="scss" scoped>
.btn {
    padding: 10px;
    display: flex;
    justify-content: flex-end;
}
</style>
