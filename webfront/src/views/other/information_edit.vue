<template>
    <el-container>
        <el-panel-header show-back @goBack="handleBack" />
        <el-main v-loading="loading">
            <el-form ref="form" :model="form" :rules="validRules" label-width="100px">
                <el-card shadow="never" style="width: 1000px">
                    <div slot="header" class="clearfix">
                        <span>基本信息</span>
                    </div>
                    <div>
                        <el-row>
                            <el-col :span="16">
                                <el-form-item prop="id" label="资讯编号" v-if="isEdit">
                                    <el-input v-model="form.id" disabled>
                                    </el-input>
                                </el-form-item>
                                <el-form-item prop="image" label="资讯图片">
                                    <div>
                                        <el-picture-upload v-model="form.image" :limit="1" />
                                    </div>
                                </el-form-item>
                                <el-form-item prop="linkUrl" label="资讯链接">
                                    <el-input v-model="form.linkUrl" placeholder="请输入资讯链接">
                                    </el-input>
                                </el-form-item>
                                <!-- <el-form-item prop="status" label="资讯状态">
                                    <el-radio-group v-model="form.status">
                                        <el-radio :label="1">显示</el-radio>
                                        <el-radio :label="0">不显示</el-radio>
                                    </el-radio-group>
                                </el-form-item> -->
                            </el-col>
                        </el-row>
                    </div>
                </el-card>
                <el-card shadow="never" style="width: 1000px; margin-top: 10px">
                    <div style="text-align: right">
                        <el-loading-button type="" style="width: 100px" @click="handleBack">取消</el-loading-button>
                        <el-loading-button style="width: 100px" @click="handleSave">保存</el-loading-button>
                    </div>
                </el-card>
            </el-form>
        </el-main>
    </el-container>
</template>
  
<script>
import { information_add_api, information_init_api, information_edit_api } from "@/api/other"
export default {
    data() {
        return {
            form: {
                id: "",
                image: {},
                linkUrl: "",
                status: 1,
            },
            validRules: {
                image: [{ required: true, trigger: 'blur', message: '资讯图片必须上传' }],
                linkUrl: [{ required: true, trigger: 'blur', message: '资讯链接必填' }],
            },
            loading: false,
            infoId: null,//当前编号
            isEdit: false,
        }
    },
    methods: {
        handleBack() {
            this.$router.go(-1)
        },
        handleSave(done) {
            this.$refs.form.validate(valid => {
                if (valid) {
                    //验证图片数量
                    let num = 0;
                    this.form.image && this.form.image.files.forEach(item => {
                        if (item.status != 'remove') {
                            num++;
                        }
                    })
                    if (num == 0) {
                        this.$message({
                            type: 'error',
                            message: '资讯图片必须上传!'
                        })
                        done();
                        return;
                    }

                    this.loading = true

                    if (this.isEdit) {
                        //修改操作
                        information_edit_api(this.form).then(res => {
                            this.$message({
                                type: 'success',
                                message: '修改成功!'
                            })
                        }).catch(e => {
                        }).finally(() => {
                            this.loading = false
                            done()
                        })
                    } else {
                        //新增操作
                        information_add_api(this.form).then(res => {
                            this.$message({
                                type: 'success',
                                message: '新增成功!'
                            })
                            this.handleBack()
                        }).catch(e => {
                        }).finally(() => {
                            this.loading = false
                            done()
                        })
                    }
                } else {
                    done()
                }
            })
        },
        loadInformationInfo(id) {
            information_init_api(id).then(res => {
                this.form = res && res.value;
                this.form.id = this.infoId;
            }).catch(() => { })
        }
    },
    mounted() {
        this.infoId = this.$route.params.key
        if (this.infoId != null) {
            this.isEdit = true;
            this.loadInformationInfo(this.infoId)
        }

    }
}
</script>
  
<style lang="scss" scoped></style>
  