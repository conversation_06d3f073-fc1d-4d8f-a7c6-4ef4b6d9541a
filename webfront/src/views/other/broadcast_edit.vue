<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack" />
    <el-main v-loading="loading">
      <el-form ref="form" :model="form" :rules="validRules" label-width="100px">
        <el-card shadow="never" style="width: 1000px">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <div>
            <el-row>
              <el-col :span="16">
                <el-form-item prop="id" label="广播编号" v-if="isEdit">
                  <el-input v-model="form.id" disabled>
                  </el-input>
                </el-form-item>
                <el-form-item prop="content" label="广播内容">
                  <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" placeholder="请输入内容"
                    v-model="form.content">
                  </el-input>
                </el-form-item>
                <el-form-item prop="status" label="广播状态">
                  <el-radio-group v-model="form.status">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                    <el-radio :label="2">暂停</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="never" style="width: 1000px; margin-top: 10px">
          <div style="text-align: right">
            <el-loading-button type="" style="width: 100px" @click="handleBack">取消</el-loading-button>
            <el-loading-button style="width: 100px" @click="handleSave">保存</el-loading-button>
          </div>
        </el-card>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import { broadcast_add_api, broadcast_init_api, broadcast_edit_api } from "@/api/other"
export default {
  data() {
    return {
      form: {
        content: "",
        status: 1,
      },
      validRules: {
        content: [{ required: true, trigger: 'blur', message: '广播内容必须输入' }],
      },
      loading: false,
      broadcastId: null,//当前编号
      isEdit: false,
    }
  },
  methods: {
    handleBack() {
      this.$router.go(-1)
    },
    handleSave(done) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          if (this.isEdit) {
            //修改操作
            broadcast_edit_api(this.form).then(res => {
              this.$message({
                type: 'success',
                message: '修改成功!'
              })
            }).catch(e => {
              this.$message({
                type: 'error',
                message: '修改失败!'
              })
            }).finally(() => {
              this.loading = false
              done()
            })
          } else {
            //新增操作
            broadcast_add_api(this.form).then(res => {
              this.$message({
                type: 'success',
                message: '添加成功!'
              })
              this.handleBack()
            }).catch(e => {
            }).finally(() => {
              this.loading = false
              done()
            })
          }
        } else {
          done()
        }
      })
    },
    loadBroadcastInfo(id) {
      broadcast_init_api(id).then(res => {
        this.form = res && res.value;
        this.form.id = this.broadcastId;
      }).catch(() => { })
    }
  },
  mounted() {
    this.broadcastId = this.$route.params.key
    if (this.broadcastId != null) {
      this.isEdit = true;
      this.loadBroadcastInfo(this.broadcastId)
    }

  }
}
</script>

<style lang="scss" scoped></style>
