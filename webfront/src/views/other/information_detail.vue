<template>
    <el-container>
        
        <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
        <el-form :model="formSearch">
            <el-row>
                <el-col :span="8">
                    <el-form-item prop="type" label="资讯分类">
                        <el-select v-model="formSearch.type" placeholder="请选择资讯分类">
                            <el-option label="全部" :value="null"></el-option>
                            <el-option label="新闻" value="1"></el-option>
                            <el-option label="公告" value="2"></el-option>
                            <el-option label="活动" value="3"></el-option>
                            </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </el-search-header>

        <el-main v-loading="loading">
            <el-table-toolbar :add-show="true" :delete-show="false" title-show @handleAddItem="handleAdd" />
            <el-pagination-table :data="dataList" :pagination.sync="pagination" :sort-column.sync="sortColumn"
                :search-function="handleSearch" :search-loading.sync="loading">
                <el-table-column prop="id" label="资讯详情编号" width="100" />
                <el-table-column prop="title" label="标题" width="100" />
                <el-table-column prop="typeName" label="分类" width="100" >
                    <template slot-scope="scope">
                        <el-tag>{{scope.row.typeName}}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="content" label="内容" width="400" />
                <el-table-column prop="linkUrl" label="资讯链接" width="300" />
                <el-table-column prop="createdAt" width="200" label="创建时间" />
                <el-table-column prop="manage">
                    <template slot-scope="scope">
                        <el-button size="mini" icon="el-icon-edit" class="mini-btn"
                            @click="handleEdit(scope.$index, scope.row)">详情</el-button>
                        <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn"
                            @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-pagination-table>
        </el-main>
    </el-container>
</template>

<script>
import { information_detail_list_api,information_detail_delete_api } from "@/api/other"
export default {
    data() {
        return {
            loading: false,
            pagination: {
                pageNumber: 1
            },
            sortColumn: {},
            dataList: [],
            formSearch:{
                type:null,
            }
        }
    },
    methods: {
        handleAdd() {
            this.$router.push('/other/info_detail/add')
        },
        refresh() {
            //刷新列表
            this.handleSearch();
        },
        handleSearch() {
            information_detail_list_api(this.formSearch).then(res => {
                this.dataList = res.value;
            }).catch(() => { })
        },
        handleDelete(index, row) {
            const self = this
            this.$confirm('是否要删除资讯详情').then(res => {
                information_detail_delete_api(row.id).then(res => {
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    })
                    self.handleSearch()
                }).catch(e => {
                })
            }).catch(e => {
            })
        },
        handleEdit(index, row) {
            this.$router.push('/other/info_detail/edit/' + row.id)
        },
        resetCondition(){
            this.formSearch.type = null;
            this.refresh();
        }
    },
    mounted() {
        this.refresh()
    }
}
</script>

<style lang="scss" scoped>
.btn {
    padding: 10px;
    display: flex;
    justify-content: flex-end;
}
</style>
