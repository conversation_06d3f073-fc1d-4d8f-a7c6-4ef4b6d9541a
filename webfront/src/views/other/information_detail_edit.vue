<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack" />
    <el-main v-loading="loading">
      <el-form ref="form" :model="form" :rules="validRules" label-width="100px">
        <el-card shadow="never" style="width: 1000px">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <div>
            <el-row>
              <el-col :span="16">
                <el-form-item prop="id" label="资讯详情编号" v-if="isEdit">
                  <el-input v-model="form.id" disabled> </el-input>
                </el-form-item>
                <el-form-item prop="type" label="状态">
                  <el-radio-group v-model="form.type">
                    <el-radio :label="1">新闻</el-radio>
                    <el-radio :label="2">公告</el-radio>
                    <el-radio :label="3">活动</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item prop="title" label="标题">
                  <el-input v-model="form.title" placeholder="请输入标题">
                  </el-input>
                </el-form-item>
                <el-form-item prop="content" label="内容">
                  <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" placeholder="请输入内容"
                    v-model="form.content"></el-input>
                </el-form-item>
                <el-form-item prop="linkUrl" label="链接">
                  <el-input v-model="form.linkUrl" placeholder="请输入资讯链接">
                  </el-input>
                </el-form-item>
                <el-form-item prop="image" label="图片">
                  <div>
                    <el-picture-upload v-model="form.image" :limit="1" />
                  </div>
                </el-form-item>
                <!-- <el-form-item prop="status" label="资讯状态">
                  <el-radio-group v-model="form.status">
                    <el-radio :label="1">显示</el-radio>
                    <el-radio :label="0">不显示</el-radio>
                  </el-radio-group>
                </el-form-item> -->
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="never" style="width: 1000px; margin-top: 10px">
          <div style="text-align: right">
            <el-loading-button type="" style="width: 100px" @click="handleBack">取消</el-loading-button>
            <el-loading-button style="width: 100px" @click="handleSave">保存</el-loading-button>
          </div>
        </el-card>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import {
  information_detail_add_api,
  information_detail_init_api,
  information_detail_edit_api,
} from "@/api/other";
export default {
  data() {
    return {
      form: {
        type: null,
        title: "",
        content: "",
        image: {},
        linkUrl: "",
        status: 1,
      },
      validRules: {
        type: [{ required: true, trigger: "blur", message: "请选择状态" }],
        title: [{ required: true, trigger: "blur", message: "请填写标题" }],
        content: [{ required: true, trigger: "blur", message: "请填写内容" }],
        image: [{ required: true, trigger: 'blur', message: '图片必须上传' }],
        linkUrl: [{ required: true, trigger: "blur", message: "请填写链接" }],
      },
      loading: false,
      infoId: null, //当前编号
      isEdit: false,
    };
  },
  methods: {
    handleBack() {
      this.$router.go(-1);
    },
    handleSave(done) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let num = 0;
          this.form.image && this.form.image.files.forEach(item => {
            if (item.status != 'remove') {
              num++;
            }
          })
          if (num == 0) {
            this.$message({
              type: 'error',
              message: '图片必须上传!'
            })
            done();
            return;
          }
          this.loading = true;
          if (this.isEdit) {
            //修改操作
            information_detail_edit_api(this.form)
              .then((res) => {
                this.$message({
                  type: "success",
                  message: "修改成功!",
                });
              })
              .catch((e) => {
                this.$message({
                  type: "error",
                  message: "修改失败!",
                });
              })
              .finally(() => {
                this.loading = false;
                done();
              });
          } else {
            //新增操作
            information_detail_add_api(this.form)
              .then((res) => {
                this.$message({
                  type: "success",
                  message: "添加成功!",
                });
                this.handleBack();
              })
              .catch((e) => { })
              .finally(() => {
                this.loading = false;
                done();
              });
          }
        } else {
          done();
        }
      });
    },
    loadInformationDetailInfo(id) {
      information_detail_init_api(id)
        .then((res) => {
          this.form = res && res.value;
          this.form.id = this.infoId;
        })
        .catch(() => { });
    },
  },
  mounted() {
    this.infoId = this.$route.params.key;
    if (this.infoId != null) {
      this.isEdit = true;
      this.loadInformationDetailInfo(this.infoId);
    }
  },
};
</script>

<style lang="scss" scoped></style>
