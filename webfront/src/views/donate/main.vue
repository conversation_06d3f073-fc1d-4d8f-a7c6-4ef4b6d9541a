<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-form-item prop="seatNo" label="捐赠编号">
          <el-input v-model="form.donateNo" style="width: 150px"/>
        </el-form-item>
      </el-form>
      <el-upload
        :action="'/rest/import/donate'"
        :show-file-list="false"
        class="upload-demo"
        accept=".xlsx,.xls"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-error="handleError">
        <el-button size="small" type="success">Excel导入</el-button>
      </el-upload>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar
        :delete-show="false"
        :export-show="true"
        :sort-column="sortColumn"
        @handleAddItem="handleAdd"
        @handleDeleteMultiItems="handleMultiDelete"
        :title-text="'捐赠信息'"
        @handleExport="handleExport"/>
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="selection" width="45"/>
        <el-table-column prop="donateNo" label="捐赠No" width="100" sortable="custom"/>
        <el-table-column prop="seatNo" label="座椅编号" width="100" sortable="custom"/>
        <el-table-column prop="seatName" label="座椅名称" width="140" />
        <el-table-column prop="seatPrice" label="价格" width="80"/>
        <el-table-column prop="pcdName" label="省/市/县/街道" width="200"/>
        <el-table-column prop="adress" label="详细地址" width="140"/>
        <el-table-column prop="createdAt" label="发布时间" width="170" sortable="custom"/>
        <el-table-column prop="validTime" label="认捐截止时间" width="170" sortable="custom"/>
        <el-table-column prop="status" label="状态" sortable="custom">
          <template slot-scope="prop">
            <el-tag type="primary">{{ prop.row.statusText }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="" width="500px">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-edit" class="mini-btn" @click="handleEdit(scope.$index, scope.row)">详情</el-button>
<!--            <el-button size="mini" v-if="scope.row.status === 2" type="info" icon="el-icon-star-off" class="mini-btn" @click="handleCancel(scope.$index, scope.row)">取消发布</el-button>-->
            <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            <el-button size="mini" icon="el-icon-info" class="mini-btn" @click="applyInfo(scope.$index, scope.row)">反馈详情</el-button>
            <el-button size="mini" v-if="scope.row.status > 1 " type="primary" icon="el-icon-s-promotion" class="mini-btn" @click="downloadQrCode(scope.$index, scope.row)">二维码</el-button>
            <el-button size="mini" v-if="scope.row.status > 1" icon="el-icon-info" class="mini-btn" @click="commentInfo(scope.$index, scope.row)">评论详情</el-button>
          </template>
        </el-table-column>
      </el-pagination-table>
    </el-main>
  </el-container>
</template>

<script>
import { list_init_api, delete_api, cancel_api, download_qrcode_api, donate_export_api } from '@/api/donate'
export default {
  name: 'DonateMain',
  data() {
    return {
      loading: false,
      form: {
        donateNo: ''
      },
      dataList: [],
      sortColumn: {},
      sortMapping: {
        donateNo: 'di.donate_no',
        seatNo: 'si.seat_no',
        createdAt: 'di.created_at',
        validTime: 'di.valid_time',
        status: 'di.status',
      },
      pagination: {
        pageNumber: 1
      }
    }
  },
  created() {
    this.handleSearch()
  },
  methods: {
    handleSearch() {
      this.loading = true
      list_init_api(this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        this.dataList = res.value
        this.pagination = res.pagination
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
      this.form.donateNo = ''
      this.pagination.pageNumber=0
      this.handleSearch();
    },
    handleAdd() {
      this.$router.push('/seats-donate/add')
    },
    handleEdit(index, row) {
      this.$router.push('/seats-donate/edit/' + row.id)
    },
    applyInfo(index, row) {
      this.$router.push('/seats-donate/applyInfo/' + row.id)
    },
    commentInfo(index, row) {
      this.$router.push('/seats-donate/commentInfo/' + row.id)
    },
    handleMultiDelete() {
    },
    handleDelete(index, row) {
      const self = this
      this.$confirm('是否要删除捐赠信息').then(res => {
        delete_api(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
    handleCancel(index, row) {
      const self = this
      this.$confirm('是否取消发布').then(res => {
        cancel_api(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
    downloadQrCode(index, row) {
      this.loading = true
      download_qrcode_api('1' + row.id).then(res => {
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    async handleExport() {
      this.loading = true
      await donate_export_api(this.form).then(res => {
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    beforeUpload() {
      this.loading = true
    },
    handleSuccess() {
      this.loading = false
      const self = this
      self.$message.success('捐赠信息导入成功。')
      self.handleSearch()
    },
    handleError() {
      this.loading = false
      const self = this
      self.$message.warning('捐赠信息导入失败。')
    },
  }
}
</script>
