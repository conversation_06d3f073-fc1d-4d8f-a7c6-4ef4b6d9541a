<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack"/>
    <el-main v-loading="loading">
      <el-form ref="form" :model="form" :rules="validRules" label-width="140px">
        <el-card shadow="never" style="width: 1000px">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <div>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="donateNo" label="捐赠编号">
                  <el-input v-model="form.donateNo" style="width: 280px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatNo" label="座椅编号">
                  <el-input v-model="form.seatNo" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="seatImage" label="座椅主图">
                  <div>
                    <el-picture-upload v-model="form.seatImage" multiple :limit="1"/>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item prop="seatName" label="座椅名称">
                  <el-input v-model="form.seatName" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="material" label="座椅材质">
                  <el-input v-model="form.seatMaterial" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="seatSize" label="座椅大小">
                  <el-input v-model="form.seatSize" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="seatPrice" label="座椅价格">
                  <el-input v-model="form.seatPrice" type="number" style="width: 200px"/>
                </el-form-item>
              </el-col>
              <el-col>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item prop="seatImageList" label="座椅其它图片">
                  <div>
                    <el-picture-upload v-model="form.seatImageList" multiple :limit="5"/>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="ownerUnit" label="建设单位">
                  <el-input v-model="form.ownerUnit" style="width: 500px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="constructUnit" label="施工单位">
                  <el-input v-model="form.constructUnit" style="width: 500px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="seatIntroduce" label="座椅介绍">
                  <el-input v-model="form.seatIntroduce" type="textarea" style="width: 630px"/>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="8">
                <el-form-item prop="adress" label="详细地址">
                  <el-geo-coordinates
                    :gps-lng.sync="form.gpsLng"
                    :gps-lat.sync="form.gpsLat"
                    :address.sync="form.adress"
                    :province.sync="form.province"
                    :city.sync="form.city"
                    :strict.sync="form.strict"
                    :street.sync="form.street"
                    isDonate
                    style="width: 500px;"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item prop="province" label="省市县街道">
                  {{form.province}}/{{form.city}}/ {{form.strict}}/ {{form.street}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="validTime" label="截止时间">
                  <el-date-picker v-model="form.validTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" default-time="23:59:59"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="status" label="状态">
<!--                  <el-tag type="primary"> {{optType === 'add' ? '做成中' : form.statusText}} </el-tag>-->
                  <el-select v-model="form.status" placeholder="请选择" style="width: 180px">
                    <el-option
                      v-for="item in statusOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="parseInt(item.value)">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="never" style="width: 1000px; margin-top: 10px">
          <div style="text-align: right">
            <el-loading-button type="" style="width: 100px" @click="handleBack">取消</el-loading-button>
            <el-loading-button style="width: 100px" @click="handleSave">保存</el-loading-button>
            <el-loading-button v-if="form.status === 1" style="width: 100px" @click="done => handleSaveStatus(done,2)">发布</el-loading-button>
            <el-loading-button v-if="form.status === 2" type="primary" style="width: 100px" @click="done => handleSaveStatus(done,4)">捐赠完成</el-loading-button>
            <el-loading-button v-if="form.status === 3" type="primary" style="width: 100px" @click="done => handleSaveStatus(done,4)">捐赠完成</el-loading-button>
            <el-loading-button v-if="form.status === 4" type="warning" style="width: 100px" @click="done => handleSaveStatus(done,5)">建造中</el-loading-button>
            <el-loading-button v-if="form.status === 5" type="success" style="width: 100px" @click="done => handleSaveStatus(done,6)">建造完成</el-loading-button>
            <!--            <el-button v-if="Number(scope.row.status) < 1" size="mini" icon="el-icon-edit-outline" class="mini-btn" @click="handleEdit(scope.$index, scope.row)">修改</el-button>-->
            <!--            <el-button v-if="Number(scope.row.status) === 0" type="primary" size="mini" icon="el-icon-position" class="mini-btn" @click="handlePublish(scope.$index, scope.row)">发布</el-button>-->
            <!--            <el-button v-if="Number(scope.row.status) === 1" type="warning" size="mini" class="mini-btn" @click="handleCancelPublish(scope.$index, scope.row)">取消发布</el-button>-->
            <!--            <el-button v-if="Number(scope.row.status) === 1" size="mini" type="success" icon="el-icon-share" class="mini-btn" @click="handleQrCode(scope.$index, scope.row)">二维码</el-button>-->
            <!--            <el-button v-if="Number(scope.row.status) === 1" size="mini" type="info" icon="el-icon-warning-outline" class="mini-btn" @click="handleStop(scope.$index, scope.row)">终止</el-button>-->
            <!--            <el-button v-if="Number(scope.row.status) < 1" size="mini" type="danger" icon="el-icon-delete" class="mini-btn" @click="handleDelete(scope.$index, scope.row)">删除</el-button>-->
          </div>
        </el-card>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import { donate_init_api, donate_save_api, donate_save_status_api } from '@/api/donate'
export default {
  name: 'DonateEdit',
  watch:{
    'form.adress':{
      deep:true,
      handler(newVal){
        this.$refs.form.validateField('adress')
      }
    }
  },
  data() {
    return {
      isEdit: true,
      loading: false,
      optType: null,
      form: {
        id: '',
        donateNo: '',
        seatId: '',
        seatNo: '',
        seatName: '',
        seatMaterial:'',
        seatSize:'',
        seatPrice: '',
        ownerUnit: '',
        constructUnit: '',
        seatImage: {
        },
        seatImageList: {},
        seatIntroduce:'',
        province: '',
        city: '',
        strict: '',
        street: '',
        geoPoint: [],
        gpsLng: 0,
        gpsLat: 0,
        adress: '',
        validTime: '',
        status: '',
      },
      statusOptions:[],
      seatOptions: [],
      validRules: {
        donateNo: [{ required: true, trigger: 'blur', message: '共建编号必须输入' }],
        seatNo: [{ required: true, trigger: 'blur', message: '座椅编号必须输入' }],
        seatPrice: [{ required: true, trigger: 'blur', message: '座椅价格必须输入' }],
        psd: [{ required: true, trigger: 'blur', message: '座椅所在省市县街道必须输入' }],
        adress: [{ required: true, trigger: 'blur', message: '详细地址必须输入' }],
        seatImage: [{ required: true, trigger: 'blur', message: '座椅主图必须上传' }],
        validTime: [{ required: true, trigger: 'blur', message: '认捐截止时间必须输入' }]
      }
    }
  },
  computed: {
    provinceText() {
      const array = []
      if (this.form.province) {
        array.push(this.form.province)
      }
      if (this.form.city) {
        array.push(this.form.city)
      }
      if (this.form.strict) {
        array.push(this.form.strict)
      }
      if (this.form.street) {
        array.push(this.form.street)
      }
      return array.join('/')
    }
  },
  created() {
    if (this.$route.path === '/seats-donate/add') {
      this.optType = 'add'
    } else {
      this.optType = 'edit'
    }
    this.init(this.$route.params.key)
  },
  methods: {
    init(id) {
      this.loading = true
      donate_init_api(this.optType, id).then(res => {
        this.statusOptions = res.map.statusOptions || []
        Object.assign(this.form, res.value)
        // this.form.geoPoint = [res.value.gpsLng, res.value.gpsLat]
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    // handleSeatSelect(seatId) {
    //   this.loading = true
    //   seat_get_api(seatId).then(res => {
    //     const data = res.value
    //     this.form.seatNo = data.seatNo
    //     this.form.seatName = data.seatName
    //     this.form.seatPrice = data.price
    //     this.form.seatImageUrl = data.image
    //   }).catch(e => {
    //     this.form.seatNo = ''
    //     this.form.seatName = ''
    //     this.form.seatPrice = ''
    //     this.form.seatImageUrl = {
    //       files: []
    //     }
    //   }).finally(() => {
    //     this.loading = false
    //   })
    // },
    handleBack(done) {
      if (done) {
        done()
      }
      this.$router.go(-1)
    },
    handleSave(done) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          donate_save_api(this.optType, this.form).then(res => {
            // this.$router.push('/seats-donate/list')
          }).catch(e => {
          }).finally(() => {
            this.loading = false
            done()
          })
        } else {
          done()
        }
      })
    },
    handleSaveStatus(done, status) {
      this.loading = true
      this.$confirm('请确认是否继续此操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        donate_save_status_api(this.form.id, status).then(res => {
          // this.$router.push('/seats-donate/list')
        }).catch(e => {
        }).finally(() => {
          this.$message({
            type: 'success',
            message: '修改成功!'
          })
          this.loading = false
          done()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
        this.loading = false
        done()
      })
    }
  }
}
</script>
