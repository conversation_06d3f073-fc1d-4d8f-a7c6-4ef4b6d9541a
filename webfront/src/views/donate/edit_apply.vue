<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack"/>
    <el-main v-loading="loading">
      <el-form ref="form" :model="form" :rules="validRules" label-width="120px">
        <el-card shadow="never" style="width: 1000px">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <div>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="donateNo" label="捐赠编号">
                  <el-input :readonly="optType == 'edit'" v-model="form.donateNo" style="width: 280px" @change="handleSeatSelect"/>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatNo" label="座椅编号">
                  {{form.seatNo}}
                </el-form-item>
                <el-form-item prop="seatImage" label="座椅主图" >
                  <div>
                    <el-picture-upload v-model="form.seatImage" multiple :limit="1" :disabled="true"/>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="seatName" label="座椅名称">
                  {{form.seatName}}
                </el-form-item>
                <el-form-item prop="material" label="座椅材质">
                  {{form.seatMaterial}}
                </el-form-item>
                <el-form-item prop="seatSize" label="座椅大小">
                  {{form.seatSize}}
                </el-form-item>
                <el-form-item prop="price" label="座椅价格">
                  {{form.seatPrice}}
                </el-form-item>
              </el-col>
              <el-col>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="name" label="姓名">
                  <el-input v-model="form.name" style="width: 200px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="idCardNo" label="身份证号">
                  <el-input v-model="form.idCardNo" style="width: 200px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="sex" label="性别">
                    <el-radio v-model="form.sex" label="1" >男</el-radio>
                    <el-radio v-model="form.sex" label="2" >女</el-radio>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="age" label="年龄">
                  <el-input v-model="form.age" style="width: 200px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="tel" label="手机号">
                  <el-input v-model="form.tel" style="width: 200px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item prop="province" label="省市县">
                  <el-cascader
                    size="large"
                    :options="options"
                    v-model="selectedOptions"
                    @change="addressChange"
                    style="width: 300px">
                  </el-cascader>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="type" label="捐赠份额" v-if="optType == 'edit'">
                  {{ form.shareNum }} / 100
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="type" label="预估金额" v-if="optType == 'edit'">
                  {{ form.seatPrice * form.shareNum /100 }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="realNum" label="实际份额">
                  <el-input-number :disabled="true" v-model="form.realNum" :min="1" :max="100" label="实际份额"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">
                <el-form-item prop="payedPrice" label="实际分配金额">
                  <el-input v-model="form.payedPrice" style="width: 150px" @change="payedPriceHandleChange"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="payedPrice" label="实际分配金额">
                  <label style="color: red;font-size: 0.7rem"> (剩余分配金额：{{ form.payedPriceRemain }})</label>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="type" label="捐赠属性">
<!--                  <el-radio v-model="form.type" label="1" value="form.type">街道</el-radio>-->
<!--                  <el-radio v-model="form.type" label="2" value="form.type">个人</el-radio>-->
                  <el-select v-model="form.type" placeholder="请选择" style="width: 180px">
                    <el-option
                      v-for="item in donateApplyTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item v-if="form.type == 2"  prop="personCertificateImage" label="身份证明">
                    <el-picture-upload v-model="form.personCertificateImage" multiple :limit="1"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item v-if="form.type != 2" prop="aptitudeCertificateImage" label="资质证明">
                  <el-picture-upload v-model="form.aptitudeCertificateImage" multiple :limit="1"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="channel" label="了解渠道">
                  <el-input v-model="form.channel" style="width: 200px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="reason" label="捐赠原因(textArea)">
                  <el-input v-model="form.reason" type="textarea" style="width: 630px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="donationWord" label="寄语(textArea)">
                  <el-input v-model="form.donationWord" type="textarea" style="width: 630px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="donationWord" label="寄语显示">
                  <el-switch
                    v-model="form.donationWordFlag"
                    active-color="#13ce66"
                    inactive-color="#ff4949">
                  </el-switch>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="validTime" label="截止时间">
                  {{form.validTime}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item v-if="optType == 'edit'" prop="applyTime" label="申请时间">
                  <el-date-picker v-model="form.applyTime" :disabled="true" type="datetime" format="" placeholder="请选择"> </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="applyPassTime" label="申请通过时间">
                  <el-date-picker v-model="form.applyPassTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择"> </el-date-picker>
<!--                  <el-date-picker v-if="form.status === 2" v-model="form.applyPassTime" type="datetime" format="" placeholder="请选择"> </el-date-picker>-->
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="never" style="width: 1000px; margin-top: 10px">
          <div style="text-align: right">
            <el-loading-button type="" style="width: 100px" @click="handleBack">取消</el-loading-button>
            <el-loading-button style="width: 100px" @click="handleSave">保存</el-loading-button>
            <el-loading-button type="success" v-if="form.status === 1" style="width: 100px" @click="done => handleSaveStatusPass(done,5)">确认份额</el-loading-button>
            <el-loading-button type="success" v-if="form.status === 6" style="width: 100px" @click="done => handleSaveStatusPass(done,2)">审核通过</el-loading-button>
            <el-loading-button type="danger" v-if="form.status === 6" style="width: 100px" @click="done => handleSaveStatus(done,3)">审核不通过</el-loading-button>
            <el-loading-button type="info" v-if="form.status === 6" style="width: 100px" @click="done => handleSaveStatus(done,4)">无效</el-loading-button>
          </div>
        </el-card>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import { regionData,CodeToText } from 'element-china-area-data'
import {
  donate_apply_init_api,
  donate_apply_save_api,
  donate_apply_save_status_api,
  seat_get_donate_no_api
} from '@/api/donate'
export default {
  name: 'DonateApplyEdit',
  data() {
    return {
      isEdit: true,
      loading: false,
      optType: null,
      form: {
        donateNo: '',
        seatNo:'',
        seatName: '',
        seatImage:{},
        seatPrice: '',
        payedPrice:'',
        predictPrice:'',
        seatProvince: '',
        payedPriceRemain:'',
        seatCity: '',
        seatStrict: '',
        seatStreet: '',
        seatAdress: '',
        seatGeoPoint:[],
        shareNum:'',
        realNum:'',
        name:'',
        idCardNo:'',
        sex:0,
        age:'',
        tel:'',
        province: '',
        city: '',
        strict: '',
        type:'',
        personCertificateImage:{},
        aptitudeCertificateImage:{},
        channel:'',
        reason:'',
        donationWord:'',
        donationWordFlag:'',
        applyTime:'',
        applyPassTime:'',
        validTime: '',
        status:''
      },
      options: regionData, //省市区数据
      selectedOptions:[], // 选中的地区
      donateApplyTypeOptions:[],
      validRules: {
        donateNo: [{ required: true, trigger: 'blur', message: '共建编号必须输入' }],
        name: [{ required: true, trigger: 'blur', message: '姓名必须输入' }],
        idCardNo: [{ required: true, trigger: 'blur', message: '身份证号必须输入' }],
        sex: [{ required: true, trigger: 'blur', message: '性别必须选择' }],
        age: [{ required: true, trigger: 'blur', message: '年龄必须输入' }],
        tel: [{ required: true, trigger: 'blur', message: '手机号必须输入' }],
        payedPrice: [{ required: true, trigger: 'blur', message: '实际分配金额必须输入' }],
        type: [{ required: true, trigger: 'blur', message: '捐赠属性必须输入' }]
      }
    }
  },
  computed: {
  },
  created() {
    // 初始化省市区
    if (this.$route.path === '/seats-donate/add_apply') {
      this.optType = 'add'
    } else {
      this.optType = 'edit'
    }
    this.init(this.$route.params.key)
  },
  methods: {
    init(id) {
      this.loading = true
      donate_apply_init_api(this.optType, id).then(res => {
        this.donateApplyTypeOptions = res.map.donateApplyTypeOptions || []
        Object.assign(this.form, res.value)
        this.form.seatGeoPoint = [res.value.seatGpsLng, res.value.seatGpsLat]
        this.form.sex = res.value.sex + ""
        this.form.type = res.value.type + ""
        if(this.form.status === 1 ) {
          this.form.payedPrice = Math.round(this.form.predictPrice)
        }
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    addressChange(arr) {
      this.form.province = CodeToText[arr[0]];
      this.form.city = CodeToText[arr[1]];
      this.form.strict = CodeToText[arr[2]];
    },
    handleBack() {
      this.$router.go(-1)
    },
    handleSave(done) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          donate_apply_save_api(this.optType, this.form).then(res => {
            // this.$router.push('/seats-donate/edit_apply')
          }).catch(e => {
          }).finally(() => {
            this.loading = false
            done()
          })
        } else {
          done()
        }
      })
    },
    handleSaveStatus(done, status) {

      this.loading = true
      this.$confirm('请确认是否继续此操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        donate_apply_save_status_api(this.form.id, status).then(res => {
          // this.$router.push('/seats-donate/apply')
        }).catch(e => {
        }).finally(() => {
          this.$message({
            type: 'success',
            message: '修改成功!'
          })
          this.loading = false
          done()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
        this.loading = false
        done()
      });
    },
    handleSaveStatusPass(done, status) {

      let msg
      if(status === 5){
        msg = '当前实际分配金额为:' + this.form.payedPrice + '，是否确认';
      } else {
        msg = '当前实际分配金额为:' + this.form.payedPrice + '，是否确认通过审核';
      }
      this.loading = true
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        donate_apply_save_status_api(this.form.id, status, this.form.payedPrice, this.form.realNum).then(res => {
          // this.$router.push('/seats-donate/apply')
        }).catch(e => {
        }).finally(() => {
          this.loading = false
          done()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
        this.loading = false
        done()
      });
    },
    realNumHandleChange(value) {
      this.form.payedPrice = Math.round(value * this.form.realNum / 100)
    },
    payedPriceHandleChange(value) {
      this.form.realNum = Math.round(value * 100 /  this.form.seatPrice)
    },
    handleSeatSelect(donateNo) {
      this.loading = true
      seat_get_donate_no_api(donateNo).then(res => {
        const data = res.value
        this.form.seatNo = data.seatNo
        this.form.seatName = data.seatName
        this.form.seatPrice = data.price
        this.form.seatImage = data.image
        this.form.seatSize = data.seatSize
        this.form.seatMaterial = data.material
      }).catch(e => {
        this.form.seatNo = ''
        this.form.seatName = ''
        this.form.seatPrice = ''
        this.form.seatSize = ''
        this.form.seatMaterial = ''
        this.form.seatImage = {}
      }).finally(() => {
        this.loading = false
      })
    },
  }
}
</script>
