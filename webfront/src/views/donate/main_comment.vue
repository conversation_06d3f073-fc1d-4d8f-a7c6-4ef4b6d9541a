<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-row>
          <el-col :span="8">
            <el-form-item prop="status" label="审核状态">
              <el-pulldown v-model="form.status" :options="statusOptions"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar
        :add-show="false"
        :delete-show="false"
        :export-show="true"
        :sort-column="sortColumn"
        :title-text="'评论详情'"
        title-show />
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="selection" width="45"/>
        <el-table-column prop="name" label="评论人" width="100" sortable="custom"/>
        <el-table-column prop="comment" label="评论内容" width="400" show-tooltip-when-overflow />
        <el-table-column prop="createdAt" label="评论时间" width="170" sortable="custom"/>
        <el-table-column prop="status" label="审核状态" sortable="custom">
          <template slot-scope="prop">
            <el-tag type="primary">{{ prop.row.statusText }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="" fixed="right" width="200px">
          <template slot-scope="scope">
            <el-button size="mini" type="success" v-if="scope.row.status === 0" icon="el-icon-edit" class="mini-btn" @click="handleSaveStatus(scope.row, 1)">审核通过</el-button>
            <el-button size="mini" type="info" v-if="scope.row.status === 1" icon="el-icon-edit" class="mini-btn" @click="handleSaveStatus(scope.row, 0)">审核驳回</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-pagination-table>
    </el-main>
  </el-container>
</template>

<script>
import {list_comment_init_api, delete_comment_api, donate_comment_save_status_api} from '@/api/donate'
export default {
  name: 'DonateCommentMain',
  data() {
    return {
      loading: false,
      form: {
        donateId:''
      },
      statusOptions: [],
      dataList: [],
      pagination: {
        pageNumber: 1
      },
      sortColumn: {
      },
      sortMapping: {
        name: 'dc.name',
        createdAt: 'dc.created_at',
        status: 'dc.status',
      }
    }
  },
  created() {
    this.form.donateId = this.$route.params.key
    this.handleSearch(true)
  },
  methods: {
    handleSearch(init) {
      this.loading = true
      list_comment_init_api(init, this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        this.dataList = res.value
        this.pagination = res.pagination
        if (init === true) {
          this.statusOptions = res.data.statusOptions
        }
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
      this.form.status = ''
      this.pagination.pageNumber=0
      this.handleSearch();
    },
    handleDelete(index, row) {
      const self = this
      this.$confirm('是否要删除此评论').then(res => {
        delete_comment_api(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
    handleSaveStatus(row, status) {
      const self = this
      this.$confirm('请确认是否继续此操作').then(res => {
        donate_comment_save_status_api(row.id, status).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    }
  }
}
</script>
