<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-form-item prop="seatNo" label="座椅编号">
          <el-input v-model="form.seatNo" style="width: 150px"/>
        </el-form-item>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar :delete-show="false" :sort-column="sortColumn" @handleAddItem="handleAdd"/>
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="index" label="#" width="45" align="center"/>
        <el-table-column prop="seatNo" label="座椅编号" width="120" sortable="custom" show-overflow-tooltip/>
        <el-table-column prop="name" label="座椅名称" width="140" sortable="custom"/>
        <el-table-column prop="image" label="座椅图片" width="200px">
          <template slot-scope="prop">
            <img :src="prop.row.image" width="120px" height="100px"/>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="座椅大小" width="100"/>
        <el-table-column prop="material" label="座椅材质" width="170"/>
        <el-table-column prop="status" label="状态" width="120" sortable="custom">
          <template slot-scope="prop">
            <el-tag type="primary">{{ prop.row.statusText }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="座椅价格" width="100"/>
        <el-table-column prop="startTime" label="发布开始时间" width="140" sortable="custom"/>
        <el-table-column prop="endTime" label="发布结束时间"/>
        <el-table-column label="操作" fixed="right" align="left" width="300px">
          <template slot-scope="scope">
            <el-button size="mini" v-if="scope.row.status === 1" type="info" icon="el-icon-star-off" class="mini-btn" @click="handleCancel(scope.$index, scope.row)">取消发布</el-button>
            <el-button size="mini" icon="el-icon-edit" class="mini-btn" @click="handleEdit(scope.$index, scope.row)">{{ scope.row.status === 0 ? '编辑' : '查看' }}</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-pagination-table>
    </el-main>
  </el-container>
</template>

<script>
import { list_search_api, delete_api, cancel_api } from '@/api/seats'
export default {
  name: 'SeatsMain',
  data() {
    return {
      loading: false,
      form: {
        seatNo: ''
      },
      dataList: [],
      sortColumn: {},
      pagination: {
        pageNumber: 1
      },
      sortMapping: {
        seatNo: 'a.seat_no',
        startTime: 'a.start_time'
      }
    }
  },
  created() {
    this.handleSearch()
  },
  methods: {
    handleSearch() {
      this.loading = true
      list_search_api(this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        this.dataList = res.value
        this.pagination = res.pagination
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
    },
    handleAdd() {
      this.$router.push('/seats/add')
    },
    handleEdit(index, row) {
      this.$router.push('/seats/edit/' + row.id)
    },
    handleDelete(index, row) {
      const self = this
      this.$confirm('是否要删除座椅【' + row.name + '】').then(res => {
        delete_api(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
    handleCancel(index, row) {
      const self = this
      this.$confirm('是否取消发布').then(res => {
        cancel_api(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    }
  }
}
</script>
