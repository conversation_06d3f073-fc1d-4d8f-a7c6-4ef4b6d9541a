<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack"/>
    <el-main v-loading="loading">
      <el-form ref="form" :model="form" :rules="validRules" label-width="100px">
        <el-card shadow="never" style="width: 1000px">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <div>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatNo" label="座椅编号">
                  <el-input v-model="form.seatNo" :readonly="!editable" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="image" label="座椅主图">
                  <!-- <el-photo-multi-upload editable :size="1" :width="200" :height="150" :img-data-list.sync="form.image"/>-->
                  <div>
                    <el-picture-upload v-model="form.image" :disabled="!editable" multiple :limit="1"/>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="seatName" label="座椅名称">
                  <el-input v-model="form.seatName" :readonly="!editable" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="material" label="座椅材质">
                  <el-input v-model="form.material" :readonly="!editable" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="seatSize" label="座椅大小">
                  <el-input v-model="form.seatSize" :readonly="!editable" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="price" label="座椅价格">
                  <el-input v-model="form.price" type="number" :readonly="!editable" style="width: 200px"/>
                </el-form-item>
              </el-col>
              <el-col>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="introduce" label="座椅介绍">
                  <el-input v-model="form.introduce" :readonly="!editable" type="textarea" style="width: 630px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="状态">
                  <el-tag type="primary">{{optType === 'add' ? '做成中' : form.statusText}}</el-tag>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="never" style="width: 1000px; margin-top: 10px">
          <div slot="header" class="clearfix">
            <span>座椅其它图片</span>
          </div>
          <div>
            <!-- <el-photo-multi-upload editable :size="8" :width="200" :height="180" :img-data-list.sync="form.imageList"/>-->
            <el-picture-upload v-model="form.imageList" :disabled="!editable" multiple :limit="5"/>
          </div>
        </el-card>
        <el-card shadow="never" style="width: 1000px; margin-top: 10px">
          <div style="text-align: right">
            <el-loading-button type="" style="width: 100px" @click="handleBack">返回</el-loading-button>
            <el-loading-button v-if="optType !== 'add' && form.status === 0" type="success" style="width: 100px" @click="handleDeploy">发布</el-loading-button>
<!--            <el-loading-button v-if="optType === 'add' || form.status === 0" style="width: 100px" @click="handleSave">保存</el-loading-button>-->
            <el-loading-button style="width: 100px" @click="handleSave">保存</el-loading-button>
          </div>
        </el-card>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import { seat_get_api, seat_edit_api } from '@/api/seats'
export default {
  name: 'SeatsEdit',
  data() {
    return {
      isEdit: true,
      loading: false,
      optType: null,
      form: {
        seatNo: '',
        seatName: '',
        seatSize: '',
        material: '',
        introduce: '',
        price: '',
        statusText: '',
        status: 0,
        image: {
        },
        imageList: {}
      },
      validRules: {
        seatNo: [{ required: true, trigger: 'blur', message: '座椅编号必须输入' }],
        seatName: [{ required: true, trigger: 'blur', message: '座椅名称必须输入' }],
        image: [{ required: true, trigger: 'blur', message: '座椅主图必须上传' }]
      }
    }
  },
  computed: {
    editable() {
      // return this.form.status === 0
      return true
    }
  },
  created() {
    if (this.$route.path === '/seats/add') {
      this.optType = 'add'
    } else {
      this.optType = 'edit'
      this.handleInit(this.$route.params.key)
    }
  },
  methods: {
    handleInit(id) {
      this.loading = true
      seat_get_api(id).then(res => {
        Object.assign(this.form, res.value)
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    handleBack() {
      this.$router.go(-1)
    },
    handleSave(done, deploy) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          seat_edit_api(this.optType, this.form, deploy).then(res => {
            // this.$router.push('/seats/list')
          }).catch(e => {
          }).finally(() => {
            this.loading = false
            done()
          })
        } else {
          done()
        }
      })
    },
    handleDeploy(done) {
      this.handleSave(done, true)
    }
  }
}
</script>
