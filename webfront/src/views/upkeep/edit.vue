<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack"/>
    <el-main v-loading="loading">
      <el-form ref="form" :model="form" :rules="validRules" label-width="140px">
        <el-card shadow="never" style="width: 1000px">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <div>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="donateNo" label="捐赠编号">
                  <el-input v-model="form.donateNo" style="width: 200px" disabled="true"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="adoptNo" label="认养编号">
                  <el-input v-model="form.adoptNo" style="width: 200px" disabled="true"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatNo" label="座椅编号">
                  <el-input v-model="form.seatNo" style="width: 200px" disabled="true"/>
                </el-form-item>
                <el-form-item prop="seatImage" label="座椅主图">
                  <div>
                    <el-picture-upload v-model="form.seatImage" multiple :limit="1" disabled="true"/>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item prop="seatName" label="座椅名称">
                  <el-input v-model="form.seatName" style="width: 200px" disabled="true"/>
                </el-form-item>
                <el-form-item prop="material" label="座椅材质">
                  <el-input v-model="form.seatMaterial" style="width: 200px" disabled="true"/>
                </el-form-item>
                <el-form-item prop="seatSize" label="座椅大小">
                  <el-input v-model="form.seatSize" style="width: 200px" disabled="true"/>
                </el-form-item>
                <el-form-item prop="seatPrice" label="座椅价格">
                  <el-input v-model="form.seatPrice" type="number" style="width: 200px" disabled="true"/>
                </el-form-item>
              </el-col>
              <el-col>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="ownerUnit" label="建设单位">
                  <el-input v-model="form.ownerUnit" style="width: 500px" disabled="true"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="constructUnit" label="施工单位">
                  <el-input v-model="form.constructUnit" style="width: 500px" disabled="true"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="adress" label="详细地址">
                  <el-input v-model="form.adress" style="width: 630px" disabled="true"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item prop="province" label="省市县街道">
                  {{form.province}}/{{form.city}}/ {{form.strict}}/ {{form.street}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="status" label="养护状态">
                  <el-tag type="primary"> {{form.statusText}} </el-tag>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="upkeepDateBegin" label="开始日期">
                  <el-date-picker v-model="form.upkeepDateBegin" value-format="yyyy-MM-dd" type="date"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="upkeepDateEnd" label="结束日期">
                  <el-date-picker v-model="form.upkeepDateEnd" value-format="yyyy-MM-dd" type="date"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item prop="upkeepUserId" label="养护人">
                <el-pulldown v-model="form.upkeepUserId" :options="form.upkeepUserOptions"/>
              </el-form-item>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="never" style="width: 1000px; margin-top: 10px">
          <div style="text-align: right">
            <el-loading-button type="" style="width: 100px" @click="handleBack">取消</el-loading-button>
            <el-loading-button style="width: 100px" @click="handleSave">保存</el-loading-button>
          </div>
        </el-card>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import {upkeep_init_api, upkeep_save_api} from '@/api/upkeep'
export default {
  name: 'DonateEdit',
  data() {
    return {
      isEdit: true,
      loading: false,
      optType: null,
      form: {
        id: '',
        donateNo: '',
        adoptNo: '',
        seatId: '',
        seatNo: '',
        seatName: '',
        seatMaterial:'',
        seatSize:'',
        seatPrice: '',
        ownerUnit: '',
        constructUnit: '',
        seatImage: {
        },
        seatIntroduce:'',
        province: '',
        city: '',
        strict: '',
        street: '',
        adress: '',
        status: '',
        upkeepDateBegin: '',
        upkeepDateEnd: '',
        upkeepUserId:''
      },
      upkeepUserOptions: [],
      validRules: {
        upkeepDateBegin: [{ required: true, trigger: 'blur', message: '养护开始时间必须输入' }],
        upkeepDateEnd: [{ required: true, trigger: 'blur', message: '养护结束时间必须输入' }],
        upkeepUser: [{ required: true, trigger: 'blur', message: '养护人必须输入' }]
      }
    }
  },
  computed: {
    provinceText() {
      const array = []
      if (this.form.province) {
        array.push(this.form.province)
      }
      if (this.form.city) {
        array.push(this.form.city)
      }
      if (this.form.strict) {
        array.push(this.form.strict)
      }
      if (this.form.street) {
        array.push(this.form.street)
      }
      return array.join('/')
    }
  },
  created() {
    this.init(this.$route.params.key, true)
  },
  methods: {
    init(id, init) {
      this.loading = true
      upkeep_init_api(id, init).then(res => {
        Object.assign(this.form, res.value)
        if (init === true) {
          this.upkeepUserOptions = res.data.upkeepUserOptions
        }
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    handleBack(done) {
      if (done) {
        done()
      }
      this.$router.go(-1)
    },
    handleSave(done) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          upkeep_save_api(this.form).then(res => {

          }).catch(e => {
          }).finally(() => {
            this.loading = false
            done()
          })
        } else {
          done()
        }
      })
    }
  }
}
</script>
