<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack"/>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-row>
          <el-col :span="8">
            <el-form-item prop="upkeepName" label="养护人">
              <el-input v-model="form.upkeepName" style="width: 150px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="upkeepCompany" label="养护单位">
              <el-input v-model="form.upkeepCompany" style="width: 150px"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar
        :add-show="false"
        :delete-show="false"
        :export-show="false"
        :sort-column="sortColumn"
        :title-text="'养护记录'"
        title-show
        @handleExport="handleExport"/>
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="selection" width="45"/>
        <el-table-column prop="upkeepName" label="养护姓名" width="120" />
        <el-table-column prop="upkeepCompany" label="养护单位" width="200" />
        <el-table-column prop="upkeepTime" label="维护时间" width="200" />
        <el-table-column prop="memo" label="备注" width="300" />
        <el-table-column label="" fixed="right" width="200px">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-edit" class="mini-btn" @click="handleEdit(scope.$index, scope.row)">详情</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-pagination-table>
    </el-main>
  </el-container>
</template>

<script>
import {list_seat_init_api, delete_upkeep_api} from '@/api/upkeep'
export default {
  name: 'UpkeepSeatList',
  data() {
    return {
      loading: false,
      form: {
        upkeepName:'',
        upkeepCompany: '',
        upkeepId:''
      },
      statusOptions:[],
      dataList: [],
      sortColumn: {},
      pagination: {
        pageNumber: 1
      },
      sortMapping: {
      }
    }
  },
  created() {
    this.form.upkeepId = this.$route.params.key
    this.handleSearch(true)
  },
  methods: {
    handleSearch(init) {
      this.loading = true
      list_seat_init_api(init, this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        this.dataList = res.value
        this.pagination = res.pagination
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
      this.form.upkeepName = ''
      this.form.upkeepCompany = ''
      this.pagination.pageNumber=0
      this.handleSearch();
    },
    handleEdit(index, row) {
      this.$router.push('/upkeep-assign/init-seat/'+ row.id)
    },
    handleMultiDelete() {
    },
    handleDelete(index, row) {
      const self = this
      this.$confirm('是否要删除维护记录').then(res => {
        delete_upkeep_api(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
    handleBack() {
      this.$router.go(-1)
    },
  }
}
</script>
