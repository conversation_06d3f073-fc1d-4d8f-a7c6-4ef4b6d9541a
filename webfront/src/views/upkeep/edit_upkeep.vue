<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack"/>
    <el-main v-loading="loading">
      <el-form ref="form" :model="form" :rules="validRules" label-width="100px">
        <el-card shadow="never" style="width: 1000px">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <div>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatNo" label="座椅No">
                  {{form.seatNo}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatName" label="座椅名称">
                  {{form.seatName}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatImageUrl" label="座椅图片">
                  <el-image :src="form.seatImageUrl" :preview-src-list="[form.seatImageUrl]"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatAdress" label="详细地址">
                  <el-geo-coordinates disabled="true"
                    :gps-lng.sync="form.seatGpsLng"
                    :gps-lat.sync="form.seatGpsLat"
                    :address.sync="form.seatAdress"
                    :province.sync="form.seatProvince"
                    :city.sync="form.seatCity"
                    :strict.sync="form.seatStrict"
                    :street.sync="form.seatStreet"
                    isDonate
                    style="width: 500px;"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item prop="province" label="省市县街道">
                  {{form.seatProvince}}/{{form.seatCity}}/ {{form.seatStrict}}/ {{form.seatStreet}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="upkeepName" label="养护姓名">
                  {{form.upkeepName}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="upkeepCompany" label="养护单位">
                  {{form.upkeepCompany}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatAdress" label="养护详细地址">
                  <el-geo-coordinates disabled="true"
                    :gps-lng.sync="form.gpsLng"
                    :gps-lat.sync="form.gpsLat"
                    :address.sync="form.adress"
                    :province.sync="form.province"
                    :city.sync="form.city"
                    :strict.sync="form.strict"
                    :street.sync="form.street"
                    style="width: 500px;"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item prop="province" label="所在省市县">
                  {{form.province}}/{{form.city}}/ {{form.strict}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="upkeepImageBefore" label="维护前照片">
                  <el-image :src="form.upkeepImageBefore" :preview-src-list="[form.upkeepImageBefore]"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="upkeepImageAfter" label="维护后照片">
                  <el-image :src="form.upkeepImageAfter" :preview-src-list="[form.upkeepImageAfter]"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="upkeepTime" label="维护时间">
                  <el-date-picker disabled="true" v-model="form.upkeepTime" type="datetime" format="" placeholder="请选择"> </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="memo" label="备注">
                  {{form.memo}}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="never" style="width: 1000px; margin-top: 10px">
          <div style="text-align: right">
            <el-loading-button type="" style="width: 100px" @click="handleBack">取消</el-loading-button>
          </div>
        </el-card>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import {upkeep_seat_init_api} from '@/api/upkeep'
export default {
  name: 'UpkeepSeatEdit',
  data() {
    return {
      isEdit: true,
      loading: false,
      optType: null,
      form: {
        seatNo:'',
        seatName: '',
        seatImageUrl:'',
        seatPrice: '',
        seatProvince: '',
        seatCity: '',
        seatStrict: '',
        seatStreet: '',
        seatAdress: '',
        seatGpsLng:'',
        seatGpsLat:'',
        seatGeoPoint:[],
        name:'',
        idCardNo:'',
        sex:'',
        age:'',
        tel:'',
        province: '',
        city: '',
        strict: '',
        type:'',
        upkeepImageBefore:'',
        upkeepImageAfter:'',
        channel:'',
        memo:'',
        donationWord:'',
        upkeepTime:''
      },
      validRules: {
      }
    }
  },
  computed: {
  },
  created() {
    this.init(this.$route.params.key)
  },
  methods: {
    init(id) {
      this.loading = true
      upkeep_seat_init_api(id).then(res => {
        Object.assign(this.form, res.value)
        this.form.seatGeoPoint = [res.value.seatGpsLng, res.value.seatGpsLat]
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    handleBack() {
      this.$router.go(-1)
    }
  }
}
</script>
