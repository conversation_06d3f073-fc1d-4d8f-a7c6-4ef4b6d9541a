<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-row>
          <el-col :span="8">
            <el-form-item prop="seatNo" label="捐赠编号">
              <el-input v-model="form.donateNo" style="width: 150px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="seatNo" label="认养编号">
              <el-input v-model="form.adoptNo" style="width: 150px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="status"  label="养护状态">
              <el-pulldown v-model="form.status" :options="statusOptions"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar
        :add-show="false"
        :delete-show="false"
        :export-show="true"
        :sort-column="sortColumn"
        :title-text="'养护管理'"/>
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="selection" width="45"/>
        <el-table-column prop="donateNo" label="捐赠No" width="110" sortable="custom"/>
        <el-table-column prop="adoptNo" label="认养No" width="110" sortable="custom"/>
        <el-table-column prop="seatNo" label="座椅编号" width="110" sortable="custom"/>
        <el-table-column prop="seatName" label="座椅名称" width="100" />
        <el-table-column prop="seatPrice" label="价格" width="80"/>
        <el-table-column prop="pcdName" label="省/市/县/街道" width="180"/>
        <el-table-column prop="adress" label="详细地址" width="140"/>
        <el-table-column prop="status" label="状态" sortable="custom">
          <template slot-scope="prop">
            <el-tag type="primary">{{ prop.row.statusText }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="" width="300px">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-edit" class="mini-btn" @click="handleEdit(scope.$index, scope.row)">任务分配</el-button>
            <el-button size="mini" icon="el-icon-info" class="mini-btn" @click="upkeepSeatInfo(scope.$index, scope.row)">查看记录</el-button>
          </template>
        </el-table-column>
      </el-pagination-table>
    </el-main>
  </el-container>
</template>

<script>
import { list_init_api} from '@/api/upkeep'
export default {
  name: 'DonateMain',
  data() {
    return {
      loading: false,
      form: {
        adoptNo:'',
        donateNo: '',
        status: ''
      },
      statusOptions:[],
      dataList: [],
      sortColumn: {},
      sortMapping: {
        donateNo: 'total.donate_no',
        adoptNo: 'total.adopt_no',
        seatNo: 'total.seat_no',
        createdAt: 'total.created_at',
        status: 'total.status',
      },
      pagination: {
        pageNumber: 1
      }
    }
  },
  created() {
    this.handleSearch(true)
  },
  methods: {
    handleSearch(init) {
      this.loading = true
      list_init_api(init, this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        this.dataList = res.value
        this.pagination = res.pagination
        if (init === true) {
          this.statusOptions = res.data.statusOptions
        }
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
      this.form.donateNo = ''
      this.form.adoptNo = ''
      this.form.status = ''
      this.pagination.pageNumber=0
      this.handleSearch();
    },
    handleEdit(index, row) {
      this.$router.push('/seats-upkeep/edit/' + row.id)
    },
    upkeepSeatInfo(index, row) {
      this.$router.push('/upkeep-assign/list-seat/' + row.id)
    },
    beforeUpload() {
      this.loading = true
    }
  }
}
</script>
