<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form ref="searchForm" :inline="true" :model="form" @submit.native.prevent>
        <el-form-item label="问卷名称">
          <el-input v-model="form.title" :maxlength="128" placeholder="输入问卷名称" clearable/>
        </el-form-item>
        <el-form-item label="发布状态">
          <el-pulldown v-model="form.status" :options="statusOptions"/>
        </el-form-item>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar :delete-show="false" :sort-column="sortColumn" @handleAddItem="handleAdd"/>
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="selection" width="45"/>
        <el-table-column prop="title" label="问卷名称" width="280" show-overflow-tooltip/>
        <el-table-column prop="statusText" label="问卷状态" width="120"/>
        <el-table-column prop="startTime" label="开始时间" width="170"/>
        <el-table-column prop="endTime" label="结束时间" width="170"/>
        <el-table-column prop="createdAt" label="创建时间" width="170"/>
        <el-table-column prop="quesCount" label="题目数量" width="80"/>
        <el-table-column />
        <el-table-column label="" fixed="right" width="300px">
          <template slot-scope="scope">
            <el-button size="mini" v-if="scope.row.status === 1" type="info" icon="el-icon-star-off" class="mini-btn" @click="handleCancel(scope.$index, scope.row)">取消发布</el-button>
            <el-button size="mini" icon="el-icon-tickets" class="mini-btn" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-pagination-table>
    </el-main>
  </el-container>
</template>

<script>
import { vote_search_api, delete_api, cancel_api  } from '@/api/vote'
export default {
  name: 'VoteMain',
  data() {
    return {
      loading: false,
      form: {
        title: '',
        status: ''
      },
      statusOptions: [],
      dataList: [],
      sortColumn: {},
      pagination: {
        pageNumber: 1
      }
    }
  },
  created() {
    this.handleSearch(true)
  },
  methods: {
    handleSearch(init) {
      vote_search_api(init, this.form, this.pagination.pageNumber).then(res => {
        if (init === true) {
          this.statusOptions = res.data.statusOptions
        }
        this.dataList = res.value
        this.pagination = res.pagination
      }).catch(e => {
      }).finally(() => {
      })
    },
    resetCondition() {
    },
    handleAdd() {
      this.$router.push('/vote/add')
    },
    handleEdit(index, row) {
      this.$router.push('/vote/edit/' + row.id)
    },
    handleDelete(index, row) {
      const self = this
      this.$confirm('是否要删除问卷【' + row.title + '】').then(res => {
        delete_api(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
    handleCancel(index, row) {
      const self = this
      this.$confirm('是否取消发布').then(res => {
        cancel_api(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    }
  }
}
</script>
