<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack"/>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form ref="searchForm" :inline="true" :model="form" @submit.native.prevent>
        <el-form-item label="提交时间">
          <el-date-range v-model="form.dateRange" style="width: 300px"/>
        </el-form-item>
        <el-form-item label="状态">
          <el-pulldown v-model="form.status" :options="statusOptions"/>
        </el-form-item>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar :add-show="false" :delete-show="false" :sort-column="sortColumn"/>
      <el-pagination-table
        :data="tableData"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="index"/>
        <el-table-column prop="wxName" label="微信名" width="200" show-overflow-tooltip/>
        <el-table-column prop="startTime" label="提交时间" width="140" sortable="custom"/>
        <el-table-column prop="duration" label="答卷时长(分)" width="70"/>
        <el-table-column prop="valueFlg" label="状态" width="127">
          <template slot-scope="scope">
            <el-tag type="primary">{{ scope.row.valueFlg }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="channel" label="渠道" width="70">
          <template>
            <span>微信</span>
          </template>
        </el-table-column>
        <el-table-column label="问卷题目">
          <el-table-column v-for="(col, index) in questionnaire.questionList" :key="index" width="200" show-overflow-tooltip>
            <template slot="header">
              <span>{{ col.questionId + '. ' + col.title }}</span>
              <span class="tye">{{ '[ ' + getQuestionType(col.type) + ' ]' }}</span>
            </template>
            <template slot-scope="scope">
              <span>{{ getQuesOptionText(index, scope.row.feedbackDetailMap[col.id]) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column/>
        <el-table-column label="" width="100px">
          <template slot-scope="scope">
            <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-pagination-table>
    </el-main>
  </el-container>
</template>

<script>
import { feedback_search_api, feedback_delete_api } from '@/api/vote'

export default {
  name: 'VoteResponse',
  data() {
    return {
      id: '',
      loading: false,
      form: {
        dateRange: [],
        status: ''
      },
      statusOptions: [],
      questionnaire: {
        questionList: []
      },
      tableData: [],
      sortColumn: {},
      pagination: {
        pageNumber: 1
      }
    }
  },
  created() {
    this.id = this.$route.params.key
    this.handleSearch(true)
  },
  methods: {
    handleSearch(init) {
      feedback_search_api(this.id, init, this.form, this.pagination.pageNumber).then(resp => {
        console.log(resp)
        if (init === true) {
          this.statusOptions = resp.data.statusOptions
          this.questionnaire = resp.data.questionnaire
        }
        this.tableData = resp.value
        this.pagination = resp.pagination
      }).catch(e => {
      }).finally(() => {
      })
    },
    resetCondition() {
    },
    getQuestionType(type) {
      switch (type) {
        case 'radio':
          return '单选题'
        case 'checkbox':
          return '多选题'
        default:
          return type
      }
    },
    handleBack() {
      this.$router.go(-1)
    },
    getQuesOptionText(number, answers) {
      debugger
      if (answers && answers.length > 0) {
        const question = this.questionnaire.questionList[number]
        const options = question.options
        return answers.map(v => {
          debugger
          for (const i in options) {
            if (question.type === 'radio' || question.type === 'checkbox') {
              if (Number(v) === Number(i)) {
                const option = options[i]
                if (option.fillbox === true) {
                  return String.fromCharCode(65 + Number(i)) + '. ' + option.text + ': ' + v.fill
                } else {
                  return String.fromCharCode(65 + Number(i)) + '. ' + option.text
                }
              }
            }
          }
          return v.userOption
        }).join(',')
      }
      return ''
    },
    handleDelete(index, row) {
      const self = this
      this.$confirm('确认是否删除该微信号【' + row.openId + '】的反馈记录?', '提示', { type: 'warning' }).then(async() => {
        self.loading = true
        await feedback_delete_api(row.wjId, row.responseId).then((res) => {
          self.$message({
            message: '该条反馈删除成功。',
            type: 'success'
          })
          self.handleSearch(false)
        }).catch(() => {}).finally(() => {
          self.loading = false
        })
      }).catch(() => { })
    }
  }
}
</script>
