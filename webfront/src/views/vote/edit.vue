<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack"/>
    <div v-loading="loading" class="div-questionnaire-design">
      <div class="div-question-area div-question-area-left">
      </div>
      <el-card v-scroll-update:handle="handleScroll" class="div-question-area div-question-area-right div-design-area" shadow="hover">
        <el-questionnaire-title :title.sync="questionnaireTitle" :describe.sync="questionnaireDescribe" @handleInsert="handleInsert"/>
        <draggable :list="questionList" ghost-class="question-ghost-class" group="question-design">
          <el-question-panel
            v-for="(item, index) in questionList"
            :id="'ques' + (index + 1)"
            ref="quesPanel"
            :key="index"
            :question-list="questionList"
            :must-input="item.must"
            :question-title="item.title"
            :question-details="item.details"
            :image-list="item.imageList"
            :type="item.type"
            :user-display="item.userDisplay"
            :index="index + 1"
            :show-number="item.number"
            :size="questionList.length"
            :is-description="item.type === 'description'"
            :options="item.options"
            @handleInsert="handleInsert"
            @handleEdit="handleEdit"
            @handleCopy="handleCopy"
            @handleDelete="handleDelete"
            @handleUp="handleUp"
            @handleDown="handleDown">
          </el-question-panel>
        </draggable>
        <el-question-add @handleInsert="handleInsert"/>
      </el-card>
      <div class="div-question-area" style="text-align: right">
        <!-- <div class="div-question-area-left-bottom div-ques-logic" style="line-height: 30px">
          <i class="el-icon-setting el-icon&#45;&#45;left el-icon&#45;&#45;right"/><span style="font-size: 14px; color: #666666;"><b>逻辑设置：</b></span>
          <el-link class="ques-relation" @click="handleSetting">问卷设置</el-link>
          <el-link class="ques-relation" @click="handleLimit">限制题目</el-link>
          <el-link class="ques-relation" @click="handleJump">跳题逻辑</el-link>
          <el-link class="ques-relation" @click="handleQuestionRelation">题目关联</el-link>
          <el-link class="ques-relation" @click="handleOptionRelation">选项关联</el-link>
        </div>-->
        <div class="div-question-area-left-bottom div-ques-logic" style="line-height: 30px">
        </div>
        <div class="div-question-area-right-bottom">
          <el-button v-if="status === '-1'" style="width: 100px" icon="el-icon-check" size="mini" @click="handleSave(-1)">保存草稿</el-button>
          <el-button style="width: 100px" type="primary" icon="el-icon-finished" size="mini" @click="handleSave(0)">完成编辑</el-button>
          <el-button  icon="el-icon-check" size="mini" @click="handleSave(1)">完成编辑并发布</el-button>
        </div>
      </div>
      <description-edit-dialog ref="DescriptionEditDialog"/>
      <question-edit-dialog ref="QuestionEditDialog" :question-list="questionList"/>
    </div>
  </el-container>
</template>

<script>
import draggable from 'vuedraggable'
import DescriptionEditDialog from './component/description_edit_dialog'
import QuestionEditDialog from './component/question_edit_dialog'
import { vote_get_api, vote_save_api } from '@/api/vote'

export default {
  components: {
    draggable,
    QuestionEditDialog,
    DescriptionEditDialog
  },
  data() {
    return {
      oprType: '',
      id: '',
      questionnaireTitle: '问卷调查',
      questionnaireDescribe: '调查问卷描述。',
      questionList: [],
      originQuestion: {
        uuid: '',
        title: '',
        details: '',
        type: 'radio',
        must: false,
        imageList: [],
        userDisplay: '0',
        number: '',
        option: '',
        options: []
      },
      fillCheckPulldown: [],
      status: '-1',
      setting: {
      },
      loading: false
    }
  },
  watch: {
    // // 题目发生变化时（新增，删除，上下拖动等），重新刷新题号
    // questionList: function() {
    //   let descCount = 0
    //   this.questionList.forEach((e, index) => {
    //     if (e.type === 'description') {
    //       e.number = ''
    //       descCount++
    //     } else {
    //       const number = index - descCount + 1
    //       e.number = String(number)
    //       if (e.appendQues) {
    //         e.appendQues.forEach((ae, $index) => {
    //           ae.number = number + '.' + ($index + 1)
    //         })
    //       }
    //     }
    //   })
    // }
  },
  created() {
    if (this.$route.path === '/vote/add') {
      this.oprType = 'add'
    } else {
      this.oprType = 'edit'
      this.id = this.$route.params.key
    }
    this.initPanel()
  },
  methods: {
    async initPanel() {
      if (this.oprType === 'add') {
        return
      }
      this.loading = true
      await vote_get_api(this.id).then(resp => {
        const data = resp.value
        this.questionnaireTitle = data.title
        this.questionnaireDescribe = data.describe
        this.status = String(data.status)
        this.questionList = data.questionList || []
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    async handleSave(temp) {
      const self = this
      async function doSave(temp) {
        if (temp===0 && (!self.questionList || self.questionList.length === 0)) {
          self.$message.warning('没有设定题目，无法完成编辑。')
          return
        }
        const param = {
          id: self.id,
          title: self.questionnaireTitle,
          describe: self.questionnaireDescribe,
          status: temp 
        }
        self.loading = true
        await vote_save_api(self.oprType, param, self.questionList).then(res => {
          self.status = param.status
        }).catch(e => {
        }).finally(() => {
          self.loading = false
        })
      }
      if (this.status === '-1' && temp === 0) {
        this.$confirm('完成编辑后该问卷就可以对外发布，发布后将不能够修改，是否继续完成编辑？如果问卷编辑还未完成，请点击【保存草稿】按钮。',
          '提示', {
            confirmButtonText: '确定完成编辑',
            cancelButtonText: '保存草稿',
            closeOnClickModal: false,
            closeOnPressEscape: false,
            showClose: false,
            type: 'warning'
          }).then(async() => {
          await doSave(0)
        }).catch(async() => {
          await doSave(-1)
        })
      } else {
        await doSave(temp)
      }
    },
    handleBack() {
      this.$router.go(-1)
    },
    handleInsert(index, type) { // 插入题目
      const self = this
      let number = this.questionList.length + 1
      if (index !== null) {
        number = index + 2
      }
      if (type !== 'description') {
        const param = {
          index: number,
          number: this.getNumber(index !== null ? index + 1 : this.questionList.length),
          type: type
        }
        this.$refs.QuestionEditDialog.open('add', param, this.fillCheckPulldown, (result) => {
          const question = Object.assign({}, this.originQuestion, result)
          if (index !== null) {
            self.questionList.splice(number - 1, 0, this.$utils.clone(question))
            // self.toPoint(number - 1)
          } else {
            self.questionList.push(this.$utils.clone(question))
            self.toPoint(self.questionList.length - 1)
          }
        })
      } else {
        this.$refs.DescriptionEditDialog.open('', '', (result) => {
          const description = Object.assign({}, self.originQuestion, result)
          if (index !== null) {
            self.questionList.splice(number - 1, 0, self.$utils.clone(description))
            // self.toPoint(number - 1)
          } else {
            self.questionList.push(self.$utils.clone(description))
            self.toPoint(self.questionList.length - 1)
          }
        })
      }
    },
    handleEdit(isAppend, index, appendIndex) {
      let question
      if (!isAppend) {
        question = this.questionList[index]
      } else {
        question = this.questionList[index].appendQues[appendIndex]
      }
      const self = this
      if (question.type !== 'description') {
        const param = {
          index: index + 1,
          appendNumber: appendIndex + 1,
          type: question.type,
          isAppend: isAppend,
          parentQues: isAppend ? this.$utils.clone(this.questionList[index]) : {},
          number: question.number,
          uuid: question.uuid
        }
        const paramQues = Object.assign(param, question)
        const editParam = this.$utils.clone(paramQues)
        this.$refs.QuestionEditDialog.open('edit', editParam, this.fillCheckPulldown, (result) => {
          const question = Object.assign(self.originQuestion, result)
          if (!isAppend) {
            const ques = self.$utils.clone(question)
            const oldQues = self.questionList[index]
            ques.appendQues = oldQues.appendQues
            ques.appendMark = oldQues.appendMark
            ques.appendCondition = oldQues.appendCondition
            self.questionList.splice(index, 1, ques)
          } else {
            self.questionList[index].appendQues.splice(appendIndex, 1, self.$utils.clone(question))
          }
        })
      } else {
        this.$refs.DescriptionEditDialog.open(question.title, question.details, (result) => {
          const description = Object.assign(self.originQuestion, result)
          self.questionList.splice(index, 1, self.$utils.clone(description))
        })
      }
    },
    handleCopy(index) {
      const question = this.questionList[index]
      const paramQues = Object.assign({
        index: this.questionList.length + 1
      },
      question,
      {
        number: this.getNumber(this.questionList.length),
        uuid: this.$utils.uuid()
      })
      const param = this.$utils.clone(paramQues)
      const self = this
      this.$refs.QuestionEditDialog.open('edit', param, this.fillCheckPulldown, (result) => {
        const question = Object.assign(self.originQuestion, result)
        self.questionList.push(this.$utils.clone(question))
        self.toPoint(self.questionList.length - 1)
      })
    },
    handleDelete(isAppend, index, appendIndex) {
      if (!isAppend) {
        this.questionList.splice(index, 1)
      } else {
        this.questionList[index].appendQues.splice(appendIndex, 1)
      }
    },
    handleUp(index) {
      this.$utils.swapArray(this.questionList, index - 1, index)
    },
    handleDown(index) {
      this.$utils.swapArray(this.questionList, index, index + 1)
    },
    handleView() {
      this.$refs.QuestionnaireViewDialog.open({
        id: this.id,
        remote: false,
        questionnaireTitle: this.questionnaireTitle,
        questionnaireDescribe: this.questionnaireDescribe,
        questionList: this.questionList
      })
    },
    handleScroll() {
      this.$refs.quesPanel && this.$refs.quesPanel.forEach(ques => {
        if (ques.$refs.popover) {
          ques.$refs.popover.doClose()
        }
        if (ques.$refs.popover1/* && this.$refs.popover1.popperJS */) {
          ques.$refs.popover1.doClose()
        }
      })
    },
    getNumber(index) {
      // let descCount = 0
      // for (const i in this.questionList) {
      //   if (i < index && this.questionList[i].type === 'description') {
      //     descCount++
      //   }
      // }
      // return String(index - descCount + 1)
      // if (this.questionList.length === 0) {
      //   return '1'
      // }
      return ''
    },
    goBack() {
      this.$confirm('创建问卷还未完成，是否确定退出?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$router.go(-1)
      }).catch(() => { })
    },
    toPoint(index) {
      this.$nextTick(() => {
        const id = '#ques' + (index + 1)
        document.querySelector(id).scrollIntoView(true)
      })
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss">
.div-questionnaire-design {
  .el-card__body {
    padding: 0 !important;
  }
  .el-tabs__nav-scroll {
    padding-left: 28px;
    height: 35px !important;
  }
}
.question-ghost-class {
  opacity: .3;
  color: #fff !important;
  background: #a0afb9 !important;
}
</style>
<style rel="stylesheet/css" lang="css" scoped>

.div-questionnaire-design {
  position: inherit;
  width: 1200px;
  height: calc(100vh - 175px);
}
.div-question-area {
  position: inherit;
  display: inline;
  overflow-y: auto;
}
.div-question-area::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  scrollbar-arrow-color:red;
}
.div-question-area::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: rgba(0,0,0,0.2);
  scrollbar-arrow-color:red;
}
.div-question-area::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0,0,0,0.1);
}

.div-question-area-left {
  height: calc(100vh - 175px);
  float: left;
}

.div-question-area-right {
  height: calc(100vh - 220px);
  float: left;
  margin-left: 20px;
}

.div-question-area-left-bottom {
  line-height: 45px;
  height: 45px;
  width: 450px;
  bottom: 0px;
  vertical-align: center;
  overflow: hidden;
  float: left;
  margin-left: 20px;
}

.div-question-area-right-bottom {
  line-height: 40px;
  height: 40px;
  width: 410px;
  bottom: 0;
  vertical-align: center;
  overflow: hidden;
  text-align: right;
  float: left;
  margin-right: 20px;
  margin-top: 5px;
}

.div-design-area {
  width: 850px;
  padding: 0;
}

.div-ques-logic {
  display: inline-block;
  line-height: 30px;
  height: 30px;
  margin-top: 10px;
  width: 440px;
  background-color: #fbfbfb;
  /*border: 1px dashed #DBDBDB;*/
}
.ques-relation {
  color: #409EFF;
  margin-left: 10px;
  margin-top: -5px;
  font-size: 12px;
  text-decoration: underline;
}
</style>
