<template>
  <el-drag-dialog ref="dialog" title="段落说明编辑">
    <el-form ref="form" :model="form" style="margin-top: 10px">
      <el-form-item label="标题">
        <el-input v-model="form.title" placeholder="请输入段落标题"/>
      </el-form-item>
      <el-form-item label="内容">
        <el-input
          v-model="form.description"
          :autosize="{ minRows: 2, maxRows: 4}"
          type="textarea"
          placeholder="请输入内容"/>
      </el-form-item>
    </el-form>
  </el-drag-dialog>
</template>

<script>
export default {
  name: 'DescriptionEditDialog',
  data() {
    return {
      form: {
        title: '',
        description: ''
      }
    }
  },
  methods: {
    open(title, description, callback) {
      this.form.title = title || ''
      this.form.description = description || ''
      const self = this
      this.$refs.dialog.open((close) => {
        if (self.form.title === '' && self.form.description === '') {
          this.$message.error('标题或者内容必须至少输入一个。')
          return
        }
        const result = {
          title: self.form.title,
          details: self.form.description,
          type: 'description'
        }
        if (callback instanceof Function) {
          callback(result)
        }
        close()
      })
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css" scoped>
  .req {
    color: red;
    font-weight: bold;
    margin-right: 5px;
    padding-top: 5px;
  }
  .split {
    border-left: 2px #ccc solid;
    margin-left: 15px;
    margin-right: 5px;
  }
</style>
