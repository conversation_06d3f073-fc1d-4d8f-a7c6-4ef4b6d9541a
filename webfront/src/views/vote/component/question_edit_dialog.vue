<template>
  <el-drag-dialog ref="dialog" title="问题编辑">
    <el-question-panel
      :index="index"
      :question-list="questionList"
      :must-input="must"
      :question-title="form.questionTitle"
      :type="questionType"
      :show-number="form.number"
      :user-display="userDisplay"
      :is-append="isAppend"
      :append-index="appendIndex"
      :append-mark="appendMark"
      :options="options"
      :cascader-options="cascaderOptions"
      :cascader-placeholder="cascaderPlaceholder"
      :show-toolbar="false"
      style="width: 850px"/>
    <el-form ref="form" :model="form" :rules="rules" style="margin-top: 10px">
      <el-form-item v-if="isAppend && optType === 'add'">
        <div style="border-top: #E0E0E0 1px solid; width: 840px; padding-left: 0">
          <span class="req">*</span>
          <span>当题目【{{ parentQues.number + '. ' + parentQues.title }}】选择下面的选项时，"当前题目"才出现</span>
          <span v-if="showMessage !== ''" style="margin-left: 10px; color: red; font-size: 10px">{{ showMessage }}</span>
        </div>
        <div style="border-bottom: #e0e0e0 1px solid; width: 840px; margin-left: 0; line-height: 50px">
          <div v-for="(option, index) in parentQues.options" :key="index" style="display: inline-block; margin-left: 15px">
            <el-radio v-show="appendedParentOption().indexOf(index) === -1" v-model="appendCondition" :label="index">
              <span v-text="String.fromCharCode(65 + index) + '.' + option.text"/>
            </el-radio>
          </div>
        </div>
      </el-form-item>
      <el-row :gutter="24">
        <el-col :span="4">
          <el-form-item label="题号" prop="number">
            <el-input v-model="form.number" style="width: 70px" class="question-title-edit" placeholder="题号"/>
          </el-form-item>
        </el-col>
        <el-col :span="20">
          <el-form-item label="问题标题" prop="questionTitle">
            <el-input v-model="form.questionTitle" style="width: 600px" class="question-title-edit" placeholder="请输入题目标题内容"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="标题图片">
        <el-switch v-model="addPic"/>
        <el-photo-multi-upload v-if="addPic" :img-data-list.sync="imageList" :size="3"/>
      </el-form-item>
      <el-form-item>
        <el-question-type-select v-model="questionType"/>
        <span class="split"/>
        <span style="margin-left: 10px">必须：</span>
        <el-switch v-model="must"/>
        <!-- <span class="split"/>
        <div v-if="!isAppend && (questionType === 'radio' || questionType === 'checkbox' || isScaleType())" style="display: inline-block">
          <span style="margin-left: 5px">反馈：</span>
          <el-radio-group v-model="userDisplay" size="mini">
            <el-radio-button label="0">不反馈</el-radio-button>
            <el-radio-button label="1">饼状图</el-radio-button>
            <el-radio-button label="2">柱状图</el-radio-button>
          </el-radio-group>
        </div>-->
        <span v-if="questionType === 'singleCompletion'" style="margin-left: 15px">填空类型：</span>
        <el-pulldown v-if="questionType === 'singleCompletion'" v-model="fillboxType" :options="fillCheckPulldown" placeholder="填空类型"/>
        <span v-if="questionType === 'cascader'" style="margin-left: 10px">配额：</span>
        <el-switch v-if="questionType === 'cascader'" v-model="cascaderQuota" :disabled="!cascaderEdit" active-text="有" inactive-text="无"/>
      </el-form-item>
      <el-table v-if="hasOptions()" :data="options" style="width: 850px">
        <el-table-column v-if="!isScaleType()" label="选项" width="50px" align="center">
          <template slot-scope="scope">
            <span>{{ String.fromCharCode(65 + scope.$index) }}.</span>
          </template>
        </el-table-column>
        <el-table-column label="选项文字" width="350px">
          <template slot-scope="scope">
            <el-input v-if="questionType === 'radio' || questionType === 'checkbox' || isScaleType()" v-model="scope.row.text"/>
            <el-fill-box v-else-if="questionType === 'multipleCompletion' && scope.row.fillbox === true" :size="Number(scope.row.fillboxSize) || 15" text=""/>
            <el-input v-else-if="questionType === 'multipleCompletion'" v-model="scope.row.text"/>
          </template>
        </el-table-column>
        <el-table-column width="100px">
          <template slot-scope="scope">
            <el-button icon="el-icon-plus" size="mini" circle @click="handleAdd(scope.$index)"/>
            <el-button
              :disabled="options.length < 2"
              icon="el-icon-minus"
              size="mini"
              circle
              @click="handleDelete(scope.$index)"/>
          </template>
        </el-table-column>
        <!-- <el-table-column v-if="questionType === 'radio' || questionType === 'checkbox'" label="补充说明" width="100px">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.fillbox" @change="changeRadioFillbox(scope.$index)"/>
          </template>
        </el-table-column>-->
        <!-- <el-table-column v-if="questionType === 'multipleCompletion'" label="空格" width="50px">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.fillbox"/>
          </template>
        </el-table-column>-->
        <!-- <el-table-column v-if="questionType === 'multipleCompletion'" label="填空类型" width="100px">
          <template slot-scope="scope">
            <el-pulldown v-if="scope.row.fillbox === true" v-model="scope.row.fillboxType" :options="fillCheckPulldown"/>
          </template>
        </el-table-column>
        <el-table-column v-if="questionType === 'multipleCompletion'" label="空格长度" width="80px">
          <template slot-scope="scope">
            <el-input v-if="scope.row.fillbox === true" v-model="scope.row.fillboxSize"/>
          </template>
        </el-table-column>
        <el-table-column v-if="isScaleType()" label="分数" width="80px">
          <template slot-scope="scope">
            <el-input v-model="scope.row.scale"/>
          </template>
        </el-table-column>-->
        <el-table-column label="上移下移" align="center">
          <template slot-scope="scope">
            <el-button
              :disabled="scope.$index === 0"
              icon="el-icon-top"
              size="mini"
              circle
              @click="handleUp(scope.$index)"/>
            <el-button
              :disabled="scope.$index === options.length - 1"
              icon="el-icon-bottom"
              size="mini"
              circle
              @click="handleDown(scope.$index)"/>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </el-drag-dialog>
</template>

<script>
export default {
  name: 'QuestionEditDialog',
  props: {
    questionList: {
      type: Array,
      required: true
    }
  },
  data() {
    const checkNumber = (rule, value, callback) => {
      if (this.form.number === null || this.form.number.trim() === '') {
        callback('必须输入题号')
      } else {
        callback()
      }
    }
    // const checkTitle = (rule, value, callback) => {
    //   if (this.form.number === '') {
    //     callback('必须输入题号')
    //     return
    //   }
    //   if (this.form.questionTitle === '') {
    //     callback('必须输入标题内容')
    //     return
    //   }
    //   callback()
    // }
    return {
      index: 1,
      uuid: '',
      addPic: false,
      form: {
        number: '',
        questionTitle: '问题标题'
      },
      rules: {
        number: [
          { required: true, message: '必须输入问题题号', validator: checkNumber, trigger: ['blur'] }
        ],
        questionTitle: [
          { required: true, message: '必须输入问题标题', trigger: ['blur'] }
        ]
      },
      cascaderPlaceholder: '请选择多级下拉选项',
      questionType: '',
      must: true,
      cascaderQuota: false,
      cascaderEdit: false,
      userDisplay: '0',
      optType: '',
      options: [
      ],
      cascaderOptions: [],
      imageList: [],
      originOptions: [
        { text: '选项1', default: '0', fillbox: false, jump: '', relationOptions: '' },
        { text: '选项2', default: '0', fillbox: false, jump: '', relationOptions: '' }
      ],
      multipleCompletionOriginOptions: [
        { text: '您的家庭住址是：', default: '0', fillbox: false },
        { text: '', default: '0', fillbox: true, fillboxSize: 30, fillboxType: null },
        { text: '，您的工作年限是：', default: '0', fillbox: false },
        { text: '', default: '0', fillbox: true, fillboxSize: 4, fillboxType: null },
        { text: '年。', default: '0', fillbox: false }
      ],
      scaleRatingOptions: [
        { text: '很不满意', default: '0', scale: 1, fillbox: false, jump: '', relationOptions: '' },
        { text: '2', default: '0', scale: 2, fillbox: false, jump: '', relationOptions: '' },
        { text: '3', default: '0', scale: 3, fillbox: false, jump: '', relationOptions: '' },
        { text: '4', default: '0', scale: 4, fillbox: false, jump: '', relationOptions: '' },
        { text: '非常满意', default: '0', scale: 5, fillbox: false, jump: '', relationOptions: '' }
      ],
      npsScaleRatingOptions: [
        { text: '不可能', default: '0', scale: 1, fillbox: false, jump: '', relationOptions: '' },
        { text: '2', default: '0', scale: 2, fillbox: false, jump: '', relationOptions: '' },
        { text: '3', default: '0', scale: 3, fillbox: false, jump: '', relationOptions: '' },
        { text: '4', default: '0', scale: 4, fillbox: false, jump: '', relationOptions: '' },
        { text: '5', default: '0', scale: 5, fillbox: false, jump: '', relationOptions: '' },
        { text: '6', default: '0', scale: 6, fillbox: false, jump: '', relationOptions: '' },
        { text: '7', default: '0', scale: 7, fillbox: false, jump: '', relationOptions: '' },
        { text: '8', default: '0', scale: 8, fillbox: false, jump: '', relationOptions: '' },
        { text: '9', default: '0', scale: 9, fillbox: false, jump: '', relationOptions: '' },
        { text: '极有可能', default: '0', scale: 10, fillbox: false, jump: '', relationOptions: '' }
      ],
      isAppend: '',
      parentQues: {},
      appendCondition: -1,
      appendIndex: 1,
      appendMark: '',
      fillboxType: '',
      callback: null,
      showMessage: '',
      cascaderText: '',
      fillCheckPulldown: []
    }
  },
  watch: {
    questionType(nv) {
      if (!this.options || this.options.length === 0) {
        if (nv === 'multipleCompletion') {
          this.options = this.$utils.clone(this.multipleCompletionOriginOptions)
        } else if (nv === 'scaleRating') {
          this.options = this.$utils.clone(this.scaleRatingOptions)
        } else if (nv === 'npsScaleRating') {
          this.options = this.$utils.clone(this.npsScaleRatingOptions)
        } else {
          this.options = this.$utils.clone(this.originOptions)
        }
      }
    },
    appendCondition(nv) {
      if (nv !== -1 && this.isAppend) {
        const index = Number(nv) || 0
        this.appendMark = String.fromCharCode(65 + index) + '.' + this.parentQues.options[index].text
        this.showMessage = ''
      } else {
        this.appendMark = ''
      }
    }
  },
  methods: {
    open(optType, param, fillCheckPulldown, callback) {
      this.optType = optType
      this.fillCheckPulldown = fillCheckPulldown || []
      this.questionType = param.type || 'radio'
      this.reset(optType, param, callback)
      const self = this
      this.$refs.dialog.open((close) => {
        self.$refs.form.validate((valid) => {
          if (valid) {
            const result = {
              uuid: self.uuid,
              title: self.form.questionTitle,
              type: self.questionType,
              number: self.form.number,
              options: self.options,
              imageList: self.imageList,
              // userDisplay: self.userDisplay,
              must: self.must
              // isAppend: self.isAppend,
              // appendMark: self.appendMark,
              // appendCondition: self.appendCondition,
              // cascaderText: self.cascaderText,
              // cascaderQuota: self.cascaderQuota,
              // fillboxType: self.fillboxType,
              // cascaderPlaceholder: self.cascaderPlaceholder,
              // cascaderOptions: self.cascaderOptions
            }
            if (self.callback instanceof Function) {
              self.callback(result)
            }
            close()
          }
        })
      })
    },
    isScaleType() {
      return this.$utils.isScaleType(this.questionType)
    },
    handleAdd(index) {
      this.options.splice(index + 1, 0, { text: '选项文字', default: '0', scale: '', fillbox: false, fillboxSize: 15, fileboxType: 'text' })
    },
    handleDelete(index) {
      this.options.splice(index, 1)
    },
    handleUp(index) {
      this.$utils.swapArray(this.options, index - 1, index)
    },
    handleDown(index) {
      this.$utils.swapArray(this.options, index, index + 1)
    },
    changeRadioFillbox(index) {
      this.options[index].fillbox === true && this.options.forEach((o, i) => {
        if (index !== i) {
          o.fillbox = false
        }
      })
    },
    hasOptions() {
      return this.questionType !== 'singleCompletion' && this.questionType !== 'content' && this.questionType !== 'cascader'
    },
    handleRequest(uploadFile) {
      this.handleImport(uploadFile.file)
    },
    handleImport(file) {
      // cascader_excel_upload_api(file).then(res => {
      //   this.cascaderEdit = true
      //   this.cascaderText = res.value || ''
      //   this.$message.success('Excel文件读取成功。')
      // }).catch(e => {
      // })
    },
    reset(optType, param, callback) {
      if (optType === 'add') {
        this.uuid = this.$utils.uuid()
      } else {
        this.uuid = param.uuid
      }
      this.index = param.index
      this.callback = callback || function() {}
      this.questionType = param.type || 'radio'
      this.form.questionTitle = param.title || '问题标题'
      this.must = param.must || true
      this.form.number = param.number || ''

      this.isAppend = param.isAppend || false
      this.parentQues = param.parentQues || {}
      // this.appendIndex = param.appendNumber || 1
      // this.appendCondition = param.appendCondition || -1
      // this.appendMark = param.appendMark || ''
      this.fillboxType = param.fillboxType || ''
      this.imageList = param.imageList || []
      // this.userDisplay = param.userDisplay || '0'
      // this.cascaderQuota = param.cascaderQuota || false
      // this.cascaderText = param.cascaderText || ''
      this.showMessage = ''

      if (this.questionType !== 'cascader') {
        if (optType === 'add') {
          if (this.questionType === 'multipleCompletion') {
            this.options = this.$utils.clone(this.multipleCompletionOriginOptions)
          } else if (this.questionType === 'scaleRating') {
            this.options = this.$utils.clone(this.scaleRatingOptions)
          } else if (this.questionType === 'npsScaleRating') {
            this.options = this.$utils.clone(this.npsScaleRatingOptions)
          } else {
            this.options = this.$utils.clone(this.originOptions)
          }
        } else {
          this.options = this.$utils.clone(param.options)
        }
      }

      // if (this.questionType === 'cascader') {
      //   this.handleCascaderSave(true)
      // }
    },
    appendedParentOption() {
      if (this.parentQues && this.parentQues.appendQues) {
        const show = []
        this.parentQues.appendQues.forEach(a => {
          show.push(Number(a.appendCondition))
        })
        return show
      }
      return []
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css">
  .question-title-edit .el-input-group__prepend {
    background-color: #fff;
    padding: 0 !important;
  }
  .question-title-edit-append .el-input__inner{
    border: 0;
    text-align: center;
    padding: 0 !important;
  }
</style>

<style rel="stylesheet/css" lang="css" scoped>
  .req {
    color: red;
    font-weight: bold;
    margin-right: 5px;
    padding-top: 5px;
  }
  .split {
    border-left: 2px #ccc solid;
    margin-left: 15px;
    margin-right: 5px;
  }
  .cascader-tips {
    margin: -10px 5px 5px 5px;
    font-weight: bold;
    color: darkgrey;
    line-height: 30px;
    height: 30px;
  }
  .cascader-tips_button {
    margin-bottom: 5px;
    margin-left: 5px;
    float: right;
    margin-right: 5px
  }
</style>
