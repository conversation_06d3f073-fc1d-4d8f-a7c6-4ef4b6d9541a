<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-row>
          <el-col :span="8">
            <el-form-item prop="name" label="姓名">
              <el-input v-model="form.name" style="width: 150px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="idCardNo"  label="身份证号">
              <el-input v-model="form.idCardNo" style="width: 150px"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar
        :add-show="false"
        :delete-show="false"
        :export-show="true"
        :chart-show="true"
        :sort-column="sortColumn"
        :title-text="'用户统计'"
        title-show
        @handleExport="handleExport"
        @showChartsDialog ='openEchatsDialog'/>
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="selection" width="45"/>
        <el-table-column prop="name" label="姓名" width="80" sortable="custom"/>
        <el-table-column prop="idCardNo" label="身份证号" width="160" sortable="custom"/>
        <el-table-column prop="sex" label="性别" width="80" />
        <el-table-column prop="age" label="年龄" width="80" />
        <el-table-column prop="tel" label="手机号" width="120" />
        <el-table-column prop="pcdName" label="省/市/区" width="180" />
        <el-table-column prop="adoptCount" label="认养总次数" width="120" sortable="custom"/>
        <el-table-column prop="adoptRate" label="认养履约率" >
          <template v-slot="prop">
            {{ prop.row.adoptRate }} %
          </template>
        </el-table-column>
      </el-pagination-table>
    </el-main>
     <echart-dialog ref="myEchart" title-text="用户统计">
    </echart-dialog>
  </el-container>
</template>

<script>
import {list_user_adopt_api, list_user_adopt_export_api} from '@/api/statistics'
export default {
  name: 'StatisticsUserAdoptMain',
  data() {
    return {
      loading: false,
      form: {
        name: '',
        idCardNo: '',
      },
      dataList: [],
      sortColumn: {},
      pagination: {
        pageNumber: 1
      },
      sortMapping: {
        name: 'name',
        idCardNo: 'id_card_no',
        adoptCount: 'adopt_count',
      }
    }
  },
  created() {
    this.handleSearch()
  },
  methods: {
    handleSearch() {
      this.loading = true
      list_user_adopt_api(this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        this.dataList = res.value
        this.pagination = res.pagination
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    async handleExport() {
      this.loading = true
      await list_user_adopt_export_api(this.form).then(res => {
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
    },
    handleMultiDelete() {
    },
    openEchatsDialog() {
      let data = this.dataList;
      if (data.length > 0) {
        let xAxis = [];
        let series = [
          {
            name: "认养总次数",
            data: [],
            showBackground: true,
            center: ['25%', '50%'],
          },
          {
            name: "认养覆盖率",
            data: [],
            yAxisIndex: 1,
            showBackground: true,
            center: ['75%', '50%'],
          }
        ];
        let legend = [
          "认养总次数", "认养覆盖率"
        ]

        let pieTitle = [
          {
            subtext: "认养总次数",
            subtextStyle: {
              color: '#5470c6'
            },
            textAlign: 'center',
            left: '25%',
            top: '75%'
          },
          {
            subtext: "认养覆盖率",
            left: '75%',
             top: '75%',
            subtextStyle: {
              color: '#5470c6'
            },
            textAlign: 'center'
          }
        ]

        let yAxis = [
          {
            name: "次数",
            position: 'left',
            minInterval: 1
          },
          {
            name: "覆盖率",
            position: 'right',
            axisLabel: {
              formatter: '{value}%'
            },
          }
        ]
        
        //横坐标处理
        data.forEach((item) => {
          let nameTag = `${item.name}[${item.tel}]`
          xAxis.push(nameTag)
          series[0].data.push({ name: nameTag, value: item.adoptCount });
          series[1].data.push({ name: nameTag, value: item.adoptRate });
        })

       this.$refs.myEchart.openWithInit([{legend, xAxis, yAxis, series, pieTitle}]);

      }
    }
  }
}
</script>
