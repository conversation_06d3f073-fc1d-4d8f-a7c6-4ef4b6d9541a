<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-row>
          <el-col :span="8">
            <el-form-item prop="areaType" label="统计维度">
              <el-pulldown v-model="form.areaType" :options="areaTypeOptions"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar
        :add-show="false"
        :delete-show="false"
        :export-show="true"
        :sort-column="sortColumn"
        :title-text="'座椅统计'"
        :chart-show="true"
        title-show
        @handleExport="handleExport"
        @showChartsDialog ='openEchatsDialog'/>
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="selection" width="45"/>
        <el-table-column prop="pcdName" label="省/市/区/街道" width="200" />
        <el-table-column prop="seatCount" label="座椅总数" width="120" sortable="custom"/>
        <el-table-column prop="donateCountTotal" label="捐赠总数" width="120" sortable="custom"/>
        <el-table-column prop="donateRate" label="捐赠率" width="120" >
          <template v-slot="prop">
            {{ prop.row.donateRate }} %
          </template>
        </el-table-column>
        <el-table-column prop="emptyRate" label="空置率" width="120" >
          <template v-slot="prop">
            {{ prop.row.emptyRate }} %
          </template>
        </el-table-column>
        <el-table-column prop="donateCountTotal" label="付款总次数" width="140" sortable="custom"/>
        <el-table-column prop="payedPriceTotal" label="付款总金额" sortable="custom"/>
      </el-pagination-table>
    </el-main>
     <echart-dialog ref="myEchart" title-text="座椅统计">
    </echart-dialog>
  </el-container>
</template>

<script>
import {list_seat_api, list_seat_export_api} from '@/api/statistics'
export default {
  name: 'StatisticsSeatMain',
  data() {
    return {
      loading: false,
      form: {
        areaType:'4',
        sortType:''
      },
      areaTypeOptions:[],
      sortTypeOptions:[],
      dataList: [],
      sortColumn: {},
      pagination: {
        pageNumber: 1
      },
      sortMapping: {
        seatCount: 'seat_count',
        donateCountTotal: 'donate_count_total',
        payedPriceTotal: 'payed_price_total',
      }
    }
  },
  created() {
    this.handleSearch(true)
  },
  methods: {
    handleSearch(init) {
      this.loading = true
      list_seat_api(init, this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        this.dataList = res.value
        this.pagination = res.pagination
        if (init === true) {
          this.areaTypeOptions = res.data.areaTypeOptions
        }
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    async handleExport() {
      this.loading = true
      await list_seat_export_api(this.form).then(res => {
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
    },
    handleMultiDelete() {
    },
    initCharts(name1,name2,yAxisName1,yAxisName2,seriesName1,seriesName2){
      let data = this.dataList;
      if (data.length > 0) {
        let xAxis = [];
        let series = [
          {
            name: name1,
            data: [],
            showBackground: true,
            center: ['25%', '50%'],
          },
          {
            name: name2,
            data: [],
            yAxisIndex: 1,
            showBackground: true,
            center: ['75%', '50%']
          }
        ];
        let legend = [
          name1,name2
        ]

        let pieTitle = [
          {
            subtext: name1,
            subtextStyle: {
              color: '#5470c6'
            },
            textAlign: 'center',
            left: '25%',
            top: '75%'
          },
          {
            subtext:name2,
            left: '75%',
            top: '75%',
            subtextStyle: {
              color: '#5470c6'
            },
            textAlign: 'center'
          },
        ]

        let yAxis = [
          {
            name: yAxisName1,
            position: 'left',
            minInterval: 1
          },
          {
            name: yAxisName2,
            position: 'right',
          },
        ]
        
        //横坐标处理
        data.forEach((item) => {
          let nameTag = `${item.strict}/${item.street}`
          xAxis.push(nameTag)
          series[0].data.push({ name: nameTag, value: item[seriesName1] });
          series[1].data.push({ name: nameTag, value: item[seriesName2] });
        })
        return {legend, xAxis, yAxis, series, pieTitle};
      }
    },
    openEchatsDialog() {
      let charts1 = this.initCharts("座椅总数","捐赠总数","总数","总数","seatCount","donateCountTotal")
      let charts2 = this.initCharts("捐赠率","空置率","捐赠率","空置率","donateRate","emptyRate")
      let charts3 = this.initCharts("付款总次数","付款总金额","总次数","总金额","donateCountTotal","payedPriceTotal")

      this.$refs.myEchart.openWithInit([charts1,charts2,charts3]);

    }
  }
}
</script>
