<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-row>
          <el-col :span="8">
            <el-form-item prop="areaType" label="统计维度">
              <el-pulldown v-model="form.areaType" :options="areaTypeOptions"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar
        :add-show="false"
        :delete-show="false"
        :export-show="true"
        :chart-show="true"
        :sort-column="sortColumn"
        :title-text="'座椅统计'"
        title-show
        @handleExport="handleExport"
        @showChartsDialog ='openEchatsDialog'/>
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="selection" width="45"/>
        <el-table-column prop="pcdName" label="省/市/区/街道" width="200" />
        <el-table-column prop="adoptRate" label="认养率" width="120" >
          <template v-slot="prop">
            {{ prop.row.adoptRate }} %
          </template>
        </el-table-column>
        <el-table-column prop="emptyRate" label="空置率" width="120" >
          <template v-slot="prop">
            {{ prop.row.emptyRate }} %
          </template>
        </el-table-column>
        <el-table-column prop="adoptCountTotal" label="认养总数" sortable="custom"/>
      </el-pagination-table>
    </el-main>
     <echart-dialog ref="myEchart" title-text="座椅统计">
    </echart-dialog>
  </el-container>
</template>

<script>
import {list_seat_adopt_api, list_seat_adopt_export_api} from '@/api/statistics'
export default {
  name: 'StatisticsSeatAdoptMain',
  data() {
    return {
      loading: false,
      form: {
        areaType:'4',
        sortType:''
      },
      areaTypeOptions:[],
      sortTypeOptions:[],
      dataList: [],
      sortColumn: {},
      pagination: {
        pageNumber: 1
      },
      sortMapping: {
        adoptCountTotal: 'adopt_count_total',
      }
    }
  },
  created() {
    this.handleSearch(true)
  },
  methods: {
    handleSearch(init) {
      this.loading = true
      list_seat_adopt_api(init, this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        this.dataList = res.value
        this.pagination = res.pagination
        if (init === true) {
          this.areaTypeOptions = res.data.areaTypeOptions
          this.sortTypeOptions = res.data.sortTypeOptions
        }
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    async handleExport() {
      this.loading = true
      await list_seat_adopt_export_api(this.form).then(res => {
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
    },
    handleMultiDelete() {
    },
     openEchatsDialog() {
      let data = this.dataList;
      if (data.length > 0) {
        let xAxis = [];
        let series = [
          {
            name: "认养率",
            data: [],
            showBackground: true,
            radius: '90',
            center: ['25%', '32%']
          },
          {
            name: "空置率",
            data: [],
            yAxisIndex: 1,
            showBackground: true,
            radius: '90',
            top: '50%',
            bottom: '100'
          },
          {
            name: "认养总数",
            data: [],
            yAxisIndex: 2,
            showBackground: true,
            radius: '90',
            center: ['75%', '32%']
          }
        ];
        let legend = [
           "认养率",  "空置率", "认养总数"
        ]

        let pieTitle = [
          {
            subtext: "认养率",
            subtextStyle: {
              color: '#5470c6'
            },
            textAlign: 'center',
            left: '24.5%',
            top: '250'
          },
          {
            subtext: "空置率",
            left: '50%',
            top: '85%',
            subtextStyle: {
              color: '#5470c6'
            },
            textAlign: 'center'
          },
          {
            subtext: "捐认养总数",
            subtextStyle: {
              color: '#5470c6'
            },
            left: '75%',
            top: '250',
            textAlign: 'center'
          }
        ]

        let yAxis = [
          {
            name: "认养率",
            position: 'left',
            axisLabel: {
              formatter: '{value}%'
            },
            minInterval: 1
          },
          {
            name: "空置率",
            axisLabel: {
              formatter: '{value}%'
            },
            position: 'right',
          },
          {
            name: "总数",
            position: 'right',
            offset: 50,
            max: 100,
            min: 0
          }
        ]
        //横坐标处理
        data.forEach((item) => {
          let nameTag = `${item.strict}/${item.street}`
          xAxis.push(nameTag)
          series[0].data.push({ name: nameTag, value: item.adoptRate });
          series[1].data.push({ name: nameTag, value: item.emptyRate });
          series[2].data.push({ name: nameTag, value: item.adoptCountTotal });
        })

        this.$refs.myEchart.openWithInit([{legend, xAxis, yAxis, series, pieTitle}]);

      }
    }
  }
}
</script>
