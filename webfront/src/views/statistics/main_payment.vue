<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-row>
          <el-col :span="8">
            <el-form-item prop="areaType" label="统计维度">
              <el-pulldown v-model="form.areaType" :options="areaTypeOptions"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar
        :add-show="false"
        :delete-show="false"
        :export-show="true"
        :chart-show="true"
        :sort-column="sortColumn"
        :title-text="'款项统计'"
        title-show
        @handleExport="handleExport"
        @showChartsDialog ='openEchatsDialog'/>
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="selection" width="45"/>
        <el-table-column prop="pcdName" label="省/市/区/街道" width="200" />
        <el-table-column prop="donateCountTotal" label="付款总次数" width="140" sortable="custom"/>
        <el-table-column prop="payedPriceTotal" label="付款总金额" sortable="custom"/>
      </el-pagination-table>
    </el-main>
    <echart-dialog ref="myEchart" title-text="款项统计">
    </echart-dialog>
  </el-container>
</template>

<script>
import {list_payment_api, list_payment_export_api} from '@/api/statistics'
export default {
  name: 'StatisticsPaymentMain',
  data() {
    return {
      loading: false,
      form: {
        areaType:'4',
        sortType:''
      },
      areaTypeOptions:[],
      sortTypeOptions:[],
      dataList: [],
      sortColumn: {},
      pagination: {
        pageNumber: 1
      },
      sortMapping: {
        donateCountTotal: 'donate_count_total',
        payedPriceTotal: 'payed_price_total',
      }
    }
  },
  created() {
    this.handleSearch(true)
  },
  methods: {
    handleSearch(init) {
      this.loading = true
      list_payment_api(init, this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        this.dataList = res.value
        this.pagination = res.pagination
        if (init === true) {
          this.areaTypeOptions = res.data.areaTypeOptions
          this.sortTypeOptions = res.data.sortTypeOptions
        }
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    async handleExport() {
      this.loading = true
      await list_payment_export_api(this.form).then(res => {
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
    },
    handleMultiDelete() {
    },
    openEchatsDialog() {
      let data = this.dataList;
      if (data.length > 0) {
        let xAxis = [];
        let series = [
          {
            name: "付款总次数",
            data: [],
            showBackground: true,
            center: ['25%', '50%'],
          },
          {
            name: "付款总金额",
            data: [],
            yAxisIndex: 1,
            showBackground: true,
            center: ['75%', '50%'],
          }
        ];
        let legend = [
          "付款总次数", "付款总金额"
        ]

        let pieTitle = [
          {
            subtext: "付款总次数",
            subtextStyle: {
              color: '#5470c6'
            },
            textAlign: 'center',
            left: '25%',
            top: '75%'
          },
          {
            subtext: "付款总金额",
            left: '75%',
            top: '75%',
            subtextStyle: {
              color: '#5470c6'
            },
            textAlign: 'center'
          }
        ]

        let yAxis = [
          {
            name: "次数",
            position: 'left',
            minInterval: 1
          },
          {
            name: "金额",
            position: 'right',
          }
        ]
        
        //横坐标处理
        data.forEach((item) => {
          let nameTag = `${item.strict}/${item.street}`
          xAxis.push(nameTag)
          series[0].data.push({ name: nameTag, value: item.seatCount });
          series[1].data.push({ name: nameTag, value: item.payedPriceTotal});
        })

       this.$refs.myEchart.openWithInit([{legend, xAxis, yAxis, series, pieTitle}]);

      }
    }
  }
}
</script>
