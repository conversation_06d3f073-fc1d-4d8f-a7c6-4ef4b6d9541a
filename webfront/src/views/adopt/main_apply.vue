<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-row>
          <el-col :span="8">
            <el-form-item prop="seatNo" label="捐赠编号">
              <el-input v-model="form.donateNo" style="width: 150px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="status"  label="发布状态">
              <el-pulldown v-model="form.status" :options="statusOptions"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="name" label="姓名">
              <el-input v-model="form.name" style="width: 150px"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar
        :add-show="false"
        :delete-show="false"
        :export-show="true"
        :sort-column="sortColumn"
        :title-text="'认养申请反馈'"
        title-show
        @handleExport="handleExport"/>
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="selection" width="45"/>
        <el-table-column prop="donateNo" label="捐赠No" width="100" sortable="custom"/>
        <el-table-column prop="seatNo" label="座椅编号" width="100" sortable="custom"/>
        <el-table-column prop="seatName" label="座椅名称" width="140" />
        <el-table-column prop="seatPrice" label="价格" width="80" />
        <el-table-column prop="pcdName" label="省/市/县/街道" width="160" />
        <el-table-column prop="name" label="姓名" width="80" sortable="custom"/>
        <el-table-column prop="idCardNo" label="身份证号" width="160" sortable="custom"/>
        <el-table-column prop="applyTime" label="申请时间" width="170" sortable="custom"/>
        <el-table-column prop="applyPassTime" label="申请通过时间" width="170" sortable="custom"/>
        <el-table-column prop="status" label="状态" width="90" sortable="custom">
          <template slot-scope="prop">
            <el-tag type="primary">{{ prop.row.statusText }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="" fixed="right" width="400px">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-edit" class="mini-btn" @click="handleEdit(scope.$index, scope.row)">详情</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            <el-button size="mini" v-if="scope.row.status == 2" type="primary" icon="el-icon-folder-add" class="mini-btn" @click="uploadCertificate(scope.$index, scope.row)">证书上传</el-button>
            <el-button size="mini" v-if="scope.row.status == 2" type="primary" icon="el-icon-folder-add" class="mini-btn" @click="showCertificate(scope.$index, scope.row)">证书查看</el-button>
          </template>
        </el-table-column>
      </el-pagination-table>
    </el-main>
  </el-container>
</template>

<script>
import {adopt_apply_export_api, delete_apply_api, list_apply_init_api} from '@/api/adopt'
export default {
  name: 'AdoptApplyMain',
  data() {
    return {
      loading: false,
      form: {
        adoptId:'',
        donateNo: '',
        status: '',
        name:''
      },
      statusOptions:[],
      dataList: [],
      sortColumn: {},
      pagination: {
        pageNumber: 1
      },
      sortMapping: {
        donateNo: 'di.donate_no',
        seatNo: 'si.seat_no',
        name: 'aa.name',
        idCardNo: 'aa.id_card_no',
        applyTime: 'aa.apply_time',
        applyPassTime: 'aa.apply_pass_time',
        status: 'aa.status',
      }
    }
  },
  created() {
    this.form.adoptId = this.$route.params.key
    this.handleSearch(true)
  },
  methods: {
    handleSearch(init) {
      this.loading = true
      list_apply_init_api(init, this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        if (init === true) {
          this.statusOptions = res.data.statusOptions
        }
        this.dataList = res.value
        this.pagination = res.pagination
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    async handleExport() {
      this.loading = true
      await adopt_apply_export_api(this.form).then(res => {
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
      this.form.donateNo = ''
      this.form.status = ''
      this.form.name = ''
      this.pagination.pageNumber=0
      this.handleSearch();
    },
    handleEdit(index, row) {
      this.$router.push('/seats-adopt/edit_apply/'+ row.id)
    },
    handleMultiDelete() {
    },
    handleDelete(index, row) {
      const self = this
      this.$confirm('是否要删除认养申请记录').then(res => {
        delete_apply_api(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
    uploadCertificate(index,row){
      let params  = {
        seatType:2,
        applyId:row.id,
        openId:row.openId
      }

      this.$router.push({ path: '/other/certificate/add', query: { seatInfo: params }});
    },
    showCertificate(index,row){
      let params  = {
        applyId:row.id,
        openId:row.openId,
        seatType:'2'
      }
      this.$router.push({ path: '/other/certificate', query: { seatInfo: params }});
    }
  }
}
</script>
