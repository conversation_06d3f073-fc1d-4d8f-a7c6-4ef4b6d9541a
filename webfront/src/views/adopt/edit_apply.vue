<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack"/>
    <el-main v-loading="loading">
      <el-form ref="form" :model="form" :rules="validRules" label-width="100px">
        <el-card shadow="never" style="width: 1000px">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <div>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="donateNo" label="捐赠编号">
                  {{form.donateNo}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatNo" label="座椅No">
                  {{form.seatNo}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatName" label="座椅名称">
                  {{form.seatName}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatImageUrl" label="座椅图片">
                  <el-image :src="form.seatImageUrl" :preview-src-list="[form.seatImageUrl]"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatPrice" label="座椅价格">
                  {{form.seatPrice}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatAdress" label="详细地址">
                  <el-geo-coordinates disabled="true"
                    :gps-lng.sync="form.seatGpsLng"
                    :gps-lat.sync="form.seatGpsLat"
                    :address.sync="form.seatAdress"
                    :province.sync="form.seatProvince"
                    :city.sync="form.seatCity"
                    :strict.sync="form.seatStrict"
                    :street.sync="form.seatStreet"
                    style="width: 500px;"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item prop="seatProvince" label="省市县街道">
                  {{form.seatProvince}}/{{form.seatCity}}/ {{form.seatStrict}}/ {{form.seatStreet}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="name" label="姓名">
                  {{form.name}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="idCardNo" label="身份证号">
                  {{form.idCardNo}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="sex" label="性别">
                  {{ form.sex === 1 ? '男' : '女' }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="age" label="年龄">
                  {{form.age}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="tel" label="手机号">
                  {{form.tel}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item prop="province" label="省市县">
                  {{form.province}}/{{form.city}}/ {{form.strict}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="channel" label="了解渠道">
                  <el-input v-model="form.channel" style="width: 200px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="reason" label="认养原因">
                  <el-input v-model="form.reason" type="textarea" style="width: 630px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="donationWord" label="寄语">
                  <el-input v-model="form.donationWord" type="textarea" style="width: 630px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="donationWord" label="寄语显示">
                  <el-switch
                    v-model="form.donationWordFlag"
                    active-color="#13ce66"
                    inactive-color="#ff4949">
                  </el-switch>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="applyTime" label="申请时间">
                  <el-date-picker v-model="form.applyTime" disabled="true" type="datetime" format="" placeholder="请选择"> </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="applyPassTime" label="申请通过时间">
                  <el-date-picker v-if="form.status === 2" v-model="form.applyPassTime" disabled="true" type="datetime" format="" placeholder="请选择"> </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="validTime" label="到期时间">
                  <el-date-picker v-if="form.status === 2" v-model="form.validTime" disabled="true" type="datetime" default-time="23:59:59" format="" placeholder="请选择"> </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="never" style="width: 1000px; margin-top: 10px">
          <div style="text-align: right">
            <el-loading-button type="" style="width: 100px" @click="handleBack">取消</el-loading-button>
            <el-loading-button style="width: 100px" @click="handleSave">保存</el-loading-button>
            <el-loading-button type="success" v-if="form.status === 1" style="width: 100px" @click="done => handleSaveStatus(done,2)">审核通过</el-loading-button>
            <el-loading-button type="danger" v-if="form.status === 1" style="width: 100px" @click="done => handleSaveStatus(done,3)">审核不通过</el-loading-button>
            <el-loading-button type="info" v-if="form.status === 2" style="width: 100px" @click="done => handleSaveStatus(done,4)">无效</el-loading-button>
          </div>
        </el-card>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import { adopt_apply_init_api,adopt_apply_save_api, adopt_apply_save_status_api } from '@/api/adopt'
export default {
  name: 'AdoptApplyEdit',
  data() {
    return {
      isEdit: true,
      loading: false,
      optType: null,
      form: {
        donateNo: '',
        seatNo:'',
        seatName: '',
        seatImageUrl:'',
        seatPrice: '',
        seatProvince: '',
        seatCity: '',
        seatStrict: '',
        seatStreet: '',
        seatAdress: '',
        seatGeoPoint:[],
        name:'',
        idCardNo:'',
        sex:'',
        age:'',
        tel:'',
        province: '',
        city: '',
        strict: '',
        type:'',
        personCertificateImage:'',
        aptitudeCertificateImage:'',
        channel:'',
        reason:'',
        donationWord:'',
        applyTime:'',
        applyPassTime:'',
        validTime: '',
        status:''
      },
      validRules: {
      }
    }
  },
  computed: {
  },
  created() {
    this.init(this.$route.params.key)
  },
  methods: {
    init(id) {
      this.loading = true
      adopt_apply_init_api(id).then(res => {
        Object.assign(this.form, res.value)
        this.form.seatGeoPoint = [res.value.seatGpsLng, res.value.seatGpsLat]
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    handleBack() {
      this.$router.go(-1)
    },
    handleSave(done) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          adopt_apply_save_api(this.optType, this.form).then(res => {
            // this.$router.push('/seats-adopt/edit_apply')
          }).catch(e => {
          }).finally(() => {
            this.loading = false
            done()
          })
        } else {
          done()
        }
      })
    },
    handleSaveStatus(done, status) {

      this.loading = true
      this.$confirm('请确认是否继续此操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        adopt_apply_save_status_api(this.form.id, status).then(res => {
          // this.$router.push('/seats-adopt/apply')
        }).catch(e => {
        }).finally(() => {
          this.$message({
            type: 'success',
            message: '修改成功!'
          })
          this.loading = false
          done()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
        this.loading = false
        done()
      });
    }
  }
}
</script>
