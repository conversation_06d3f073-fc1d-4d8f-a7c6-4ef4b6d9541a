<template>
  <el-container>
    <el-search-header :search-loading.sync="loading" :handle-search="handleSearch" @reset="resetCondition">
      <el-form :model="form">
        <el-row>
          <el-col :span="8">
            <el-form-item prop="adoptNo" label="认养编号">
              <el-input v-model="form.adoptNo" style="width: 150px"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="donateNo" label="捐赠编号">
              <el-input v-model="form.donateNo" style="width: 150px"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-upload
        :action="'/rest/import/adopt'"
        :show-file-list="false"
        class="upload-demo"
        accept=".xlsx,.xls"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-error="handleError">
        <el-button size="small" type="success">Excel导入</el-button>
      </el-upload>
    </el-search-header>
    <el-main v-loading="loading">
      <el-table-toolbar
        :delete-show="false"
        :export-show="true"
        :sort-column="sortColumn"
        @handleAddItem="handleAdd"
        @handleDeleteMultiItems="handleMultiDelete"
        :title-text="'认养信息'"
        @handleExport="handleExport"/>
      <el-pagination-table
        :data="dataList"
        :pagination.sync="pagination"
        :sort-column.sync="sortColumn"
        :search-function="handleSearch"
        :search-loading.sync="loading">
        <el-table-column type="selection" width="45"/>
        <el-table-column prop="donateNo" label="捐赠No" width="100" sortable="custom"/>
        <el-table-column prop="adoptNo" label="认养No" width="100" sortable="custom"/>
        <el-table-column prop="seatNo" label="座椅编号" width="100" sortable="custom"/>
        <el-table-column prop="seatName" label="座椅名称" width="140" />
        <el-table-column prop="seatPrice" label="价格" width="80" />
        <el-table-column prop="pcdName" label="省/市/县/街道" width="160" />
        <el-table-column prop="adress" label="详细地址" width="140" />
        <el-table-column prop="createdAt" label="发布时间" width="170" sortable="custom"/>
        <el-table-column prop="status" label="状态" sortable="custom">
          <template slot-scope="prop">
            <el-tag type="primary">{{ prop.row.statusText }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="" fixed="right" width="500px">
          <template slot-scope="scope">
            <el-button size="mini" icon="el-icon-edit" class="mini-btn" @click="handleEdit(scope.$index, scope.row)">详情</el-button>
            <el-button size="mini" icon="el-icon-star-off" v-if="scope.row.status === 2" type="info" class="mini-btn" @click="handleCancel(scope.$index, scope.row)">取消发布</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" class="mini-btn" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            <el-button size="mini" icon="el-icon-info" class="mini-btn" @click="applyInfo(scope.$index, scope.row)">认养反馈</el-button>
<!--            <el-button size="mini" icon="el-icon-info" class="mini-btn" @click="upkeepInfo(scope.$index, scope.row)">维护反馈</el-button>-->
            <el-button size="mini" type="primary" icon="el-icon-s-promotion" class="mini-btn" @click="downloadQrCode(scope.$index, scope.row)">二维码</el-button>
          </template>
        </el-table-column>
      </el-pagination-table>
    </el-main>
  </el-container>
</template>

<script>
import {list_init_api, delete_api, cancel_api, adopt_export_api} from '@/api/adopt'
import {download_qrcode_api} from "@/api/donate";
export default {
  name: 'DonateMain',
  data() {
    return {
      loading: false,
      form: {
        adoptNo:'',
        donateNo: ''
      },
      dataList: [],
      sortColumn: {},
      pagination: {
        pageNumber: 1
      },
      sortMapping: {
        donateNo: 'di.donate_no',
        adoptNo: 'ai.adopt_no',
        seatNo: 'si.seat_no',
        createdAt: 'ai.created_at',
        status: 'ai.status',
      }
    }
  },
  created() {
    this.handleSearch()
  },
  methods: {
    handleSearch() {
      this.loading = true
      list_init_api(this.form, this.pagination.pageNumber, this.$utils.sort(this.sortColumn, this.sortMapping)).then(res => {
        this.dataList = res.value
        this.pagination = res.pagination
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    resetCondition() {
      this.form.donateNo = ''
      this.form.adoptNo = ''
      this.pagination.pageNumber=0
      this.handleSearch();
    },
    handleAdd() {
      this.$router.push('/seats-adopt/add')
    },
    handleEdit(index, row) {
      this.$router.push('/seats-adopt/edit/'+ row.id)
    },
    applyInfo(index, row) {
      this.$router.push('/seats-adopt/applyInfo/' + row.id)
    },
    upkeepInfo(index, row) {
      this.$router.push('/seats-adopt/upkeepInfo/' + row.id)
    },
    handleMultiDelete() {
    },
    handleDelete(index, row) {
      const self = this
      this.$confirm('是否要删除认养信息').then(res => {
        delete_api(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
    handleCancel(index, row) {
      const self = this
      this.$confirm('是否取消发布').then(res => {
        cancel_api(row.id).then(res => {
          self.handleSearch()
        }).catch(e => {
        })
      }).catch(e => {
      })
    },
    beforeUpload() {
      this.loading = true
    },
    handleSuccess() {
      this.loading = false
      const self = this
      self.$message.success('认养信息导入成功。')
      self.handleSearch()
    },
    downloadQrCode(index, row) {
      this.loading = true
      download_qrcode_api('2' + row.id).then(res => {
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    handleError() {
      this.loading = false
      const self = this
      self.$message.warning('认养信息导入失败。')
    },
    async handleExport() {
      this.loading = true
      await adopt_export_api(this.form).then(res => {
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
  }
}
</script>
