<template>
  <el-container>
    <el-panel-header show-back @goBack="handleBack"/>
    <el-main v-loading="loading">
      <el-form ref="form" :model="form" :rules="validRules" label-width="100px">
        <el-card shadow="never" style="width: 1000px">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <div>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="adoptNo" label="认养编号">
                  <el-input v-model="form.adoptNo" style="width: 200px"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="donateNo" label="捐赠编号">
                  <el-input v-model="form.donateNo" style="width: 200px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="seatNo" label="座椅编号">
                  <el-input v-model="form.seatNo" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="seatImage" label="座椅主图">
                  <div>
                    <el-picture-upload v-model="form.seatImage" multiple :limit="1"/>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="seatName" label="座椅名称">
                  <el-input v-model="form.seatName" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="material" label="座椅材质">
                  <el-input v-model="form.seatMaterial" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="seatSize" label="座椅大小">
                  <el-input v-model="form.seatSize" style="width: 200px"/>
                </el-form-item>
                <el-form-item prop="seatPrice" label="座椅价格">
                  <el-input v-model="form.seatPrice" type="number" style="width: 200px"/>
                </el-form-item>
              </el-col>
              <el-col>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="seatImageList" label="座椅其它图片">
                  <div>
                    <el-picture-upload v-model="form.seatImageList" multiple :limit="5"/>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="ownerUnit" label="建设单位">
                  <el-input v-model="form.ownerUnit" style="width: 500px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="constructUnit" label="施工单位">
                  <el-input v-model="form.constructUnit" style="width: 500px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="seatIntroduce" label="座椅介绍">
                  <el-input v-model="form.seatIntroduce" type="textarea" style="width: 630px"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="adress" label="详细地址">
                  <el-geo-coordinates
                    :gps-lng.sync="form.gpsLng"
                    :gps-lat.sync="form.gpsLat"
                    :address.sync="form.adress"
                    :province.sync="form.province"
                    :city.sync="form.city"
                    :strict.sync="form.strict"
                    :street.sync="form.street"
                    style="width: 500px;"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item prop="province" label="省市县街道">
                  {{form.province}}/{{form.city}}/ {{form.strict}}/ {{form.street}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="adoptPrice" label="认养金额">
                  <el-input v-model="form.adoptPrice" style="width: 200px"/>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="8">
                <el-form-item prop="adoptTerm" label="认养期限">
                  <template>
                    <el-select v-model="form.adoptTerm" placeholder="请选择">
                      <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        style="width: 200px">
                      </el-option>
                    </el-select>
                  </template>
                </el-form-item>
              </el-col> -->
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="validTime" label="状态">
                  <el-tag type="primary"> {{optType === 'add' ? '做成中' : form.statusText}} </el-tag>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="never" style="width: 1000px; margin-top: 10px">
          <div style="text-align: right">
            <el-loading-button type="" style="width: 100px" @click="handleBack">取消</el-loading-button>
            <el-loading-button v-if="form.status <= 1" style="width: 100px" @click="handleSave">保存</el-loading-button>
            <el-loading-button v-if="form.status == 1" style="width: 100px" @click="done => handleSaveStatus(done,2)">发布</el-loading-button>
            <el-loading-button type="info" v-if="form.status == 2" style="width: 100px" @click="done => handleSaveStatus(done,1)">取消发布</el-loading-button>
          </div>
        </el-card>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import { adopt_init_api, adopt_save_api, adopt_save_status_api, seat_get_api } from '@/api/adopt'
export default {
  name: 'AdoptEdit',
  watch:{
    'form.adress':{
      deep:true,
      handler(newVal){
        this.$refs.form.validateField('adress')
      }
    }
  },
  data() {
    return {
      isEdit: true,
      loading: false,
      optType: null,
      form: {
        adoptNo: '',
        donateNo: '',
        adoptPrice:'',
        adoptTerm:'',
        seatNo: '',
        seatName: '',
        seatMaterial:'',
        seatSize:'',
        seatPrice: '',
        ownerUnit: '',
        constructUnit: '',
        seatImage: {
        },
        seatImageList: {},
        seatIntroduce:'',
        province: '',
        city: '',
        strict: '',
        street: '',
        adress: '',
        geoPoint:[],
        status:'',
        pcdName:''
      },
      seatOptions:[],
      validRules: {
        adoptNo:[{ required: true, trigger: 'blur', message: '认养编号必须输入' }],
        seatNo: [{ required: true, trigger: 'blur', message: '座椅编号必须输入' }],
        seatPrice: [{ required: true, trigger: 'blur', message: '座椅价格必须输入' }],
        seatImage: [{ required: true, trigger: 'blur', message: '座椅主图必须上传' }],
        adress: [{ required: true, trigger: 'blur', message: '详细地址必须输入' }],
        adoptPrice: [{ required: true, trigger: 'blur', message: '认养金额必须输入' }]
      },
      options: [{
        value: 1,
        label: '一年'
      }, {
        value: 2,
        label: '二年'
      }, {
        value: 3,
        label: '三年'
      }]
    }
  },
  computed: {
    editable() {
      // return this.form.status != 1
      return true
    }
  },
  created() {
    if (this.$route.path === '/seats-adopt/add') {
      this.optType = 'add'
    } else {
      this.optType = 'edit'
    }
    this.init(this.$route.params.key)
  },
  methods: {
    init(id) {
      this.loading = true
      adopt_init_api(this.optType, id).then(res => {
        // if(this.optType === 'edit'){
        //   this.form.geoPoint = [res.value.gpsLng, res.value.gpsLat]
        // }
        // this.seatOptions = res.map.seatOptions || []
        Object.assign(this.form, res.value)
      }).catch(e => {
      }).finally(() => {
        this.loading = false
      })
    },
    // handleSeatSelect(seatId) {
    //   this.loading = true
    //   seat_get_api(seatId).then(res => {
    //     const data = res.value
    //     this.form.seatNo = data.seatNo
    //     this.form.seatName = data.seatName
    //     this.form.seatPrice = data.price
    //     this.form.seatImageUrl = data.image
    //   }).catch(e => {
    //     this.form.seatNo = ''
    //     this.form.seatName = ''
    //     this.form.seatPrice = ''
    //     this.form.seatImageUrl = {
    //       files: []
    //     }
    //   }).finally(() => {
    //     this.loading = false
    //   })
    // },
    handleBack() {
      this.$router.go(-1)
    },
    handleSave(done) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          adopt_save_api(this.optType, this.form).then(res => {
            // this.$router.push('/seats/edit')
          }).catch(e => {
          }).finally(() => {
            this.loading = false
            done()
          })
        } else {
          done()
        }
      })
    },
    handleSaveStatus(done, status) {

      this.loading = true
      this.$confirm('请确认是否继续此操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        adopt_save_status_api(this.form.id, status).then(res => {
          // this.$router.push('/seats-adopt/list')
        }).catch(e => {
        }).finally(() => {
          this.$message({
            type: 'success',
            message: '修改成功!'
          })
          this.loading = false
          done()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
        this.loading = false
        done()
      });
    }
  }
}
</script>
