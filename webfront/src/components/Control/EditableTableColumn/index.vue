<template>
  <el-table-column :label="label" :width="width">
    <template slot-scope="scope">
      <el-input
        v-if="type === 'input' && (scope.row.status === 'editing' || scope.row.status === 'adding')"
        v-model="scope.row[propName]"
        :class="getErrorTextClass(scope.row.error, propName)"
        class="editable-table-column"
        size="mini"/>
      <el-input
        v-if="type === 'number' && (scope.row.status === 'editing' || scope.row.status === 'adding')"
        v-model.number="scope.row[propName]"
        :class="getErrorTextClass(scope.row.error, propName)"
        class="editable-table-column"
        type="number"
        size="mini"/>
      <el-select
        v-if="type === 'select' && (scope.row.status === 'editing' || scope.row.status === 'adding')"
        v-model="scope.row[propName]"
        :clearable="selectClearable"
        :class="getErrorTextClass(scope.row.error, propName)"
        class="editable-table-column"
        size="mini">
        <el-option
          v-for="item in selectOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"/>
      </el-select>
      <el-select
        v-if="type === 'select.number' && (scope.row.status === 'editing' || scope.row.status === 'adding')"
        v-model.number="scope.row[propName]"
        :clearable="selectClearable"
        :class="getErrorTextClass(scope.row.error, propName)"
        class="editable-table-column"
        size="mini">
        <el-option
          v-for="item in selectOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"/>
      </el-select>
      <el-cascader
        v-if="type === 'cascader' && (scope.row.status === 'editing' || scope.row.status === 'adding')"
        v-model="scope.row[propName]"
        :options="selectOptions"
        :clearable="selectClearable"
        :style="{'width': (width - 20) + 'px'}"
        class="editable-table-column"
        filterable/>
      <el-date-picker
        v-if="type === 'date' && (scope.row.status === 'editing' || scope.row.status === 'adding')"
        v-model="scope.row[propName]"
        :class="getErrorTextClass(scope.row.error, propName)"
        class="editable-table-column"
        size="mini"
        value-format="yyyy-MM-dd"
        style="width:130px"/>
      <el-date-picker
        v-if="type === 'datetime' && (scope.row.status === 'editing' || scope.row.status === 'adding')"
        v-model="scope.row[propName]"
        :class="getErrorTextClass(scope.row.error, propName)"
        class="editable-table-column"
        size="mini"
        type="datetime"
        style="width:175px"
        value-format="yyyy-MM-dd HH:mm:ss"/>
      <el-time-picker
        v-if="type === 'time.is-range' && (scope.row.status === 'editing' || scope.row.status === 'adding')"
        v-model="scope.row[propName]"
        :start-placeholder="startPlaceholder"
        :end-placeholder="endPlaceholder"
        value-format="HH:mm:ss"
        is-range
        range-separator="～"/>
      <el-time-select
        v-if="type === 'time.select' && (scope.row.status === 'editing' || scope.row.status === 'adding')"
        v-model="scope.row[propName]"
        :editable="false"
        :picker-options="{
          start: '00:00',
          step: '00:30',
          end: '24:00'
        }"
        :placeholder="placeholder"
        style="width: 175px"/>
      <slot v-if="type === 'custom' && (scope.row.status === 'editing' || scope.row.status === 'adding')" :row="scope.row"/>
      <br v-if="scope.row.status === 'editing' || scope.row.status === 'adding'">
      <span v-if="hasError(scope.row.error, propName)" class="row_field_error_span">{{ getErrorMsg(scope.row.error, propName) }}</span>
      <span
        v-if="scope.row.error !== null && scope.row.error !== undefined && !hasError(scope.row.error, propName)">&nbsp;</span>
      <span v-if="scope.row.status !== 'editing' && scope.row.status !== 'adding'">{{ getText(scope.row.index, type, scope.row[propName]) }}</span>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: 'ElEditableTableColumn',
  props: {
    propName: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: () => 'input'
    },
    selectOptions: {
      type: Array,
      default: () => []
    },
    selectClearable: {
      type: Boolean,
      default: () => true
    },
    width: {
      type: Number,
      default: () => null
    },
    placeholder: {
      type: String,
      default: () => ''
    },
    startPlaceholder: {
      type: String,
      default: () => ''
    },
    endPlaceholder: {
      type: String,
      default: () => ''
    },
    customGetTextFunc: {
      type: Function,
      default: (index, val) => val
    }
  },
  data() {
    return {}
  },
  mounted() {

  },
  created() {
  },
  methods: {
    hasError: function(errors, field) {
      if (errors === null || errors === undefined) {
        return false
      }
      for (let i = 0; i < errors.length; i++) {
        if (errors[i].field === field) {
          return true
        }
      }
      return false
    },
    getErrorTextClass: function(errors, field) {
      if (this.hasError(errors, field)) {
        return 'is-error'
      }
      return ''
    },
    getErrorMsg: function(errors, field) {
      if (errors === null || errors === undefined) {
        return ''
      }
      for (let i = 0; i < errors.length; i++) {
        if (errors[i].field === field) {
          return errors[i].message
        }
      }
      return ''
    },
    getText(index, type, val) {
      if (type === 'select' || type === 'cascader') {
        return this.getSelectText(val)
      } else if (type === 'time.is-range') {
        return val[0] + ' ～ ' + val[1]
      } else {
        return this.customGetTextFunc(index, val)
      }
    },
    getSelectText(val) {
      let text = val
      function find(t, options, index, length) {
        if (options === null || options === undefined) {
          return
        }
        options.forEach(o => {
          if (String(o.value) === String(val[index])) {
            t.push(o.label)
            if (index + 1 < length) {
              find(t, o.children, index + 1, length)
            }
          }
        })
      }
      if (this.type === 'cascader') {
        const t = []
        find(t, this.selectOptions, 0, val.length)
        text = t.join(' / ')
      } else {
        this.selectOptions.forEach(o => {
          if (String(o.value) === String(val)) {
            text = o.label
          }
        })
      }
      return text
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
  .editable-table-column {
    .el-input__inner {
      padding: 0 5px !important;
    }
  }
</style>

<style type="text/css" scoped>
  .button {
    margin-left: 5px;
  }

  .is-error {
    border-color: #F56C6C;
  }

  .row_field_error_span {
    color: #F56C6C;
    font-size: 12px;
    line-height: 1;
    padding-top: 2px;
    /*position: absolute;*/
    top: 100%;
    left: 0;
  }
</style>
