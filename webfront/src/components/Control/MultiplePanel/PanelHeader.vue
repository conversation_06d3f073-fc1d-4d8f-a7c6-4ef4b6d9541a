<template>
  <div class="page-header">
    <div v-if="!showBack" :style="{'width': width + 'px'}" class="text"><b>{{ titleText }}</b></div>
    <div v-else :style="{'width': backWidth + 'px'}" class="text back">
      <el-link :underline="false" type="text" style="color:#0e8bc5; font-weight: bold" icon="el-icon-back" @click="goBack">返回</el-link>
      <span style="border-left:2px #ccc solid; margin-left: 5px; margin-right: 10px;"/>
      <b>{{ titleText }}</b>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElPanelHeader',
  props: {
    title: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: () => 100
    },
    showBack: {
      type: Boolean,
      default: () => false
    }
  },
  computed: {
    backWidth() {
      return this.width + 65
    },
    titleText() {
      return this.title || this.$route.meta.title || ''
    }
  },
  methods: {
    goBack() {
      this.$emit('goBack')
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css" scoped>
  .page-header {
    height: 24px;
    border-bottom: 1px solid #dbdbdb;
    margin-bottom: 10px;
    display: flex;
    line-height: 24px;
  }
  .page-header .text {
    display: block;
    margin-left: 1px;
    text-align: center;
    height: 24px;
    line-height: 24px;
    border-bottom: 2px solid #0e8bc5;
    font-size: 14px;
  }
</style>
