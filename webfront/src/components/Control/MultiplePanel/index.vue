<template>
  <el-tabs v-model="activeName" :lazy="true" class="multiple_panel_dev" tab-position="right">
    <el-tab-pane
      v-for="(panel, index) in panels"
      :key="panel.name"
      :label="tabName"
      :name="panel.name"
    >
      <el-panel-header :title="title" :width="headTitleWidth" :show-back="index !== 0" @goBack="goBack"/>
      <component :is="panel.name" :key="panel.name + keyIndent[panel.name]" :ref="panel.name"/>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import Vue from 'vue'

export default {
  name: 'ElMultiPanel',
  provide: function() {
    return {
      toPanel: this.toPanel
    }
  },
  props: {
    panels: {
      type: Array,
      default: function() {
        return []
      }
    },
    activePanel: {
      type: String,
      default: () => ''
    },
    headTitleWidth: {
      type: Number,
      default: () => 90
    }
  },
  data() {
    return {
      activeName: '',
      tabName: '',
      keyIndent: {},
      title: '',
      oprType: '',
      panelReCreate: {}
    }
  },
  watch: {
    activeName(nv) {
      const panel = this.panels.filter(e => e.name === nv)
      if (panel) {
        const p = panel[0]
        if ((typeof p.title) === 'string') {
          this.title = p.title
        } else {
          this.title = p.title[this.oprType]
        }
        this.$emit('update:activePanel', p.name)
      }
    }
  },
  created() {
    this.panels.forEach(p => {
      Vue.component(p.name, () => import('@/views/' + p.vuePath))
      this.keyIndent[p.name] = 0
      if (p.reCreate !== undefined) {
        this.panelReCreate[p.name] = p.reCreate
      } else {
        this.panelReCreate[p.name] = true
      }
    })
    this.activeName = this.panels[0].name
  },
  methods: {
    toPanel(panelName, ...param) {
      let name = ''
      if (panelName.indexOf('/') !== -1) {
        name = panelName.split('/')[0]
        this.oprType = panelName.split('/')[1]
      } else {
        name = panelName
        this.oprType = ''
      }
      this.panelReCreate[name] && this.keyIndent[name]++
      this.activeName = name
      const self = this
      this.$message.closeAll()
      this.$nextTick(() => {
        if (self.$refs[name][0].initPanel) {
          self.$refs[name][0].initPanel(...param)
        }
      })
      return new Promise((resolve, reject) => resolve())
    },
    goBack() {
      if (this.$refs[this.activeName][0].goBack) {
        this.$refs[this.activeName][0].goBack()
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
  .multiple_panel_dev {
    .el-tabs__header .is-right {
      padding: 0;
      position: relative;
      margin: 0 0 15px;
      visibility: hidden;
    }
  }
</style>
