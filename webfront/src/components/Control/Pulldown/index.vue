<template>
  <el-select
    v-model="currentValue"
    :value="value"
    :multiple="multiple"
    :clearable="clearable"
    :filterable="filterable"
    :placeholder="placeholder"
    expand-trigger="hover"
    @change="(e) => $emit('change', e)"
  >
    <el-option v-for="(item, index) in options" :key="index" :label="item.label" :value="item.value"/>
  </el-select>
</template>

<script>
export default {
  name: 'ElPulldown',
  props: {
    options: {
      type: Array,
      required: true
    },
    value: {
      type: [String, Number, Array],
      default: () => null
    },
    readonly: {
      type: Boolean,
      default: () => false
    },
    clearable: {
      type: Boolean,
      default: () => true
    },
    filterable: {
      type: Boolean,
      default: () => false
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    placeholder: {
      type: String,
      default: () => ''
    },
    multiple: {
      type: Boolean,
      default: () => false
    }
  },
  data: function() {
    return {
      currentValue: ''
    }
  },
  watch: {
    value(val) {
      this.currentValue = this.getValue(val)
    },
    currentValue(val) {
      this.$emit('input', this.getValue(val))
    }
  },
  mounted() {
    this.currentValue = this.getValue(this.value)
  },
  methods: {
    getValue(val) {
      if (this.multiple === false) {
        return val || ''
      } else {
        return val !== null ? val : []
      }
    }
  }
}
</script>
