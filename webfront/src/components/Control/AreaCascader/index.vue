<template>
  <el-cascader v-model="model" :options="options" :props="props" :disabled="disabled" clearable filterable style="width: 100%" @change="handleChange"/>
</template>

<script>
import Vue from 'vue'
import request from '@/common/request'

export default {
  name: 'ElAreaCascader',
  props: {
    province: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    strict: {
      type: String,
      required: true
    },
    street: {
      type: String,
      required: true
    },
    props: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean
    }
  },
  data() {
    return {
      options: [],
      loading: false
    }
  },
  computed: {
    model: {
      get() {
        const currentValue = []
        if (this.province) {
          currentValue.push(this.province)
        }
        if (this.city) {
          currentValue.push(this.city)
        }
        if (this.strict) {
          currentValue.push(this.strict)
        }
        if (this.street) {
          currentValue.push(this.street)
        }
        return currentValue
      },

      set(val) {
        if (!val || val.length === 0) {
          this.$emit('update:province', '')
          this.$emit('update:city', '')
          this.$emit('update:strict', '')
          this.$emit('update:street', '')
        }
        if (val.length > 0) {
          this.$emit('update:province', val[0])
        }
        if (val.length > 1) {
          this.$emit('update:city', val[1])
        }
        if (val.length > 2) {
          this.$emit('update:strict', val[2])
        }
        if (val.length > 3) {
          this.$emit('update:street', val[3])
        }
      }
    }
  },
  watch: {
  },
  created() {
    if (!this.$area) {
      this.loading = true
      this.getAreaPulldown().then(res => {
        this.options = res.value
        Vue.prototype.$area = res.value

        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    } else {
      this.options = this.$area
    }
  },
  methods: {
    getAreaPulldown() {
      return request({
        url: '/area/cascader',
        method: 'get'
      })
    },
    handleChange(e) {
      this.$nextTick(() => {
        this.$emit('change')
      })
    }
  }
}
</script>
