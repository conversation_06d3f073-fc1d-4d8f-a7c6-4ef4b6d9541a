<template>
  <div style="border: 0; display: inline-block">
    <el-photo-upload
      v-for="(imgData, index) in dataList"
      :key="index"
      :img-data="imgData"
      :editable="editable"
      :width="width"
      :height="height"
      :pre-view-img-list="preViewImgList"
      style="margin: 10px 5px 3px 5px"
      @handleImageChange="(img) => handleImageChange(index, img)"/>
  </div>
</template>

<script>
export default {
  name: 'ElPhotoMultiUpload',
  props: {
    imgDataList: {
      type: Array,
      default: () => []
    },
    size: {
      type: Number,
      default: () => 3
    },
    width: {
      type: Number,
      default: () => 120
    },
    height: {
      type: Number,
      default: () => 120
    },
    editable: {
      type: Boolean,
      default: () => true
    }
  },
  data() {
    return {
      // dataList: this.editable === true ? [''] : []
    }
  },
  computed: {
    dataList: function() {
      const viewList = []
      this.imgDataList.forEach(data => {
        viewList.push(data)
      })
      if (this.editable === true && this.imgDataList.length === 0) {
        viewList.push('')
      }
      return viewList
    },
    preViewImgList: function() {
      const viewList = []
      this.dataList.forEach(data => {
        if (data !== '') {
          viewList.push(data)
        }
      })
      return viewList
    }
  },
  watch: {
    imgDataList(nv) {
      this.dataList.length = 0
      this.imgDataList.forEach(i => {
        if (i) {
          this.dataList.push(i)
        }
      })
      if (this.editable === true && this.dataList.length < this.size) {
        this.dataList.push('')
      }
    }
  },
  methods: {
    handleImageChange(index, image) {
      if (image) {
        if (this.dataList.length === 0) {
          this.dataList.push(image)
        } else {
          this.dataList.splice(index, 1, image)
        }
        this.$emit('update:imgDataList', this.updateData(this.dataList))
        if (this.editable === true && this.dataList.length < this.size) {
          this.dataList.push('')
        }
      } else {
        const last = index === this.dataList.length - 1
        this.dataList.splice(index, 1)
        this.$emit('update:imgDataList', this.updateData(this.dataList))
        if (this.editable === true && last === true) {
          this.dataList.push('')
        }
      }
    },
    updateData(imageList) {
      const dataList = []
      imageList.forEach(e => {
        if (e) {
          dataList.push(e)
        }
      })
      return dataList
    }
  }
}
</script>
