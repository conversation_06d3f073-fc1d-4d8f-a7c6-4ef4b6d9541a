<template>
  <div v-loading="searchLoading" class="el-thin-scrollbar">
    <el-table
      ref="$table"
      :data="dataList"
      :height="height"
      fit
      highlight-current-row
      @row-dblclick="toggleRowExpansion"
      @sort-change="c => sortChange(c.column)"
      @selection-change="handleSelectionChange">
      <slot/>
    </el-table>
    <el-pagination
      v-if="pagination !== null && pagination.totalCount && pagination.totalCount > 0"
      id="employee_list_pageContainer"
      :current-page="pagination.pageNumber"
      :page-size="pagination.pageSize"
      :total="pagination.totalCount"
      background
      align="right"
      style="margin: 10px;"
      layout="total, prev, pager, next, jumper"
      @current-change="handleCurrentChange"/>
  </div>
</template>

<script>
export default {
  name: 'ElPaginationTable',
  props: {
    data: {
      type: Array,
      required: true
    },
    pagination: {
      type: Object,
      required: true
    },
    searchFunction: {
      type: Function,
      required: true
    },
    selectionCondition: {
      type: Function,
      default: () => (row) => false
    },
    searchLoading: {
      type: Boolean,
      required: true
    },
    sortColumn: {
      type: Object,
      default: () => {}
    },
    height: {
      type: String,
      default: () => 'calc(100vh - 330px)'
    }
  },
  data() {
    return {
    }
  },
  computed: {
    dataList() {
      this.data && this.data.forEach(d => { d.$selection = false })
      return this.data
    }
  },
  mounted() {
    this.$refs.$table.layout.gutterWidth = 9 // scrollbar width
  },
  methods: {
    toggleRowExpansion(row) {
      this.$refs.$table.toggleRowExpansion(row)
    },
    handleSelectionChange(rows) {
      this.data.forEach(d => {
        if (this.selectionCondition(d) === false) {
          d.$selection = false
          this.$refs.$table.toggleRowSelection(d, false)
        } else {
          d.$selection = rows.indexOf(d) !== -1
        }
      })
    },
    async handleCurrentChange(pageNumber) {
      this.pagination.pageNumber = pageNumber
      this.$emit('update:pagination', this.pagination)
      this.$emit('update:searchLoading', true)
      await this.searchFunction(false)
      this.$emit('update:searchLoading', false)
    },
    async sortChange(column) {
      this.$emit('update:sortColumn', column)
      this.pagination.pageNumber = 1
      this.$emit('update:searchLoading', true)
      await this.searchFunction(false)
      this.$emit('update:searchLoading', false)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.el-thin-scrollbar {
  ::-webkit-scrollbar {
     width: 9px;
     height: 9px;
   }
  ::-webkit-scrollbar-thumb{
     background-color: #bbb;
     -webkit-border-radius: 9px;
     border-radius: 9px;
   }
  ::-webkit-scrollbar-thumb:vertical:hover{
     background-color: #999;
   }
  ::-webkit-scrollbar-thumb:vertical:active{
     background-color: #888;
   }
  ::-webkit-scrollbar-button{
     display: none;
   }
  ::-webkit-scrollbar-track{
     background-color: #f1f1f1;
   }
}
</style>
