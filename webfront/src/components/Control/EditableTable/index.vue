<template>
  <el-table
    ref="editedTable"
    :data="tableData"
    :height="height"
    :highlight-current-row="highlightCurrentRow"
    :row-class-name="rowClassName"
    class="el-edited-table"
    @row-dblclick="handleToggleRowExpansion">
    <slot/>
    <el-table-column label="" min-width="10"/>
    <el-table-column v-if="editButtonShow || statusShow" prop="status" label="编辑状态" width="100">
      <template slot-scope="scope">
        <el-tag v-if="scope.row.status === 'added' || scope.row.status === 'adding' " type="success" effect="plain">新增</el-tag>
        <el-tag v-else-if="scope.row.status === 'edited'" type="" effect="plain">已修改</el-tag>
        <el-tag v-else-if="scope.row.status === 'deleted'" type="danger" effect="plain">已删除</el-tag>
      </template>
    </el-table-column>
    <el-table-column v-if="editButtonShow" :width="buttonTextShow ? 200: 100" fixed="right" align="center">
      <template slot="header" slot-scope="scope">
        <el-button v-if="buttonTextShow && addButtonShow" type="primary" icon="el-icon-circle-plus-outline" plain @click="handleAdd">{{ tableButtonAddText }}</el-button>
        <el-button v-if="!buttonTextShow && addButtonShow" type="primary" icon="el-icon-circle-plus-outline" circle plain @click="handleAdd"/>
      </template>
      <template slot-scope="scope">
        <div v-if="buttonTextShow">
          <el-link
            v-if="scope.row.status === 'editing'"
            :underline="false"
            type="warning"
            icon="el-icon-refresh-right"
            class="button"
            @click="handleEditCancel(scope.$index, scope.row)">{{ tableButtonEditCancelText }}</el-link>
          <el-link
            v-if="scope.row.status === 'editing' || scope.row.status === 'adding'"
            :underline="false"
            type="success"
            icon="el-icon-check"
            class="button"
            @click="handleEditComplete(scope.$index, scope.row)">{{ tableButtonCompleteText }}</el-link>
          <el-link
            v-if="scope.row.status === 'edited' || scope.row.status === 'deleted'"
            :underline="false"
            type="warning"
            icon="el-icon-refresh-left"
            class="button"
            @click="handleRestore(scope.$index, scope.row)">{{ tableButtonRestoreText }}</el-link>
          <el-link
            v-if="(scope.row.status === 'edited' || scope.row.status === 'added' || scope.row.status === 'normal')"
            :underline="false"
            type="primary"
            icon="el-icon-edit"
            class="button"
            @click="handleEdit(scope.$index, scope.row)">{{ tableButtonEditText }}</el-link>
          <el-link
            v-if="scope.row.status === 'edited' || scope.row.status === 'added' || scope.row.status === 'adding' || scope.row.status === 'normal'"
            :underline="false"
            type="danger"
            icon="el-icon-delete"
            class="button"
            @click="handleDelete(scope.$index, scope.row)">{{ tableButtonDelText }}</el-link>
        </div>
        <div v-else>
          <el-link
            v-if="scope.row.status === 'editing'"
            :underline="false"
            type="warning"
            icon="el-icon-refresh-right"
            class="button"
            @click="handleEditCancel(scope.$index, scope.row)"/>
          <el-link
            v-if="scope.row.status === 'editing' || scope.row.status === 'adding'"
            :underline="false"
            type="success"
            icon="el-icon-check"
            class="button"
            @click="handleEditComplete(scope.$index, scope.row)"/>
          <el-link
            v-if="scope.row.status === 'edited' || scope.row.status === 'deleted'"
            :underline="false"
            type="warning"
            icon="el-icon-refresh-left"
            class="button"
            @click="handleRestore(scope.$index, scope.row)"/>
          <el-link
            v-if="(scope.row.status === 'edited' || scope.row.status === 'added' || scope.row.status === 'normal')"
            :underline="false"
            type="primary"
            icon="el-icon-edit"
            class="button"
            @click="handleEdit(scope.$index, scope.row)"/>
          <el-link
            v-if="scope.row.status === 'edited' || scope.row.status === 'added' || scope.row.status === 'adding' || scope.row.status === 'normal'"
            :underline="false"
            type="danger"
            icon="el-icon-delete"
            class="button"
            @click="handleDelete(scope.$index, scope.row)"/>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import Schema from 'async-validator'
export default {
  name: 'ElEditableTable',
  props: {
    propKey: {
      type: String,
      default: () => null
    },
    height: {
      type: String,
      default: () => null
    },
    buttonTextShow: {
      type: Boolean,
      default: () => true
    },
    tableButtonAddText: {
      type: String,
      default: () => '添加'
    },
    tableButtonDelText: {
      type: String,
      default: () => '删除'
    },
    tableButtonEditText: {
      type: String,
      default: () => '编辑'
    },
    tableButtonEditCancelText: {
      type: String,
      default: () => '取消'
    },
    tableButtonRestoreText: {
      type: String,
      default: () => '恢复'
    },
    tableButtonCompleteText: {
      type: String,
      default: () => '完成'
    },
    highlightCurrentRow: {
      type: Boolean,
      default: () => false
    },
    editButtonShow: {
      type: Boolean,
      default: () => true
    },
    statusShow: {
      type: Boolean,
      default: () => false
    },
    addButtonShow: {
      type: Boolean,
      default: () => true
    },
    data: {
      type: Array,
      default: () => []
    },
    rulesInvalidate: {
      type: Object,
      default: () => null
    },
    relationInvalidate: {
      type: Object,
      default: () => null
    },
    toggleRowExpansion: {
      type: Boolean,
      default: () => false
    },
    handleModifyEvent: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      status: {
        status: '' // editing,edited,deleted,adding,added,normal
      },
      tableData: [],
      beforeEditData: {}
    }
  },
  watch: {
    'data': function(ov) {
      this.resetTable()
    }
  },
  mounted() {
    this.resetTable()
  },
  created() {
  },
  methods: {
    resetTable() {
      this.tableData = []
      this.data.forEach((e) => {
        const target = JSON.parse(JSON.stringify(e))
        let status = null
        if (e.status !== null) {
          if (e.status.value) {
            status = e.status.value
          } else {
            status = e.status
          }
        }
        if (status === 'delete') {
          status = 'deleted'
        }
        target.status = status || 'normal'
        e.status = status
        target.error = null
        this.tableData.push(target)
      })
    },
    handleAdd() {
      if (this.handleModifyEvent) {
        const self = this
        this.handleModifyEvent('add', function(data) {
          const target = JSON.parse(JSON.stringify(data))
          target.__rowId__ = this.$utils.guid()
          target.status = 'added'
          target.error = null
          self.tableData.push(target)
        })
      } else {
        this.tableData.push({ __rowId__: this.$utils.guid(), status: 'adding', error: null })
      }
    },
    handleRestore(index, row) {
      const _self = this
      this.$confirm('是否放弃此条数据的变更, 是否继续?', '提示', {
        type: 'warning'
      }).then(() => {
        const _data = JSON.parse(JSON.stringify(_self.data[index]))
        for (const key in _data) {
          row[key] = _data[key]
        }
        row.status = 'normal'
        _self.$emit('update:data', _self.tableData)
      }).catch(() => {})
    },
    handleEdit(index, row) {
      const _data = JSON.parse(JSON.stringify(this.tableData[index]))
      for (const key in _data) {
        this.beforeEditData[key] = _data[key]
      }
      if (row.status === 'added') {
        row.status = 'adding'
      } else {
        row.status = 'editing'
      }
    },
    handleEditCancel(index, row) {
      const _self = this
      this.$confirm('是否取消此次变更, 是否继续?', '提示', {
        type: 'warning'
      }).then(() => {
        const _data = JSON.parse(JSON.stringify(_self.beforeEditData))
        for (const key in _data) {
          row[key] = _data[key]
        }
      }).catch(() => {})
    },
    handleDelete(index, row) {
      this.$confirm('是否删除此条数据, 是否继续?', '提示', {
        type: 'warning'
      }).then(() => {
        if (row.status === 'added' || row.status === 'adding') {
          this.tableData.splice(index, 1)
        } else {
          row.status = 'deleted'
        }
        this.$emit('update:data', this.tableData)
      }).catch(() => {})
    },
    handleEditComplete(index, row) {
      const _self = this
      function editedChangeStatus() {
        if (row.status === 'adding') {
          row.status = 'added'
        } else {
          let change = false
          for (const key in _self.data[index]) {
            if (key !== 'status' && row[key] !== _self.data[index][key]) {
              change = true
              break
            }
          }
          row.status = change ? 'edited' : 'normal'
        }
        _self.$emit('update:data', _self.tableData)
      }
      function handleErrors(errors) {
        _self.tableData[index].error = errors
      }
      function handleRelationInvalidate() {
        const keys = Object.keys(_self.relationInvalidate)
        const validErrors = []
        keys.forEach(field => {
          const validFunc = _self.relationInvalidate[field]
          validFunc(row, row[field], (message) => {
            if (message) {
              validErrors.push({ 'field': field, 'message': message })
            }
          }, index, _self.tableData)
        })
        if (validErrors.length > 0) {
          handleErrors(validErrors)
        } else {
          _self.tableData[index].error = null
          editedChangeStatus()
        }
      }
      if (this.rulesInvalidate !== null) {
        const validator = new Schema(this.rulesInvalidate)
        validator.validate(row, (errors, fields) => {
          if (errors) {
            handleErrors(errors)
          } else {
            if (this.relationInvalidate !== null) {
              handleRelationInvalidate()
            } else {
              _self.tableData[index].error = null
              editedChangeStatus()
            }
          }
        })
      } else {
        if (this.relationInvalidate !== null) {
          handleRelationInvalidate()
        } else {
          editedChangeStatus()
        }
      }
    },
    rowClassName({ row, rowIndex }) {
      if (row.status === 'deleted') {
        return 'edittable-deleted-row'
      } else if (row.status === 'edited') {
        return 'edittable-edited-row'
      } else if (row.status === 'added') {
        return 'edittable-added-row'
      }
      return ''
    },
    handleToggleRowExpansion(row) {
      if (this.toggleRowExpansion === true) {
        this.$refs.editedTable.toggleRowExpansion(row)
      }
    },
    isEditing() {
      return this.tableData.filter(d => d.status === 'editing' || d.status === 'adding').length > 0
    },
    allData(discard) {
      let hasEditing = false
      this.tableData.forEach(d => {
        if (d.status === 'editing' || d.status === 'adding') {
          hasEditing = true
        }
      })
      if (discard) {
        hasEditing = false
      }
      if (hasEditing) {
        const error = new Error('表格中还有正在编辑的数据！')
        this.$message.error(error.message)
        return Promise.reject(error)
      } else {
        const changedData = []
        this.tableData.forEach(d => {
          changedData.push(JSON.parse(JSON.stringify(d)))
        })
        return Promise.resolve(changedData)
      }
    },
    changedData(discard) {
      let hasEditing = false
      this.tableData.forEach(d => {
        if (d.status === 'editing' || d.status === 'adding') {
          hasEditing = true
        }
      })
      if (discard) {
        hasEditing = false
      }
      if (hasEditing) {
        const error = new Error('表格中还有正在编辑的数据！')
        this.$message.error(error.message)
        return Promise.reject(error)
      } else {
        const changedData = []
        this.tableData.forEach(d => {
          if (d.status === 'added' || d.status === 'edited' || d.status === 'deleted') {
            changedData.push(JSON.parse(JSON.stringify(d)))
          }
        })
        return Promise.resolve(changedData)
      }
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css">
  .edittable-deleted-row {
    background-color: #fafafa !important;
  }
  .edittable-edited-row {
    background-color: #fdf5e6 !important;
  }
  .edittable-added-row {
    background-color: #f0f9eb !important;
  }
</style>
<style rel="stylesheet/css" lang="css" scoped>
  .button {
    margin-left: 5px;
  }
</style>
