<template>
  <div :style="{'min-width': minWidth}" style="width: 100%;" class="div-group">
    <div class="div-group-title-area">
      <span style="width: 300px" class="div-group-title" >{{ title }}</span>
      <div class="div-group-tools">
        <slot name="button-area"/>
      </div>
    </div>
    <el-divider/>
    <div style="padding: 0 10px">
      <slot/>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElGroupPanel',
  props: {
    title: {
      type: String,
      required: true
    },
    minWidth: {
      type: String,
      default: () => '600px'
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css">
  .div-group .el-divider--horizontal {
    margin: 22px 0 15px 0 !important;
  }
</style>

<style rel="stylesheet/css" lang="css" scoped>
  .div-group {
    border-radius: 5px;
    padding: 5px 0;
    margin-bottom: 15px;
    border: 1px solid #cbcdd3;
  }
  .div-group-title-area {
    height: 16px;
  }
  .div-group-title{
    display: block;
    font-size: medium;
    font-weight: bold;
    color: darkgrey;
    position: relative;
    top: 10px;
    text-align: left;
    float: left;
    margin-left: 15px;
    background: white;
  }
  .div-group-tools {
    min-width: 300px;
    display: block;
    float: right;
    text-align: right;
    margin-right: 20px;
  }
</style>
