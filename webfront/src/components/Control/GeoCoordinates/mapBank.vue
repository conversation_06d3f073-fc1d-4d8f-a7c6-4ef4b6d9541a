<template>
  <el-drag-dialog ref="dialog" :width="1000" top="5vh" title="经纬度拾取">
    <div>
      <el-area-cascader
        :province.sync="provinceText"
        :city.sync="cityText"
        :strict.sync="strictText"
        :street.sync="streetText"
        :disabled="readonly"
        style="width: 300px;"
        @change="handleChange"
      />
      <el-input v-model="position.address" :readonly="readonly" style="width: 500px; margin: 0 20px">
        <el-button slot="append" icon="el-icon-position" title="定位" @click="handleLocation"/>
      </el-input>
    </div>
    <div style="margin-top: 3px">
      <baidu-map :center="center" :zoom="zoom" :ak="$ak" :scroll-wheel-zoom="true" class="bm-view" @click="getPoint" @ready="handler">
        <bm-city-list anchor="BMAP_ANCHOR_TOP_RIGHT"/>
        <bm-geolocation
          :show-address-bar="true"
          :auto-location="true"
          anchor="BMAP_ANCHOR_BOTTOM_RIGHT"
        />
        <bm-navigation anchor="BMAP_ANCHOR_BOTTOM_LEFT"/>
        <bm-marker :position="position" :dragging="true" animation="BMAP_ANIMATION_BOUNCE"/>
        <!--<bm-control :offset="{width: '100px', height: '10px'}">-->
        <!--<bm-auto-complete v-model="keyword" :sug-style="{zIndex: 1}">-->
        <!--<el-input v-model="keyword" placeholder="请输入地名关键字">-->
        <!--<i slot="suffix" class="el-input__icon el-icon-search"/>-->
        <!--</el-input>-->
        <!--</bm-auto-complete>-->
        <!--</bm-control>-->
      </baidu-map>
    </div>
  </el-drag-dialog>
</template>

<script>
import BaiduMap from 'vue-baidu-map/components/map/Map.vue'
import BmCityList from 'vue-baidu-map/components/controls/CityList'
import BmGeolocation from 'vue-baidu-map/components/controls/Geolocation.vue'
import BmMarker from 'vue-baidu-map/components/overlays/Marker.vue'
import BmNavigation from 'vue-baidu-map/components/controls/Navigation.vue'
// import BmControl from 'vue-baidu-map/components/controls/Control.vue'
// import BmAutoComplete from 'vue-baidu-map/components/others/AutoComplete.vue'
let geoc = null
let BMap = null

export default {
  name: 'GeoMap',
  components: {
    BaiduMap,
    BmCityList,
    BmGeolocation,
    BmMarker,
    BmNavigation
    // BmControl,
    // BmAutoComplete
  },
  props: {
    longitude: { // 经度
      type: Number,
      default: () => 0
    },
    latitude: { // 纬度
      type: Number,
      default: () => 0
    },
    province: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    strict: {
      type: String,
      required: true
    },
    street: {
      type: String,
      required: true
    },
    address: {
      type: String,
      required: true
    },
    readonly: Boolean
  },
  data() {
    return {
      center: { lng: 0, lat: 0 },
      zoom: 3,
      map: null,
      form: {
        province: '',
        city: '',
        strict: '',
        street: ''
      },
      position: {
        lng: 0,
        lat: 0,
        address: '',
        province: '',
        city: '',
        strict: '',
        street: ''
      },
      keyword: '',
      mapStyle: {
        styleJson: [{
          featureType: 'poilabel',
          elementType: 'labels.icon',
          stylers: {
            visibility: 'off'
          }
        }]
      }
    }
  },
  computed: {
    provinceText: {
      get() {
        return this.province
      },
      set(val) {
        this.$emit('update:province', val)
      }
    },
    cityText: {
      get() {
        return this.city
      },
      set(val) {
        this.$emit('update:city', val)
      }
    },
    strictText: {
      get() {
        return this.strict
      },
      set(val) {
        this.$emit('update:strict', val)
      }
    },
    streetText: {
      get() {
        return this.street
      },
      set(val) {
        this.$emit('update:street', val)
      }
    }
  },
  methods: {
    open() {
      this.position.address = this.address
      this.$refs.dialog.open((close) => {
        this.$emit('update:longitude', this.position.lng || 0)
        this.$emit('update:latitude', this.position.lat || 0)
        this.$emit('update:address', this.position.address || '')
        close()
      })
    },
    handler(o) {
      this.map = o.map
      BMap = o.BMap
      this.map.setMapStyleV2(this.mapStyle)

      this.center.lng = this.longitude || 121.495793
      this.center.lat = this.latitude || 31.240872
      this.position.lng = this.center.lng
      this.position.lat = this.center.lat
      this.zoom = 18
    },
    getPoint(e) {
      if (this.readonly) {
        return
      }
      this.position.lng = this.$utils.keepFourDecimal(e.point.lng)
      this.position.lat = this.$utils.keepFourDecimal(e.point.lat)
      if (geoc == null) {
        geoc = new BMap.Geocoder()
      }
      // geoc.getLocation(e.point, (res) => {
      //   this.position.address = res.address
      // })
    },
    handleChange() {
      let area = ''
      if (this.provinceText) {
        area += this.provinceText
      }
      if (this.cityText) {
        area += this.cityText
      }
      if (this.strictText) {
        area += this.strictText
      }
      if (this.streetText) {
        area += this.streetText
      }
      if (area) {
        const self = this
        if (geoc == null) {
          geoc = new BMap.Geocoder()
        }
        geoc.getPoint(area, (point) => {
          const p = new BMap.Point(parseFloat(point.lng), parseFloat(point.lat))
          self.map.panTo(p)
        })
      }
    },
    handleLocation() {
      if (this.readonly) {
        return
      }
      if (this.position.address) {
        const self = this
        if (geoc == null) {
          geoc = new BMap.Geocoder()
        }
        geoc.getPoint(this.position.address, (point) => {
          const p = new BMap.Point(parseFloat(point.lng), parseFloat(point.lat))
          self.map.panTo(p)
          self.getPoint({
            point: p
          })
        })
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .bm-view {
    width: 100%;
    height: 500px;
  }
  ::v-deep .el-dialog__body {
    padding: 10px !important;
  }
</style>

<style rel="stylesheet/scss" lang="scss">
.citylist_popup_main .city_content_top {
  padding: 0 !important;
}
#city_ctrl_form {
  top: 0 !important;
}
#selCitySubmit {
  margin-top: 3px !important;
}
</style>
