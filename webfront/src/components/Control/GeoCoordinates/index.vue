<template>
  <div
    :class="{'is-active' : isActive}"
    :style="{width}"
    class="coordinates-out coordinates-editor el-input__inner el-range-editor--small"
    @mouseenter="hovering = true"
    @mouseleave="hovering = false">
    <div class="coordinates-button-div">
      <el-button type="primary" plain class="coordinates-button" icon="el-icon-map-location" @click="handleMap"/>
    </div>
    <input v-model="addressText" readonly class="coordinates-input__inner el-input__inner" style="text-align: left; margin-left: 5px"/>
    <span v-if="showClear" class="el-input__suffix">
      <span class="el-input__suffix-inner">
        <i class="el-input__icon el-range__close-icon el-icon-circle-close" @click="handleClear"/>
      </span>
    </span>
    <geo-map
      ref="map"
      :longitude.sync="longitude"
      :latitude.sync="latitude"
      :province.sync="provinceText"
      :city.sync="cityText"
      :strict.sync="strictText"
      :street.sync="streetText"
      :address.sync="addressText"
      :readonly="readonly"
      :isDonate="isDonate"/>
  </div>
</template>

<script>
import emitter from 'element-ui/src/mixins/emitter'
import Migrating from 'element-ui/src/mixins/migrating'
import GeoMap from './map'

export default {
  name: 'ElGeoCoordinates',
  components: { GeoMap },
  mixins: [emitter, Migrating],
  inheritAttrs: false,
  inject: {
    elForm: {
      default: ''
    },
    elFormItem: {
      default: ''
    }
  },
  props: {
    value: { // 绑定的 value
      type: Array,
      default: () => []
    },
    gpsLng: {
      type: Number,
      required: true
    },
    gpsLat: {
      type: Number,
      required: true
    },
    address: {
      type: String,
      required: true
    },
    province: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    strict: {
      type: String,
      required: true
    },
    street: {
      type: String,
      required: true
    },
    width: {
      type: String,
      default: () => '300px'
    },
    validateEvent: {
      type: Boolean,
      default: () => true
    },
    isDonate: {
      type: Boolean,
      default: () => false
    },
    readonly: Boolean
  },
  data() {
    return {
      longitude: this.gpsLng,
      latitude: this.gpsLat,
      focused: false,
      hovering: false
    }
  },
  computed: {
    isActive() {
      return this.focused
    },
    showClear() {
      return (this.longitude || this.latitude) && (this.focused || this.hovering) && !this.readonly
    },
    addressText: {
      get() {
        return this.address
      },
      set(val) {
        this.$emit('update:address', val)
      }
    },
    provinceText: {
      get() {
        return this.province
      },
      set(val) {
        this.$emit('update:province', val)
      }
    },
    cityText: {
      get() {
        return this.city
      },
      set(val) {
        this.$emit('update:city', val)
      }
    },
    strictText: {
      get() {
        return this.strict
      },
      set(val) {
        this.$emit('update:strict', val)
      }
    },
    streetText: {
      get() {
        return this.street
      },
      set(val) {
        this.$emit('update:street', val)
      }
    }
  },
  watch: {
    longitude(value) {
      this.$emit('update:gpsLng', value)
    },
    latitude(value) {
      this.$emit('update:gpsLat', value)
    }
  },
  methods: {
    handleClear() {
      this.latitude = 0
      this.longitude = 0
      this.addressText = ''
    },
    handleMap() {
      this.longitude = this.gpsLng
      this.latitude = this.gpsLat
      this.$refs.map.open()
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .coordinates-out {
    position: relative;
    display: inline-block;
    text-align: left;
    line-height: 32px;
    padding: 0 10px 0 0 !important;
  }
  .coordinates-editor.el-input__inner {
    display: inline-flex;
    align-items: center;
    padding: 0px 10px;
  }
  .coordinates-out.is-active {
    border-color: #409EFF;
  }
  .coordinates-button-div{
    display: inline-block;
    height: 30px;
    line-height: 30px;
  }
  .coordinates-button {
    border: none;
    border-right: 1px solid #DCDFE6 !important;
    width: 30px;
    height: 30px;
    padding: 5px !important;
    font-size: medium;
    vertical-align: center;
  }
  .coordinates-label {
    display: inline-block;
    width: 50px;
    font-weight: bold;
    color: #606266;
  }
  .coordinates-input__inner{
    appearance: none;
    border: none;
    outline: none;
    height: 98%;
    display: inline-block;
    margin: 0;
    padding: 0;
    width: 99%;
    text-align: center;
    font-size: 13px;
    color: #606266;
  }
</style>
