<template>
  <el-drag-dialog ref="dialog" :width="1300" top="5vh" title="经纬度拾取">
    <div>

      <el-row :gutter="20">
        <el-col :lg="9" :md="12" :sm="24">
          <el-area-cascader :province.sync="provinceText" :city.sync="cityText" :strict.sync="strictText"
            :street.sync="streetText" :disabled="readonly" @change="handleChange" />
        </el-col>
        <el-col :lg="13" :md="12" :sm="24">
          <el-input v-model="position.address" :readonly="readonly">
            <el-button slot="append" icon="el-icon-position" title="定位" @click="handleLocation" />
          </el-input>
        </el-col>
      </el-row>
    </div>
    <div style="margin-top: 3px">
      <div class="bm-view" id="map"></div>
    </div>
  </el-drag-dialog>
</template>

<script>
import BaiduMap from "vue-baidu-map/components/map/Map.vue";
import BmCityList from "vue-baidu-map/components/controls/CityList";
import BmGeolocation from "vue-baidu-map/components/controls/Geolocation.vue";
import BmMarker from "vue-baidu-map/components/overlays/Marker.vue";
import BmNavigation from "vue-baidu-map/components/controls/Navigation.vue";


// import BmControl from 'vue-baidu-map/components/controls/Control.vue'
// import BmAutoComplete from 'vue-baidu-map/components/others/AutoComplete.vue'
let geoc = null;
let BMap = null;

import { getDistrict, geocoder } from "@/api/serviceForMap";

export default {
  name: "GeoMap",
  components: {
    BaiduMap,
    BmCityList,
    BmGeolocation,
    BmMarker,
    BmNavigation
    // BmControl,
    // BmAutoComplete
  },
  props: {
    longitude: {
      // 经度
      type: Number,
      default: () => 0
    },
    latitude: {
      // 纬度
      type: Number,
      default: () => 0
    },
    province: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    strict: {
      type: String,
      required: true
    },
    street: {
      type: String,
      required: true
    },
    address: {
      type: String,
      required: true
    },
    readonly: Boolean,
    isDonate: Boolean,//是否是捐赠

  },
  data() {
    return {
      center: { lng: 0, lat: 0 },
      zoom: 3,
      map: null,
      form: {
        province: "",
        city: "",
        strict: "",
        street: ""
      },
      position: {
        lng: 0,
        lat: 0,
        address: "",
        province: "",
        city: "",
        strict: "",
        street: ""
      },
      keyword: "",
      mapStyle: {
        styleJson: [
          {
            featureType: "poilabel",
            elementType: "labels.icon",
            stylers: {
              visibility: "off"
            }
          }
        ]
      },
      TXMap: null,
      tMap: null, //腾讯地图
      marker: null,
      iconDonate: require("@/assets/image/icon_donate.png"),
      iconAdopt: require("@/assets/image/icon_adopt.png"),
    };
  },
  computed: {
    provinceText: {
      get() {
        return this.province;
      },
      set(val) {
        this.$emit("update:province", val);
      }
    },
    cityText: {
      get() {
        return this.city;
      },
      set(val) {
        this.$emit("update:city", val);
      }
    },
    strictText: {
      get() {
        return this.strict;
      },
      set(val) {
        this.$emit("update:strict", val);
      }
    },
    streetText: {
      get() {
        return this.street;
      },
      set(val) {
        this.$emit("update:street", val);
      }
    }
  },
  methods: {
    open() {
      this.position.address = this.address;
      this.$refs.dialog.open(close => {
        this.$emit("update:longitude", this.position.lng || 0);
        this.$emit("update:latitude", this.position.lat || 0);
        this.$emit("update:address", this.position.address || "");
        close();
      });
      this.handler();
    },
    handler() {
      //定义map变量，调用 TMap.Map() 构造函数创建地图

      this.$nextTick(() => {
        //加入检查经纬度是否正确
        
        if (this.longitude && this.longitude > -180 && this.longitude < 180 && this.latitude && this.latitude > -90 && this.latitude < 90) {
          this.center.lng = this.longitude;
          this.center.lat = this.latitude;
        } else {
          this.center.lng = 121.48915529251;
          this.center.lat = 31.23515770942;
        }

        this.position.lng = this.center.lng;
        this.position.lat = this.center.lat;
        this.zoom = 18;

        var center = new TMap.LatLng(this.center.lat, this.center.lng);
        this.tMap = new TMap.Map(document.getElementById("map"), {
          center: center, //设置地图中心点坐标
          minZoom: 6,
          maxZoom: 30,
          zoom: this.zoom //设置地图缩放级别
        });

        this.createMarker(this.center.lat, this.center.lng, true);
      });
    },
    buildAMarker(latLng) {
      this.marker = new TMap.MultiMarker({
        map: this.tMap,
        styles: {
          // 点标记样式
          "default": new TMap.MarkerStyle({
            width: 28, // 样式宽
            height: 43, // 样式高
            src: this.isDonate ? this.iconDonate : this.iconAdopt,
          }),
        },
        geometries: [
          // 点标记数据数组
          {
            // 标记位置(纬度，经度，高度)
            position: latLng,
            id: 'marker',
          },
        ],
      });
    },
    createMarker(lat, lng, isInit) {
      if (this.readonly) {
        return;
      }

      this.buildAMarker(new TMap.LatLng(lat, lng))

      // TMap.event.addListener(this.marker, "dragend", (e) => {
      //   //拖动监听
      //   if (e.latLng) {
      //     this.position.lat = this.$utils.keepFourDecimal(e.latLng.lat);
      //     this.position.lng = this.$utils.keepFourDecimal(e.latLng.lng);
      //   }
      // });

      //拖动监听
      //  const editor = new TMap.GeometryEditor({
      //   map:this.tMap, // 编辑器绑定的地图对象
      //   overlayList: [{
      //     overlay: this.marker, // 可编辑图层
      //     id: "marker",
      //     selectedStyleId: "highlight"  // 被选中的marker会变为高亮样式
      //   }],
      //   activeOverlayId: "marker", // 激活图层
      //   selectable: true
      // });

      if (!isInit) {
        this.position.lat = lat;
        this.position.lng = lng;
      } else {
        //添加点击事件监听
        this.tMap.on("click", (e) => {
          if (e.latLng) {
            this.changeMarkerLocation(
              this.$utils.keepFourDecimal(e.latLng.lat),
              this.$utils.keepFourDecimal(e.latLng.lng)
            );
          }
        });
      }
    },
    clearMarker() {
      this.marker.setMap(null);
    },
    changeCenter(lat, lng) {
      this.tMap.panTo(new TMap.LatLng(lat, lng));
    },
    changeMarkerLocation(lat, lng) {
      //2023-11-15调整
      //更换标记位置
      // this.clearMarker();
      // this.createMarker(lat, lng);

      //更新当前标记点
      this.marker.updateGeometries({
        id: 'marker',
        position: new TMap.LatLng(lat, lng)
      });
      this.position.lng = lng;
      this.position.lat = lat;

      this.changeCenter(lat, lng);
    },
    getPoint(e) {
      if (this.readonly) {
        return;
      }
      this.position.lng = this.$utils.keepFourDecimal(e.point.lng);
      this.position.lat = this.$utils.keepFourDecimal(e.point.lat);
      if (geoc == null) {
        geoc = new BMap.Geocoder();
      }
      // geoc.getLocation(e.point, (res) => {
      //   this.position.address = res.address
      // })
    },
    handleChange() {
      let area = "";
      if (this.provinceText) {
        area += this.provinceText;
      }
      if (this.cityText) {
        area += this.cityText;
      }
      if (this.strictText) {
        area += this.strictText;
      }
      if (this.streetText) {
        area += this.streetText;
      }
      if (area) {
        let finalPlace = this.streetText.replace("(不限)", "");

        getDistrict(finalPlace)
          .then(res => {
            let result = res.result[0];
            for (let i = 0; i < result.length; i++) {
              if (result[i].address.indexOf("上海") != -1) {
                this.changeCenter(
                  result[i].location.lat,
                  result[i].location.lng
                );
                return;
              }
            }
          })
          .catch(e => {
            console.log(e);
          });
      }
    },
    handleLocation() {
      let area = "";
      if (this.provinceText) {
        area += this.provinceText;
      }
      if (this.cityText) {
        area += this.cityText;
      }
      if (this.strictText) {
        area += this.strictText;
      }
      if (this.streetText) {
        area += this.streetText;
      }

      if (area == "") {
        area = "上海市"
      }

      if (this.readonly) {
        return;
      }
      if (this.position.address) {
        const self = this;
        geocoder(area + this.position.address).then(res => {
          if (res.status == 0) {
            this.changeMarkerLocation(res.result.location.lat, res.result.location.lng)
          }
        }).catch(() => {

        })
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.bm-view {
  width: 100%;
  height: 500px;
}

::v-deep .el-dialog__body {
  padding: 10px !important;
}
</style>

<style rel="stylesheet/scss" lang="scss">
.citylist_popup_main .city_content_top {
  padding: 0 !important;
}

#city_ctrl_form {
  top: 0 !important;
}

#selCitySubmit {
  margin-top: 3px !important;
}
</style>
