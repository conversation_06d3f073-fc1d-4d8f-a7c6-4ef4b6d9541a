<template>
  <div class="table-opt">
    <span v-if="titleShow" class="optarea">
      <span style="font-weight:600; font-size: 14px; color:rgba(87,87,87,0.67)">{{ titleText }}</span>
      <el-button @click='showChartsDialog'  v-if="chartShow" size="mini" class="margin-left-button" type="primary" plain icon="el-icon-s-data">图表</el-button>
    </span>
    <span class="optarea">
      <el-link v-if="addShow" :underline="false" type="primary" icon="el-icon-circle-plus-outline" @click="handleAddItem">{{ addText }}</el-link>
      <span v-if="addShow && (batchAddShow || deleteShow)" class="splitbar"/>
      <el-link v-if="batchAddShow" :underline="false" type="primary" icon="el-icon-circle-check" @click="handleBatchAddItems">{{ batchAddText }}</el-link>
      <span v-if="batchAddShow && deleteShow" class="splitbar"/>
      <el-link v-if="deleteShow" :underline="false" type="danger" icon="el-icon-delete" @click="handleDeleteMultiItems">{{ deleteText }}</el-link>
    </span>
    <span v-for="(action, index) in customActions" v-if="action.hidden !== true" :key="index" class="optarea">
      <span v-if="action.hideSplitbar === undefined || action.hideSplitbar === false" class="splitbar"/>
      <el-link :icon="action.icon" :underline="false" :type="action.type" @click="action.func">{{ action.text }}</el-link>
    </span>
    <span class="sortarea">
      <span v-if="sortShow && exportShow" class="splitbar"/>
      <el-link v-if="exportShow" :underline="false" icon="el-icon-download" @click="handleExport">{{ exportText }}</el-link>
    </span>
    <span class="sortarea">
      <span v-if="sortShow" class="sort">
        <span>排序：</span>
        <span v-if="sortColumn === null || !sortColumn.order" class="name">
          <span class="text">无</span>
        </span>
        <span v-else class="name">
          <span class="text">{{ sortColumn.label }}</span>
          <i v-if="sortColumn.order === 'ascending'" class="icon-asc"/>
          <i v-else class="icon-desc"/>
        </span>
      </span>
    </span>
  </div>
</template>

<script>
export default {
  name: 'ElTableToolbar',
  props: {
    titleShow: {
      type: Boolean,
      default: () => false
    },
    titleText: {
      type: String,
      default: () => ''
    },
    addShow: {
      type: Boolean,
      default: () => true
    },
    addText: {
      type: String,
      default: () => '添加'
    },
    batchAddShow: {
      type: Boolean,
      default: () => false
    },
    batchAddText: {
      type: String,
      default: () => '批量添加'
    },
    deleteShow: {
      type: Boolean,
      default: () => false
    },
    deleteText: {
      type: String,
      default: () => '批量删除'
    },
    sortShow: {
      type: Boolean,
      default: () => true
    },
    sortColumn: {
      type: Object,
      default: function() {
        return null
      }
    },
    exportShow: {
      type: Boolean,
      default: () => false
    },
    exportText: {
      type: String,
      default: () => '导出'
    },
    customActions: {
      type: Array,
      default: () => []
    },
    chartShow:{
      type:Boolean,
      default:()=>false
    }
  },
  data() {
    return {}
  },
  methods: {
    handleAddItem: function() {
      const self = this
      self.$emit('handleAddItem', function() {
        // callback
      })
    },
    handleBatchAddItems: function() {
      const self = this
      self.$emit('handleBatchAddItems', function() {
        // callback
      })
    },
    handleDeleteMultiItems: function() {
      const self = this
      self.$emit('handleDeleteMultiItems', function() {
        // callback
      })
    },
    handleExport: function() {
      const self = this
      self.$emit('handleExport', function() {
        // callback
      })
    },
    showChartsDialog(){
      this.$emit('showChartsDialog')
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css" scoped>
  .table-opt {
    margin-bottom: 10px;
    height: 40px;
    border: 1px solid #d9dbe1;
    background-color: #FDFDFD;
    padding-left: 10px;
  }
  .optarea {
    line-height: 35px;
  }
  .sortarea {
    float: right;
    margin-right: 10px;
    line-height: 35px;
  }
  .sort {
    line-height: 40px;
    font-weight: 600;
    font-size: 14px;
    color: rgba(96, 96, 96, 0.87);
  }
  .sort .text {
    color: #38a3d5;
    font-size: 13px;
  }
  .icon-asc {
    margin-right: 3px;
    display: inline-block;
    width: 10px;
    height: 9px;
    background: url(../../../assets/image/px.png);
  }
  .icon-desc {
    margin-right: 3px;
    display: inline-block;
    width: 10px;
    height: 9px;
    background: url(../../../assets/image/px.png);
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    transition: transform .5s;
  }
  .splitbar {
    border-left:2px #ccc solid;
    margin-left: 5px;
    margin-right: 10px;
  }

  .margin-left-button{
    margin-left: 10px;
  }
</style>
