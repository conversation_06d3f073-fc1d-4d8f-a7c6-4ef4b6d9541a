<template>
  <el-upload
    :auto-upload="false"
    :multiple="multiple"
    :file-list="fileList"
    :accept="accept"
    :limit="10"
    :list-type="listType"
    :disabled="disabled"
    :http-request="handleHttpRequest"
    :on-exceed="handleExceed"
    :on-remove="handleRemove"
    :on-preview="handlePreview"
    :on-change="handleChange"
    :drag="false"
    action="#">
    <i class="el-icon-upload"/>
    <!--    <div class="el-upload__text" style="width: 320px">-->
    <!--      将文件拖到此处，或者<em>点击上传</em>-->
    <!--    </div>-->
  </el-upload>
</template>

<script>
export default {
  name: 'ElFileUpload',
  props: {
    // file: {
    //   type: Object,
    //   required: true
    // },
    accept: {
      type: String,
      default: () => ''
    },
    fileList: {
      type: Array,
      default: () => []
    },
    multiple: <PERSON><PERSON>an,
    listType: String
  },
  data() {
    return {
      disabled: false
    }
  },
  mounted() {
    if (this.fileList.length > 0) {
      this.fileList.map(item => {
        item.uid = item.uid || (Date.now() + this.tempIndex++)
        item.status = item.status || 'success'
        return item
      })
      const file = this.fileList[0]
      this.$emit('input', { raw: file.raw, update: file.status === 'ready' })
    }
  },
  methods: {
    handleChange(file, fileList) {
      const _file = fileList[0]
      this.$emit('input', { raw: _file.raw, update: _file.status === 'ready' })
    },
    handleExceed(files, fileList) {
      let postFiles = Array.prototype.slice.call(files)
      postFiles = postFiles.slice(0, 1).shift()

      const file = fileList.shift()
      file.name = postFiles.name
      file.size = postFiles.size
      file.raw = postFiles
      file.status = 'ready'
      fileList.push(file)
      const _file = fileList[0]
      this.$emit('input', { raw: _file.raw, update: _file.status === 'ready' })
    },
    handlePreview(file) {
    },
    handleHttpRequest(options) {
      // const file = options.file
      // if (file) {
      // }
    },
    handleRemove() {
      this.$emit('input', { raw: null, update: this.fileList.length > 0 })
    }
  }
}
</script>
