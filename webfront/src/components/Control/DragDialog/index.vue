<template>
  <el-dialog
    :key="dialogKey"
    v-drag-dialog
    :visible="dialogVisible"
    :append-to-body="appendToBody"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :title="title"
    :top="top"
    :custom-class="'dialog__width_' + width">
    <slot/>
    <span v-if="buttonAreaShow" slot="footer" class="dialog-footer">
      <el-button v-if="cancelButtonShow" style="width: 80px" @click="handleClose()">取 消</el-button>
      <el-button :loading="buttonLoading" :icon="okButtonIcon" :style="{width: okButtonWidth}" type="primary" @click="handleOk()">{{ okButtonText }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'ElDragDialog',
  props: {
    title: {
      type: String,
      required: true
    },
    appendToBody: {
      type: Boolean,
      default: () => false
    },
    width: {
      type: Number,
      default: () => 900
    },
    okButtonText: {
      type: String,
      default: () => '确 定'
    },
    okButtonWidth: {
      type: String,
      default: () => '80px'
    },
    okButtonIcon: {
      type: String,
      default: () => ''
    },
    cancelButtonShow: {
      type: Boolean,
      default: () => true
    },
    buttonLoading: {
      type: Boolean,
      default: () => false
    },
    buttonAreaShow: {
      type: Boolean,
      default: () => true
    },
    loading: {
      type: Boolean,
      default: () => false
    },
    top: {
      type: String,
      default: '15vh'
    }
  },
  data() {
    return {
      dialogKey: 0,
      dialogVisible: false,
      callback: null
    }
  },
  methods: {
    open(callback) {
      this.callback = callback || function(close) {
        close()
      }
      this.dialogVisible = true
    },
    openWithInit(init, callback) {
      init()
      this.callback = callback || function(close) {
        close()
      }
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogKey++
      this.handleResetFields()
    },
    handleResetFields() {
      const self = this
      this.$nextTick().then(() => {
        self.$emit('handleResetFields')
      }).then(() => {
        self.dialogVisible = false
      })
    },
    handleOk() {
      const self = this
      this.callback(() => {
        self.handleResetFields()
      })
    }
  }
}
</script>
