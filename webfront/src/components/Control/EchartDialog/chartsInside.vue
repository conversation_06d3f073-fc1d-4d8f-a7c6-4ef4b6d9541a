<template>
  <div>
    <div class="to-right">
      <el-radio-group v-model="tabPosition" style="margin-bottom: 30px;" @change="changeRadio">
        <el-radio-button label="bar">柱状图</el-radio-button>
        <el-radio-button label="line">折线图</el-radio-button>
        <el-radio-button label="pie">饼图</el-radio-button>
        <el-radio-button label="circle">圆环图</el-radio-button>
      </el-radio-group>
    </div>
    <div class="echarts-out" :id="'myEchart'+imageIndex">
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tabPosition: "bar",
      myChart: "",
      nowLegend: "",
      nowXAxis: "",
      nowYAxis: "",
      nowSeries: "",
      pieTitle: '',
      zoomShow: "",
      endValue: ""
    }
  },
  props: {
    data: {
      type: Object,
      default: {}
    },
    imageIndex: {
      type: Number
    }
  },
  watch: {
    'data'(newVal) {
      this.initData();
    }
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      let newVal = this.data;
      this.openWithInit(newVal.legend, newVal.xAxis, newVal.yAxis, newVal.series, newVal.pieTitle)
    },
    changeRadio() {
      this.openWithInit(this.nowLegend, this.nowXAxis, this.nowYAxis, this.nowSeries, this.pieTitle)
    },
    openWithInit(legend, xAxis, yAxis, series, pieTitle) {
      [this.nowLegend, this.nowXAxis, this.nowYAxis, this.nowSeries, this.pieTitle] = [legend, xAxis, yAxis, series, pieTitle]

      if (xAxis.length > 5 && (this.tabPosition == 'bar' || this.tabPosition == 'line')) {//this.xData是横轴的数据，通过后台动态获取的
        this.zoomShow = true;//通过横轴数据多少来判断滚动条是否显示(官网说滚动条不显示，但过滤数据的功能还在，过滤数据请移步官网去看具体配置项说明)
        this.endValue = (5 / xAxis.length) * 100;
      } else {
        this.zoomShow = false;
        this.endValue = 100;//这里一定要注意不显示滚动条的时候一定要把滚动条结束位置设置一下，不然会有bug
      }

      this.dialogVisible = true;

      this.$nextTick(() => {
        if (this.myChart == "") {
          this.myChart = this.$echarts.init(document.getElementById("myEchart" + this.imageIndex));
        } else {
          this.myChart.clear();
        }

        this.myChart.resize();

        let colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
        let colorsPie = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']

        series && series.forEach((item) => {
          if (this.tabPosition == 'circle') {
            item.radius = ['50', '90'];
            item.type = 'pie';
            item.itemStyle = {}
            item.label = {
              position: 'outside',

            }
          } else if (this.tabPosition == 'pie') {
            item.type = 'pie';
            item.radius = '90', '50';
            item.itemStyle = {}
            item.label = {
              position: 'outside'
            }
          } else {
            item.radius = '90';
            item.type = this.tabPosition;
            item.itemStyle = {
              normal: {
                label: {
                  show: true, //开启显示数值
                  position: 'top', //数值在上方显示
                  textStyle: {  //数值样式
                    fontSize: 14  //字体大小
                  }
                }
              }
            }
          }
        })

        let nowChartType = this.tabPosition;
        if (nowChartType == 'circle') {
          nowChartType = 'pie'
        }

        yAxis && yAxis.forEach((item, index) => {
          item.type = 'value';
          item.alignTicks = true;
          item.show = nowChartType != "pie";

          item.axisLine = {
            show: nowChartType != "pie",
            lineStyle: {
              color: colors[index]
            }
          };
        })

        // 指定图表的配置项和数据
        var option = {
          color: nowChartType == "pie" ? colorsPie : colors,
          title: nowChartType != "pie" ? [] : pieTitle,
          grid: {
            right: "12%"
          },

          dataZoom: [
            {
              orient: "horizontal",
              show: this.zoomShow,//控制滚动条显示隐藏
              realtime: true, //拖动滚动条时是否动态的更新图表数据
              height: 25, //滚动条高度
              start: 0, //滚动条开始位置（共100等份）
              end: this.endValue,//滚动条结束位置
              bottom: "4%",
              zoomLock: true, //控制面板是否进行缩放
              bottom: 5
            }],
          tooltip: {
            trigger: nowChartType == "pie" ? 'item' : 'axis',
            axisPointer: {
              type: nowChartType == "pie" ? 'shadow' : 'cross'
            },
            formatter: function (params) {
              if (params.seriesType == "pie") {
                let seriesName = params.seriesName;
                if (seriesName.search("率") != -1) {
                  return `${seriesName}：${params.value}%`
                } else {
                  return `${seriesName}：${params.value}`
                }
              }
              var relVal = params[0].name
              for (var i = 0, l = params.length; i < l; i++) {
                let name = params[i].seriesName;
                if (name.search("率") != -1) {
                  relVal += '<br/>' + params[i].marker + name + "&nbsp;&nbsp;&nbsp;" + params[i].value + '%'
                } else {
                  relVal += '<br/>' + params[i].marker + name + "&nbsp;&nbsp;&nbsp;" + params[i].value
                }

              }
              return relVal
            }
          },
          legend: {
            data: nowChartType == "pie" ? xAxis : legend
          },
          xAxis: {
            show: nowChartType != "pie",
            data: xAxis,
            offset: 10,
            axisLabel: {
              interval: 0,
              route: 90
            }
          },
          axisTick: {
            show: true,    // 是否显示坐标轴刻度
            length: 10,

          },
          yAxis: yAxis,
          series: series
        };

        // 使用刚指定的配置项和数据显示图表。
        this.myChart.setOption(option, true);

        //处理echarts不随浏览器变化
        window.onresize = () => {
          setTimeout( ()=> {
            this.myChart.resize();
          }, 100)
        };
      })
    }
  }

}
</script>

<style lang="css" scoped>
.to-right {
  display: flex;
  justify-content: flex-end;
  margin-right: 20px;
}

.echarts-out {
  height: 30rem;
  width: 100%;
  margin-bottom: 5rem;
}
.echarts {
  height: 100%;
  width: 100%;
}
</style>