<template>
  <el-dialog :visible.sync="dialogVisible" :title="titleText" width="70%">

    <div class="scroll">
      <charts-inside v-for="(item,index) in nowChartsData" :key="index" :data="item" :imageIndex="index"></charts-inside>
    </div>

  </el-dialog>
</template>

<script>
import chartsInside from "./chartsInside.vue"
export default {
  name: 'EchartDialog',
  components: {
    chartsInside
  },
  props: {
    titleText: {
      type: String,
      default: () => '',
    }

  },
  data() {
    return {
      dialogVisible: false,
      tabPosition: "bar",
      myChart: "",

      nowLegend: "",
      nowXAxis: "",
      nowYAxis: "",
      nowSeries: "",
      pieTitle: '',

      nowChartsData: [],

      zoomShow: "",
      endValue: ""
    }
  },
  methods: {
    changeRadio() {
      this.openWithInit(this.nowChartsData)
    },
    openWithInit(nowChartsData) {
      this.nowChartsData = nowChartsData;
      this.dialogVisible = true;
    }

  }
}
</script>

<style lang="css">
.scroll {
  height: 40rem;
  overflow: auto;
  overflow-x:hidden;
}
</style>