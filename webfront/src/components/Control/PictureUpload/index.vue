<template>
  <div :class="{'pic-upload-disabled': disableControl || full}" style="display: inline-block">
    <el-upload
      :auto-upload="false"
      :multiple="limit > 1"
      :file-list="fileList"
      :accept="acceptType"
      :limit="limit"
      :disabled="disabled"
      :http-request="handleHttpRequest"
      :on-exceed="handleExceed"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :on-change="handleChange"
      :before-remove="beforeRemove"
      :drag="false"
      list-type="picture-card"
      action="#">
      <i slot="default" class="el-icon-plus"></i>
      <!--    <div slot="file" slot-scope="{file}">-->
      <!--      <img-->
      <!--        class="el-upload-list__item"-->
      <!--        :src="file.url" alt=""-->
      <!--      >-->
      <!--      <span>{{file.name}}</span>-->
      <!--    </div>-->
      <!--    <div class="p-file-upload">-->
      <!--      <div class="p-dropzone">-->
      <!--        <p class="p-dropzone__text">ここにファイルをドロップ<span class="p-dropzone__small">または</span></p>-->
      <!--        <el-button class="p-file-input c-btn p-btn-enter">ファイル選択</el-button>-->
      <!--      </div>-->
      <!--    </div>-->
    </el-upload>
    <el-image-viewer v-if="dialogVisible" :on-close="closeViewer" :url-list="viewImageList()"/>
  </div>
</template>

<script>
import ElImageViewer from '../PhotoUpload/image-viewer'
export default {
  name: 'ElPictureUpload',
  components: { ElImageViewer },
  props: {
    value: {
      type: Object,
      default: () => {}
    },
    acceptType: {
      type: String,
      default: () => '.png,.jpg,.jpeg,.PNG'
    },
    limit: {
      type: Number,
      default: () => 1
    },
    disabled: Boolean,
    downloadFunc: {
      type: Function,
      default: () => (file) => {}
    },
    maxSize: {
      type: Number,
      default: () => -1
    },
    beforeSelectCheck: Function
  },
  data() {
    return {
      dialogVisible: false,
      deleted: [],
      full: false
    }
  },
  computed: {
    fileList: {
      get() {
        const files = this.value.files || []
        return files.filter(e => e.status !== 'remove')
      },
      set(val) {
        this.value.files = val
        const size = this.value.files.filter(file => file.status !== 'remove').length
        this.full = size >= this.limit
        this.$emit('input', this.value)
      }
    },
    disableControl() {
      const size = this.value.files.filter(file => file.status !== 'remove').length
      return this.disabled || size >= this.limit
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(value) {
        if (value === null) {
          this.$emit('input', {
            files: []
          })
          return
        }
        if (value.files === undefined || value.files === null || !(value.files instanceof Array)) {
          value.files = []
        }
        // if (value.type !== '_file_') {
        // }
        Object.defineProperty(value, 'type', { value: '_file_' })
      }
    }
  },
  methods: {
    handleChange(file, fileList) {
      // if (!this.$utils.fileSuffixCheck(file)) {
      //   fileList.pop()
      //   this.$message.error(file.name + 'は禁止されているファイルの種類です。')
      //   return
      // }
      if (this.acceptType) {
        if (!this.checkFile(file.name)) {
          fileList.pop()
          this.$message.error('[' + file.name + ']文件不是图片类型，请上传图片类型。')
          return
        }
      }
      if (this.maxSize !== -1 && file && file.size > this.maxSize) {
        fileList.pop()
        this.$message.error('')
        return
      }
      if (this.beforeSelectCheck) {
        const result = this.beforeSelectCheck(file)
        if (result === false) {
          fileList.pop()
          return
        }
      }
      const removed = this.value.files.filter(file => file.status === 'remove')
      this.fileList = fileList.concat(...removed)
      this.$emit('change', file, this.value)
    },
    viewImageList() {
      return this.value.files.map(e => e.url)
    },
    handleExceed(files, fileList) {
      // const list = []
      // list.push(...fileList)
      // for (let i = 0; i < files.length; i++) {
      //   const raw = files[i]
      //   const file = {
      //     name: raw.name,
      //     percentage: 0,
      //     raw: raw,
      //     size: raw.size,
      //     status: 'ready',
      //     uid: raw.uid
      //   }
      //   list.push(file)
      // }
      // let ff = []
      // if (list.length <= this.limit) {
      //   ff = list
      // } else {
      //   ff = list.slice(list.length - this.limit, list.length + 1)
      // }
      // const removed = this.value.files.filter(f => f.status === 'remove')
      // this.fileList = ff.concat(...removed)
      this.$message.error('文件超过了最大上传件数。')
    },
    handlePreview(file) {
      this.dialogVisible = true
    },
    handleHttpRequest(options) {
    },
    closeViewer() {
      this.dialogVisible = false
    },
    handleRemove(file, fileList) {
      if (file.status === 'success') {
        file.status = 'remove'
      }
      const removed = this.value.files.filter(f => f.status === 'remove')
      this.fileList = fileList.concat(...removed)
      this.$emit('remove', file, this.value)
    },
    async beforeRemove(file, fileList) {
      if (file.status === 'success') {
        const confirm = await this.$confirm('确定删除该图片吗？', '提示')
        return confirm === 'confirm'
      }
      return true
    },
    checkFile(name) {
      const extension = name.indexOf('.') > -1
        ? `.${name.split('.').pop()}`
        : ''
      if (extension === '') {
        return false
      }
      const accept = []
      if (this.acceptType.indexOf(',') !== -1) {
        accept.push(...this.acceptType.split(','))
      } else {
        accept.push(this.acceptType)
      }
      return accept.filter(c => c === extension).length !== 0
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.pic-upload-disabled {
  .el-upload--picture-card {
    display: none;
  }
}
//.file-upload {
//  width: 80%;
//}
//::v-deep a {
//  text-decoration: none !important;
//}
//::v-deep .el-upload {
//  width: 100%;
//}
//::v-deep .el-upload-dragger {
//  border: none !important;
//  width: 100% !important;
//  height: 100% !important;
//  color: #fad20c;
//}
//::v-deep .el-upload-list__item {
//  margin-top: 2px !important;
//}
//::v-deep .is-error {
//  .p-dropzone {
//    border-color: red;
//  }
//}
</style>
