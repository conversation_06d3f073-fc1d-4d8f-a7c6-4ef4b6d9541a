<template>
  <div style="display: inline-block">
    <el-upload
      v-if="image === '' && editable"
      :show-file-list="false"
      :on-change="handleAvatarChange"
      :auto-upload="false"
      :style="{'width': w, 'height': h, 'line-height': h}"
      action="#"
      class="avatar-uploader"
      accept="image/jpeg,image/gif,image/png,image/bmp">
      <i slot="default" class="el-icon-plus avatar-uploader-icon"/>
    </el-upload>
    <div v-else style="position: relative">
      <img :src="image" :style="{'width': w, 'height': h}" class="avatar" alt="">
      <div v-if="image !== '' || editable" :style="{'width': w, 'height': h, 'line-height': lh}" class="show-div">
        <span v-if="image !== ''" class="action" @click="handlePictureCardPreview(file)">
          <i class="el-icon-zoom-in"/>
        </span>
        <span v-if="editable" class="action" @click="handleRemove(file)">
          <i class="el-icon-delete"/>
        </span>
      </div>
    </div>
    <el-image-viewer v-if="dialogVisible" :on-close="closeViewer" :url-list="viewImageList()"/>
  </div>
</template>

<script>
import ElImageViewer from './image-viewer'
export default {
  name: 'ElPhotoUpload',
  components: {
    'el-image-viewer': ElImageViewer
  },
  props: {
    listType: {
      type: String,
      default: () => 'picture'
    },
    imgData: {
      type: String,
      default: () => ''
    },
    preViewImgList: {
      type: Array,
      default: () => []
    },
    width: {
      type: Number,
      default: () => 100
    },
    height: {
      type: Number,
      default: () => 100
    },
    editable: {
      type: Boolean,
      default: () => true
    }
  },
  data() {
    return {
      image: '',
      file: null,
      dialogVisible: false,
      disabled: false,
      h: '',
      w: '',
      lh: ''
    }
  },
  watch: {
    'image': function(nv) {
      this.$emit('update:imgData', nv)
    },
    'imgData': function(nv) {
      this.image = nv
    }
  },
  mounted() {
    this.w = this.width + 'px'
    this.h = this.height + 'px'
    this.lh = (parseInt(this.height) * 1.3) + 'px'
    this.image = this.imgData
  },
  methods: {
    handleRemove(file) {
      this.image = ''
      this.$emit('handleImageChange', this.image)
    },
    handlePictureCardPreview(file) {
      const self = this
      // self.$emit('handleRefreshViewList', function() {
      //   self.dialogVisible = true
      // })
      self.dialogVisible = true
    },
    handleAvatarChange(file, fileList) {
      const _self = this
      const type = file.raw.type
      const isJPG = type === 'image/jpeg'
      const isGIF = type === 'image/gif'
      const isPNG = type === 'image/png'
      const isBMP = type === 'image/bmp'
      const isLt2M = file.raw.size / 1024 / 1024 < 16

      if (!isJPG && !isGIF && !isPNG && !isBMP) {
        this.$message.error('上传图片必须是JPG/GIF/PNG/BMP 格式!')
        return
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 16MB!')
        return
      }

      this.file = file
      this.imgToBase64(file.raw, function(img, width) {
        _self.image = img
        _self.$emit('handleImageChange', _self.image)
      })
    },
    closeViewer() {
      this.dialogVisible = false
    },
    viewImageList() {
      if (this.preViewImgList.length > 0) {
        return this.preViewImgList
      }
      return [this.image]
    },
    imgToBase64(file, callback) {
      const URL = window.URL || window.webkitURL
      const url = URL.createObjectURL(file)
      // 将图片转换为Base64
      let canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()
      img.crossOrigin = 'Anonymous'
      img.onload = function() {
        canvas.height = img.height
        canvas.width = img.width
        ctx.drawImage(img, 0, 0)
        const base64 = canvas.toDataURL(file.type)
        callback(base64, img.width, img.height)
        canvas = null
      }
      img.src = url
    },
    cutImgToBase64(url, wid, quality, callback) {
      const img = new Image()
      img.crossOrigin = 'Anonymous'
      img.onload = function() {
        const that = this
        // 生成比例
        let w = that.width
        let h = that.height
        const scale = w / h
        w = wid || w
        h = w / scale
        // 生成canvas
        let canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        canvas.height = h
        canvas.width = w

        ctx.drawImage(that, 0, 0, w, h)
        // 生成base64
        const base64 = canvas.toDataURL('image/jpeg', quality || 0.8)
        callback(base64, w, h)
        canvas = null
      }
      img.src = url
    }
  }
}
</script>

<style scoped>
  .avatar-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    text-align: center;
  }

  .avatar-uploader:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }

  .avatar {
    display: block;
    border: 1px dashed #d9d9d9;
  }

  .show-div {
    position: absolute;
    top: 0px;
    display: block;
    text-align: center;
  }

  .show-div:hover {
    filter: alpha(Opacity=60);
    -moz-opacity: 0.6;
    opacity: 0.6;
    background-color: #1b1e21;
  }

  .show-div span {
    display: none;
    margin: 10px;
  }

  .show-div:hover span {
    display: inline;
    font-size: 28px;
    color: #fff;
  }
</style>
