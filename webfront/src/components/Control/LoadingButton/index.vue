<template>
  <el-button :type="type" :icon="icon" :circle="circle" :plain="plain" :autofocus="autofocus" :disabled="disabled" :loading="loading || innerLoading" :size="size" @click="handleChick">
    <span v-if="$slots.default && !(loadingNoText && (loading || innerLoading))">
      <slot/>
    </span>
  </el-button>
</template>

<script>
export default {
  name: 'ElLoadingButton',
  props: {
    type: {
      type: String,
      default: function() {
        return 'primary'
      }
    },
    icon: {
      type: String,
      default: function() {
        return ''
      }
    },
    disabled: {
      type: <PERSON>olean,
      default: function() {
        return false
      }
    },
    circle: {
      type: Boolean,
      default: function() {
        return false
      }
    },
    size: {
      type: String,
      default: function() {
        return ''
      }
    },
    autofocus: {
      type: Boolean,
      default: function() {
        return true
      }
    },
    plain: {
      type: <PERSON>olean,
      default: function() {
        return false
      }
    },
    loadingNoText: {
      type: <PERSON><PERSON><PERSON>,
      default: function() {
        return false
      }
    },
    loading: {
      type: <PERSON><PERSON><PERSON>,
      default: function() {
        return false
      }
    }
  },
  data() {
    return {
      innerLoading: false
    }
  },
  watch: {
    loading(nv) {
      if (nv === false) {
        this.innerLoading = false
      }
    }
  },
  methods: {
    handleChick: function(evt) {
      const self = this
      self.innerLoading = true
      self.$emit('click', function() {
        setTimeout(function() {
          self.innerLoading = false
        }, 300)
      }, evt)
    }
  }
}
</script>
