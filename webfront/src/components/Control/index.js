import Vue from 'vue'

import LoadingButton from './LoadingButton'
import GroupPanel from './GroupPanel'
import MultiPanel from './MultiplePanel'
import PanelHeader from './MultiplePanel/PanelHeader'
import ElSearchHeader from './SearchHeader'
import ElTableToolbar from './TableToolbar'
import ElClipboardInput from './ClipboardInput'
import ElPasswordInput from './PasswordInput'
import ElTextShow from './TextShow'
import FormItemNoError from './FormItemNoError'
import ElFileUpload from './FileUpload'
import ElTableColumnWrap from './TableColumnWrap'
import ElDragDialog from './DragDialog'
import ElPaginationTable from './PaginationTable'
import ElPulldown from './Pulldown'
import ElEditableTable from './EditableTable'
import ElEditableTableColumn from './EditableTableColumn'
import PhotoUpload from './PhotoUpload'
import PhotoMultipleUpload from './PhotoMultipleUpload'
import GeoCoordinates from './GeoCoordinates'
import ElPictureUpload from './PictureUpload'
import AreaCascader from './AreaCascader'
import ElDateRange from './DateRange'
import EchartDialog from "./EchartDialog"

Vue.component(LoadingButton.name, LoadingButton)
Vue.component(GroupPanel.name, GroupPanel)
Vue.component(MultiPanel.name, MultiPanel)
Vue.component(PanelHeader.name, PanelHeader)
Vue.component(ElSearchHeader.name, ElSearchHeader)
Vue.component(ElTableToolbar.name, ElTableToolbar)
Vue.component(ElClipboardInput.name, ElClipboardInput)
Vue.component(ElPasswordInput.name, ElPasswordInput)
Vue.component(ElTextShow.name, ElTextShow)
Vue.component(FormItemNoError.name, FormItemNoError)
Vue.component(ElFileUpload.name, ElFileUpload)
Vue.component(ElTableColumnWrap.name, ElTableColumnWrap)
Vue.component(ElDragDialog.name, ElDragDialog)
Vue.component(ElPaginationTable.name, ElPaginationTable)
Vue.component(ElPulldown.name, ElPulldown)
Vue.component(ElEditableTable.name, ElEditableTable)
Vue.component(ElEditableTableColumn.name, ElEditableTableColumn)
Vue.component(PhotoUpload.name, PhotoUpload)
Vue.component(PhotoMultipleUpload.name, PhotoMultipleUpload)
Vue.component(GeoCoordinates.name, GeoCoordinates)
Vue.component(ElPictureUpload.name, ElPictureUpload)
Vue.component(AreaCascader.name, AreaCascader)
Vue.component(ElDateRange.name, ElDateRange)
Vue.component(EchartDialog.name,EchartDialog)
