<template>
  <el-table-column
    :show-overflow-tooltip="showOverflowTooltip"
    :label="label"
    :prop="prop"
    :width="width"
    style="word-wrap:break-word;">
    <template slot-scope="scope">
      <div v-for="(text, index) in textArray(scope.row[prop])" :key="index">{{ text }}</div>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: 'ElTableColumnWrap',
  props: {
    prop: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    showOverflowTooltip: {
      type: Boolean,
      default: () => false
    },
    width: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      texts: []
    }
  },
  methods: {
    textArray: function(prop) {
      return prop.split('<br/>')
    }
  }
}
</script>
