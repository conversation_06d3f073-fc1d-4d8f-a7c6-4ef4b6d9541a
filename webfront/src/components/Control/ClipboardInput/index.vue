<template>
  <el-input v-model="currentValue" :value="value" :readonly="readonly" :clearable="clearable" :disabled="disabled" @input="handleInput">
    <template slot="append">
      <el-tooltip :enterable="false" content="copy to clipboard" placement="right-start" effect="light">
        <el-button v-clipboard:copy="currentValue" class="el-button--input-clipboard" style="width: 45px; height: 29px; margin: 0 -20px" icon="el-icon-document-copy" size="mini"/>
      </el-tooltip>
    </template>
  </el-input>
</template>

<script>
export default {
  name: 'ElClipboardInput',
  props: {
    value: {
      type: [String, Number],
      default: () => null
    },
    readonly: {
      type: <PERSON><PERSON>an,
      default: () => false
    },
    clearable: {
      type: <PERSON>olean,
      default: () => false
    },
    disabled: {
      type: <PERSON>olean,
      default: () => false
    }
  },
  data: function() {
    return {
      currentValue: this.value
    }
  },
  watch: {
    value(val) {
      this.currentValue = val
    }
  },
  methods: {
    handleInput(event) {
      this.$emit('input', this.currentValue)
    }
  }
}
</script>
