<template>
  <div :style="{ width }" class="text-span">{{ text }}</div>
</template>

<script>
export default {
  name: 'ElTextShow',
  props: {
    text: {
      type: String,
      required: true
    },
    width: {
      type: String,
      default: () => '480px'
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css" scoped>
  .text-span {
    display: inline-block;
    word-break: break-word;
    margin: 0 0;
    color: #7b7b7b;
  }
</style>
