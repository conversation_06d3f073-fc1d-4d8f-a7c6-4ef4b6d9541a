<template>
  <el-header style="width: 100%; min-height: 60px; display: inline-block" height="">
    <div :style="{'width': 'calc(100vw - ' + btnAreaWidth + 'px - ' + sidebarWidth + 'px)'}" class="left-area-div">
      <slot/>
    </div>
    <div :style="{'width': btnAreaWidth + 'px'}" class="right-area-div">
      <el-button v-for="button in customButtons" :key="button.text" :loading="button.loading" :icon="button.icon" :type="button.type || 'primary'" @click="() => handleCustomAction(button.action)">{{ button.text }}</el-button>
      <el-button v-if="searchBtnShow" id = "searchBtn" :loading="searchLoading" type="primary" icon="el-icon-search" @click="search">{{ searchBtnText }}</el-button>
      <el-button v-if="resetBtnShow" id = "resetBtn" type="" icon="el-icon-refresh-left" @click="reset">重置</el-button>
    </div>
  </el-header>
</template>

<script>
export default {
  name: 'ElSearchHeader',
  props: {
    searchBtnShow: {
      type: Boolean,
      default: () => true
    },
    searchBtnText: {
      type: String,
      default: () => '检索'
    },
    resetBtnShow: {
      type: Boolean,
      default: () => true
    },
    searchLoading: {
      type: Boolean,
      default: () => false
    },
    customButtons: {
      type: Array,
      default: () => []
    },
    handleSearch: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      btnAreaWidth: 0
    }
  },
  computed: {
    sidebarWidth() {
      return this.$store.state.app.sidebar.opened ? 330 : 150
    }
  },
  mounted() {
    const w = 110
    if (this.searchBtnShow) {
      this.btnAreaWidth += w
    }
    if (this.searchBtnShow) {
      this.btnAreaWidth += w
    }
    this.customButtons.forEach(b => {
      this.btnAreaWidth += b.width || w
    })
  },
  methods: {
    async search() {
      this.$emit('update:searchLoading', true)
      await this.handleSearch()
      this.$emit('update:searchLoading', false)
    },
    reset() {
      this.$emit('reset')
    },
    handleCustomAction(action) {
      action && action()
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css" scoped>
  .left-area-div {
    float: left;
  }
  .right-area-div {
    text-align: right;
    float: right;
  }
</style>
