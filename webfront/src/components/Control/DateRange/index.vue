<template>
  <div
    :class="{'is-active' : isActive}"
    class="date-range__out el-range-editor--small el-input__inner"
    @mouseenter="hovering = true"
    @mouseleave="hovering = false">
    <i :class="['el-input__icon', 'el-range__icon', triggerClass]"/>
    <el-date-picker
      v-model="startDate"
      :clearable="false"
      :type="type"
      :placeholder="startPlaceholderShow"
      :default-time="startDefaultTime"
      :validate-event="false"
      :format="dateFormat"
      :value-format="dateFormat"
      :readonly="readonly"
      :picker-options="startPickerOptions"
      align="right"
      prefix-icon="null"
      class="date-range__inner el-range-input"
      @focus="handleFocus"
      @blur="handleBlur"/>
    <span class="el-range-separator">{{ rangeSeparator }}</span>
    <el-date-picker
      v-model="endDate"
      :clearable="false"
      :type="type"
      :placeholder="endPlaceholderShow"
      :default-time="endDefaultTime"
      :validate-event="false"
      :format="dateFormat"
      :value-format="dateFormat"
      :readonly="readonly"
      :picker-options="endPickerOptions"
      align="right"
      prefix-icon="null"
      class="date-range__inner el-range-input"
      @focus="handleFocus"
      @blur="handleBlur"/>
    <span v-if="showClear" class="el-input__suffix">
      <span class="el-input__suffix-inner">
        <i class="el-input__icon el-range__close-icon el-icon-circle-close" @click="handleClear"/>
      </span>
    </span>
  </div>
</template>

<script>
import Emitter from '@/common/directive/mixins/emitter'
// import ClickOutside from 'element-ui/src/utils/clickoutside'
// import { parseDate } from 'element-ui/src/utils/date-util'

// -----parseDate----
const parseDate = function(str, type) {
  if (type === 'date') {
    const dateStrs = str.split('-')
    const year = parseInt(dateStrs[0], 10)
    const month = parseInt(dateStrs[1], 10) - 1
    const day = parseInt(dateStrs[2], 10)
    return new Date(year, month, day)
  } else {
    const tempStrs = str.split(' ')
    const dateStrs = tempStrs[0].split('-')
    const year = parseInt(dateStrs[0], 10)
    const month = parseInt(dateStrs[1], 10) - 1
    const day = parseInt(dateStrs[2], 10)

    const timeStrs = tempStrs[1].split(':')
    const hour = parseInt(timeStrs[0], 10)
    const minute = parseInt(timeStrs[1], 10)
    const second = parseInt(timeStrs[2], 10)

    return new Date(year, month, day, hour, minute, second)
  }
}

const clearTime = function(date) {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate())
}

const isDateObject = function(val) {
  return val instanceof Date
}

const validator = function(val) {
  // either: Array of String, null / undefined
  return (
    val === null ||
      val === undefined ||
      (Array.isArray(val) /* && val.length === 2 */ && val.every((d) => isString(d) || isNull(d)))
  )
}

const isString = function(val) {
  return typeof val === 'string' || val instanceof String
}

const isNull = function(val) {
  return val === null || val === undefined
}

const valueEquals = function(a, b) {
  // considers Date object and string
  const dateEquals = function(a, b) {
    const aIsDate = a instanceof Date
    const bIsDate = b instanceof Date
    if (aIsDate && bIsDate) {
      return a.getTime() === b.getTime()
    }
    if (!aIsDate && !bIsDate) {
      return a === b
    }
    return false
  }

  const aIsArray = a instanceof Array
  const bIsArray = b instanceof Array
  if (aIsArray && bIsArray) {
    if (a.length !== b.length) {
      return false
    }
    return a.every((item, index) => dateEquals(item, b[index]))
  }
  if (!aIsArray && !bIsArray) {
    return dateEquals(a, b)
  }
  return false
}

export default {
  name: 'ElDateRange',
  // directives: { ClickOutside },
  mixins: [Emitter],
  inject: {
    elForm: {
      default: ''
    },
    elFormItem: {
      default: ''
    }
  },
  props: {
    value: {
      type: Array,
      default: () => [],
      validator
    },
    type: {
      type: String,
      default: () => 'date' // date, datetime
    },
    startDefaultTime: {
      type: String,
      default: () => '00:00:00'
    },
    endDefaultTime: {
      type: String,
      default: () => '00:00:00'
    },
    startPlaceholder: {
      type: String,
      default: () => null
    },
    endPlaceholder: {
      type: String,
      default: () => null
    },
    rangeSeparator: {
      type: String,
      default: () => '~'
    },
    // format: {
    //   type: String,
    //   default: () => null // 'yyyy-MM-dd', 'yyyy-MM-dd HH:mm:ss'
    // },
    validateEvent: {
      type: Boolean,
      default: () => true
    },
    readonly: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      startDate: null,
      endDate: null,
      showClose: true,
      hovering: false,
      focused: false
    }
  },
  computed: {
    dateFormat() {
      return /* this.format || */ this.type === 'date' ? 'yyyy-MM-dd' : 'yyyy-MM-dd HH:mm:ss'
    },
    isActive() {
      return this.focused === true
    },
    showClear() {
      return !this.readonly && ((this.startDate !== null) || (this.endDate !== null)) && (this.focused || this.hovering)
    },
    triggerClass() {
      return this.type !== 'date' ? 'el-icon-time' : 'el-icon-date'
    },
    startDateWithoutTime() {
      if (this.startDate) {
        const start = parseDate(this.startDate, this.type)
        if (isDateObject(start)) {
          return clearTime(start)
        }
      }
      return null
    },
    endDateWithoutTime() {
      if (this.endDate) {
        const end = parseDate(this.endDate, this.type)
        if (isDateObject(end)) {
          return clearTime(end)
        }
      }
      return null
    },
    startPickerOptions() {
      const self = this
      return {
        disabledDate(time) {
          if (self.endDateWithoutTime) {
            return time.getTime() > self.endDateWithoutTime
          }
          return false
        }
      }
    },
    endPickerOptions() {
      const self = this
      return {
        disabledDate(time) {
          if (self.startDateWithoutTime) {
            return time.getTime() < self.startDateWithoutTime
          }
          return false
        }
      }
    },
    startPlaceholderShow() {
      return this.startPlaceholder || this.type === 'date' ? '开始日期' : '开始时间'
    },
    endPlaceholderShow() {
      return this.endPlaceholder || this.type === 'date' ? '结束日期' : '结束时间'
    }
  },
  watch: {
    startDate(nv) {
      if (nv === null && this.endDate === null) {
        this.$emit('input', [])
      } else {
        this.$emit('input', [nv, this.endDate])
      }
    },
    endDate(nv) {
      if (nv === null && this.startDate === null) {
        this.$emit('input', [])
      } else {
        this.$emit('input', [this.startDate, nv])
      }
    },
    value(val, oldVal) {
      this.setValue(val)
      if (!valueEquals(val, oldVal) && this.validateEvent) {
        this.$emit('change', val)
        if (this.validateEvent) {
          let v = val
          if (val.length === 0 || (val[0] === null && val[1] === null)) {
            v = []
          }
          this.dispatch('ElFormItem', 'el.form.change', v)
        }
      }
    }
  },
  mounted() {
    this.setValue(this.value)
  },
  methods: {
    handleClear() {
      this.startDate = null
      this.endDate = null
    },
    handleFocus(event) {
      setTimeout(() => {
        this.focused = true
      }, 200)
      this.$emit('focus', event)
    },
    handleBlur() {
      this.focused = false
      this.$emit('blur', event)
      if (this.validateEvent) {
        let v = this.value
        if (this.value.length === 0 || (this.value[0] === null && this.value[1] === null)) {
          v = []
        }
        this.dispatch('ElFormItem', 'el.form.blur', v)
      }
    },
    setValue(val) {
      if (val && Array.isArray(val)) {
        if (val.length > 0) {
          this.startDate = val[0]
        } else {
          this.startDate = null
        }
        if (val.length > 1) {
          this.endDate = val[1]
        } else {
          this.endDate = null
        }
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
  .date-range__out.is-active {
    border-color: #409EFF;
  }

  .date-range__out {
    line-height: 32px;
    padding-left: 0;
    min-width: 300px;

    .el-input__inner {
      text-align: center !important;
    }

    .el-range__icon {
      font-size: 14px;
      margin-left: 5px;
      color: #C0C4CC;
      float: left;
      line-height: 32px;
    }

    .date-range__inner {
      width: 39%;
    }

    .date-range__inner {
      .el-input__inner {
        border: 0 solid #FFF;
        height: 26px;
        padding: 0 10px;
      }
    }
  }
</style>
