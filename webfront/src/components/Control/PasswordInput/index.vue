<template>
  <el-input
    v-model="currentValue"
    :value="value"
    :type="pwdType"
    :clearable="clearable"
    :disabled="disabled"
    :readonly="innerReadonly"
    autocomplete="off"
    @focus="resetReadonly()"
    @input="handleInput">
    <template slot="append">
      <el-tooltip :content="pwdType === 'password' ? 'show password' : 'hide password'" :enterable="false" placement="right-start" effect="light">
        <el-button class="el-button--input-clipboard" style="width: 45px; height: 28px; margin: 0 -20px" size="mini" @click="showPwd()">
          <svg-icon :icon-class="pwdType === 'password' ? 'eye' : 'eye-open'" class="svg-icon-password" style="margin-left: -5px; margin-top: -5px" />
        </el-button>
      </el-tooltip>
    </template>
  </el-input>
</template>

<script>
export default {
  name: 'ElPasswordInput',
  props: {
    value: {
      type: [String, Number],
      default: () => null
    },
    readonly: {
      type: Boolean,
      default: () => false
    },
    hidePwd: {
      type: <PERSON><PERSON>an,
      default: () => false
    },
    clearable: {
      type: Boolean,
      default: () => false
    },
    disabled: {
      type: Boolean,
      default: () => false
    }
  },
  data: function() {
    return {
      pwdType: 'password',
      innerReadonly: true,
      currentValue: this.value
    }
  },
  watch: {
    value(val) {
      this.currentValue = val
    },
    hidePwd(val) {
      if (val === true) {
        this.pwdType = 'password'
      } else {
        this.pwdType = 'text'
      }
    },
    readonly(val) {
      this.innerReadonly = val === true
    }
  },
  methods: {
    handleInput() {
      this.$emit('input', this.currentValue)
    },
    showPwd() {
      if (this.pwdType === 'password') {
        this.pwdType = 'text'
      } else {
        this.pwdType = 'password'
      }
    },
    resetReadonly() {
      if (this.readonly !== true) {
        this.innerReadonly = false
      }
    }
  }
}
</script>
