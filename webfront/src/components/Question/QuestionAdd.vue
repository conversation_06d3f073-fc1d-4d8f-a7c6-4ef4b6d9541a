<template>
  <div :style="{'background': mouseover ? '#FAFAFA' : ''}" class="div-question-add" @mouseenter="mouseover = true" @mouseleave="mouseover = false">
    <div v-if="popover" tabindex="0" class="div-ques-add-border">
      <el-question-add-popover ref="popover" @handleChoiceConfirm="handleAdd">
        <i slot="reference" class="el-icon-plus">{{ buttonText }}</i>
      </el-question-add-popover>
    </div>
    <div v-else class="div-ques-add-border" @click="handleAdd">
      <i class="el-icon-plus">{{ buttonText }}</i>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ElQuestionAdd',
  props: {
    popover: {
      type: Boolean,
      default: () => true
    },
    buttonText: {
      type: String,
      default: () => '添加问题'
    }
  },
  data() {
    return {
      mouseover: false
    }
  },
  methods: {
    handleAdd(type) {
      this.$emit('handleInsert', null, type)
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css" scoped>
  .div-question-add {
    width: 850px;
    height: 220px;
    outline: none;
    padding-top: 35px;
    border: none;
    text-align: center;
    display: inline-block;
    border-bottom: 1px solid #E0E0E0;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
  }
  .div-ques-add-border:hover {
    border: 1px dashed #409eff;
  }
  .div-ques-add-border {
    background-color: #fbfdff;
    border: 1px dashed #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 248px;
    height: 128px;
    line-height: 128px;
    vertical-align: top;
    display: block;
    text-align: center;
    cursor: pointer;
    outline: 0;
    margin: auto;
  }
</style>
