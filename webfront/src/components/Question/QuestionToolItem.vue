<template>
  <div class="div-tool-btn">
    <el-popover v-if="type === 'radio'" :title="title" placement="right" width="500" trigger="hover">
      <el-radio-group v-model="radioTemplate">
        <div class="div-question-option">
          <el-radio :label="3">选项1</el-radio>
        </div>
        <div class="div-question-option">
          <el-radio :label="6">选项2</el-radio>
        </div>
        <div class="div-question-option">
          <el-radio :label="9">选项3</el-radio>
        </div>
      </el-radio-group>
      <div slot="reference">
        <slot/>
      </div>
    </el-popover>
    <el-popover v-else-if="type === 'checkbox'" :title="title" placement="right" width="500" trigger="hover">
      <el-checkbox-group v-model="checkBoxTemplate">
        <div class="div-question-option">
          <el-checkbox label="复选框 A"/>
        </div>
        <div class="div-question-option">
          <el-checkbox label="复选框 B"/>
        </div>
        <div class="div-question-option">
          <el-checkbox label="复选框 C"/>
        </div>
      </el-checkbox-group>
      <div slot="reference">
        <slot/>
      </div>
    </el-popover>
    <el-popover v-else-if="type === 'singleCompletion'" :title="title" placement="right" width="500" trigger="hover">
      <div class="div-question-option">
        <el-fill-box :size="15" text="您的家庭住址："/>
      </div>
      <div slot="reference">
        <slot/>
      </div>
    </el-popover>
    <el-popover v-else-if="type === 'multipleCompletion'" :title="title" placement="right" width="500" trigger="hover">
      <div class="div-question-option">
        <el-fill-box :size="10" text="您的家庭住址："/><el-fill-box :size="5" text="，您的工作年限："/>年。
      </div>
      <div slot="reference">
        <slot/>
      </div>
    </el-popover>
    <el-popover v-else-if="type === 'content'" :title="title" placement="right" width="500" trigger="hover">
      <div class="div-question-option" style="height: 60px">
        <el-input v-model="contentTemplate" :rows="2" type="textarea"/>
      </div>
      <div slot="reference">
        <slot/>
      </div>
    </el-popover>
    <div v-else>
      <slot/>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElQuestionToolItem',
  props: {
    title: {
      type: String,
      required: true
    },
    type: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      radioTemplate: '',
      checkBoxTemplate: [],
      contentTemplate: ''
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css" scoped>
  .div-tool-btn {
    margin: 5px 10px;
    display: inline-block;
  }
  .div-question-option {
    margin: 5px;
    height: 20px;
  }
</style>
