<template>
  <div style="display: inline-block">
    <span style="vertical-align:middle;padding-left:2px;" v-text="text"/>
    <div :style="{'width': (size * 12) + 'px'}" class="div-fillbox-border">
      <el-input v-model="input" :clearable="true" size="mini" class="fill-box-input"/>
      <span class="fill-box-span">{{ input }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElFillBox',
  props: {
    text: {
      type: String,
      default: () => ''
    },
    size: {
      type: Number,
      default: () => 15
    }
  },
  data() {
    return {
      input: ''
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.div-fillbox-border {
  position: inherit;
  display: inline-block;
  width: 120px;
  border-bottom: 1px #606266 solid;
  height: 28px;
  line-height: 28px;
}

.div-fillbox-border {
  &:hover {
    border-bottom: 0px;
    .fill-box-input {
      display: inherit;
    }
    .fill-box-span {
      display: none;
    }
  }
}
.fill-box-input {
  width: 100%;
  display: none;
}

.fill-box-span {
  width: 100%;
  display: inherit;
}
</style>
