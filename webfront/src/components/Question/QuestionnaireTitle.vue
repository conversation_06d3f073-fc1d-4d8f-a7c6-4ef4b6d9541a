<template>
  <div
    id="quesnaireTitle"
    :style="{'background': mouseover ? '#FAFAFA' : '', 'min-height': view ? '90px' : '', 'border': view ? 'none' : ''}"
    class="div-questionnaire-title"
    @mouseenter="mouseover = editable"
    @mouseleave="mouseover = false">
    <h2 style="text-align: center">{{ title }}</h2>
    <div class="div-questionnaire-desc" v-html="describe"/>
    <div v-if="editable" class="div-operate">
      <div v-show="mouseover" class="div_ins_question spanLeft">
        <a slot="reference" href="javascript:" class="link-normal" style="text-decoration:underline;">在此后插入新题</a>
      </div>
      <div v-show="mouseover" class="spanRight">
        <ul class="stuff">
          <li>
            <el-button size="mini" icon="el-icon-edit" round @click="openEditDialog()">编辑问卷标题描述</el-button>
          </li>
        </ul>
      </div>
    </div>
    <el-questionnaire-title-dialog v-if="editable" ref="dialog" dialog-title="问卷标题编辑"/>
  </div>
</template>
<script>
export default {
  name: 'ElQuestionnaireTitle',
  props: {
    title: {
      type: String,
      required: true
    },
    describe: {
      type: String,
      default: () => ''
    },
    editable: {
      type: Boolean,
      default: () => true
    },
    view: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      mouseover: false
    }
  },
  methods: {
    openEditDialog() {
      this.$refs.dialog.open({ title: this.title, describe: this.describe }, (param, close) => {
        this.$emit('update:title', param.title)
        this.$emit('update:describe', param.describe)
        close()
      })
    },
    handleInsert(type) {
      this.$emit('handleInsert', -1, type)
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css" scoped>
  .div-questionnaire-title {
    width: 850px;
    min-height: 120px;
    outline: none;
    border: none;
    display: inline-block;
    border-bottom: 1px solid #E0E0E0;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    cursor: pointer;
    padding: 0;
  }
  .div-questionnaire-desc {
    color: #a0afb9;
    line-height: 20px;
    text-align: left;
    text-indent: 2em;
    font-size: 14px;
    padding: 0 30px;
    margin-left: 0;
    margin-top: 20px;
  }
  .div_ins_question {
    padding-top: 8px;
    color: #666666;
    vertical-align: center;
    padding-left: 24px;
    line-height: 24px;
    font-size: 12px;
    clear: both;
    word-break: break-all;
  }
  .div-operate {
    padding: 0 60px;
    height: 40px;
    width: 850px;
    display: inline-block;
  }
  .link-normal {
    text-decoration: underline;
    color: #1ea0fa !important;
  }
  .spanLeft {
    float: left;
  }
  .spanRight {
    float: right;
    right: 50px;
    width: 200px;
  }
  .stuff {
    font-size: 12px;
    float: left;
    height: 23px;
    line-height: 23px;
    margin: 6px 0 11px;
  }
  .stuff ul {
    display: block;
    list-style-type: disc;
  }
  .stuff li {
    float: left;
    vertical-align: middle;
    text-align: center;
    margin: 0px 3px;
    border-radius: 2px;
    background: #fff;
    list-style: none;
  }
</style>
