<template>
  <el-drag-dialog ref="dialog" :title="dialogTitle" :loading="loading" :button-loading="loading" :ok-button-text="okButtonText" @handleResetFields="handleResetFields">
    <div v-loading="loading" style="text-align: center">
      <slot name="header"/>
      <el-form ref="form" :model="form" :rules="validRules" style="margin: auto" label-width="100px">
        <el-form-item label="问卷标题" prop="title">
          <el-input v-model="form.title" style="width: 100%" placeholder="请输入问卷标题"/>
        </el-form-item>
        <el-form-item label="问卷描述" prop="describe">
          <el-input v-model="form.describe" :rows="4" type="textarea" placeholder="请输入问卷描述" style="width: 100%"/>
        </el-form-item>
      </el-form>
    </div>
  </el-drag-dialog>
</template>

<script>
export default {
  name: 'ElQuestionnaireTitleDialog',
  props: {
    dialogTitle: {
      type: String,
      required: true
    },
    okButtonText: {
      type: String,
      default: () => '创建'
    }
  },
  data() {
    return {
      form: {
        title: '',
        describe: ''
      },
      validRules: {
        title: [
          { required: true, message: '问卷标题必须输入', trigger: ['blur'] }
        ]
      },
      loading: false,
      callback: null
    }
  },
  methods: {
    open(param, callback) {
      this.callback = callback || function(form, close) { close() }
      if (param) {
        this.form.title = param.title || ''
        this.form.describe = param.describe || ''
      } else {
        this.form.title = ''
        this.form.describe = ''
      }
      const self = this
      this.$refs.dialog.open((close) => {
        self.$refs['form'].validate((valid) => {
          if (valid) {
            self.loading = true
            self.callback(self.form, () => {
              close()
              self.loading = false
            }, () => {
              self.loading = false
            })
          }
        })
      })
    },
    handleResetFields() {
      this.$refs['form'].resetFields()
      // TODO value reset
    }
  }
}
</script>
