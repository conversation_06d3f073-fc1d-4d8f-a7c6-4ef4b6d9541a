<template>
  <el-select v-model="questionType" :disabled="disabled" :value="value" placeholder="问题类型">
    <el-option-group
      v-for="group in typeOptions"
      :key="group.label"
      :label="group.label">
      <el-option
        v-for="item in group.options"
        :key="item.value"
        :label="item.label"
        :value="item.value"/>
    </el-option-group>
  </el-select>
</template>

<script>
import { QuestionTypes } from './QuestionTypes.js'
export default {
  name: 'ElQuestionTypeSelect',
  props: {
    value: {
      type: String,
      default: () => 'radio'
    }
  },
  data() {
    return {
      typeOptions: QuestionTypes.filter(e => e.label !== '说明'),
      questionType: 'radio'
    }
  },
  computed: {
    disabled() {
      return false // this.value === 'cascader'
    }
  },
  watch: {
    value(val) {
      this.questionType = val
    },
    questionType(nv) {
      this.$emit('input', nv)
    }
  },
  mounted() {
    this.questionType = this.value
  }
}
</script>
