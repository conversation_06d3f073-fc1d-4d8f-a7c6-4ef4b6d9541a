<template>
  <div
    :style="{'background': mouseover ? isAppend ? '#E9E9E9' : '#FAFAFA' : '','cursor': editable ? 'move' : 'pointer', 'border': view ? 'none' : ''}"
    :class="isAppend ? 'div-question-append' : 'div-question'"
    @mouseenter="mouseover = editable"
    @mouseleave="mouseover = false">
    <div :class="isAppend ? 'div-preview-append' : 'div-preview'">
      <div v-if="isDescription === true" class="div-question-content">
        <div class="div-desc-title">
          <span>{{ questionTitle }}</span><el-tag v-if="questionTitle && questionTitle !== ''" type="info" effect="plain" size="mini" style="margin-left: 15px">段落说明</el-tag>
        </div>
        <div class="div-desc">
          <span>{{ questionDetails }}</span><el-tag v-if="!questionTitle || questionTitle === ''" type="info" effect="plain" size="mini" style="margin-left: 15px; text-indent: 0">段落说明</el-tag>
        </div>
      </div>
      <div v-else class="div-question-content">
        <el-question-title
          :required="mustInput"
          :index="index"
          :show-number="showNumber"
          :is-append="isAppend"
          :title-mark="titleMark"
          :question-relative-mark="questionRelativeMark"
          :append-index="appendIndex"
          :type="type"
          :user-display="userDisplay"
          :image-list="imageList"
          :append-mark="appendMark">
          {{ questionTitle }}
        </el-question-title>
        <el-question-par :type="type" :options="options" :question-list="questionList"/>
      </div>
      <slot name="append"/>
      <div v-if="showToolbar && editable" class="div-operate">
        <div v-show="mouseover" v-if="!isAppend" class="div_ins_question spanLeft">
          <el-question-add-popover v-if="popover === true" ref="popover" @handleChoiceConfirm="insertConfirm">
            <a slot="reference" href="javascript:;" class="link-normal">在此题后插入新题</a>
          </el-question-add-popover>
          <a v-else slot="reference" href="javascript:;" class="link-normal" @click="insertConfirm">在此题后插入新题</a>
        </div>
        <div v-show="mouseover" class="spanRight">
          <ul class="stuff">
            <li>
              <el-button size="mini" icon="el-icon-edit" @click="handleEdit">编辑</el-button>
            </li>
            <li v-if="!isAppend && isDescription !== true && copyBtnVisible">
              <el-button size="mini" icon="el-icon-document-copy" @click="handleCopy">复制</el-button>
            </li>
            <!-- <li v-if="appendBtnVisible && !isAppend && !isDescription && (type === 'radio' || type === 'checkbox')">
              <el-question-add-popover ref="popover" :show-description="isAppend" :disabled="appendBtnDisable" @handleChoiceConfirm="addConfirm">
                <el-button slot="reference" :disabled="appendBtnDisable" size="mini" icon="el-icon-zoom-in">追问</el-button>
              </el-question-add-popover>
            </li>-->
            <li>
              <el-button size="mini" icon="el-icon-delete" @click="handleDelete">删除</el-button>
            </li>
            <li v-if="!isAppend">
              <el-button :disabled="index === 1" size="mini" icon="el-icon-arrow-up" @click="handleUp">上移</el-button>
            </li>
            <li v-if="!isAppend">
              <el-button :disabled="index === size" size="mini" icon="el-icon-arrow-down" @click="handleDown">下移</el-button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ElQuestionPanel',
  props: {
    questionList: {
      type: Array,
      required: true
    },
    type: {
      type: String,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    mustInput: {
      type: Boolean,
      default: true
    },
    showNumber: {
      type: String,
      required: true
    },
    questionTitle: {
      type: String,
      default: () => ''
    },
    questionDetails: {
      type: String,
      default: () => ''
    },
    imageList: {
      type: Array,
      default: () => []
    },
    appendIndex: {
      type: Number,
      default: () => 0
    },
    isAppend: {
      type: Boolean,
      default: false
    },
    appendMark: {
      type: String,
      default: () => ''
    },
    titleMark: {
      type: String,
      default: () => ''
    },
    questionRelativeMark: {
      type: String,
      default: () => ''
    },
    isDescription: {
      type: Boolean,
      default: false
    },
    userDisplay: {
      type: String,
      default: () => '0'
    },
    showToolbar: {
      type: Boolean,
      default: true
    },
    size: {
      type: Number,
      default: () => 0
    },
    options: {
      type: Array,
      default: () => []
    },
    appendBtnVisible: {
      type: Boolean,
      default: true
    },
    copyBtnVisible: {
      type: Boolean,
      default: true
    },
    editable: {
      type: Boolean,
      default: () => true
    },
    view: {
      type: Boolean,
      default: () => false
    },
    popover: {
      type: Boolean,
      default: () => true
    },
    /*,
    appendQeus: {
      type: Array,
      default: () => []
    } */
  },
  data() {
    return {
      mouseover: false
    }
  },
  methods: {
    handleInsert() {
      this.$emit('handleInsert', this.index - 1)
    },
    handleEdit() {
      this.$emit('handleEdit', this.isAppend, this.index - 1, this.appendIndex - 1)
    },
    handleCopy() {
      this.$emit('handleCopy', this.index - 1)
    },
    // handleAddQuestion() {
    //   this.$emit('handleAddQuestion', this.index - 1)
    // },
    handleDelete() {
      this.$confirm('确认是否删除该题目?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('handleDelete', this.isAppend, this.index - 1, this.appendIndex - 1)
      }).catch(() => { })
    },
    handleUp() {
      this.$emit('handleUp', this.index - 1)
    },
    handleDown() {
      this.$emit('handleDown', this.index - 1)
    },
    insertConfirm(type) {
      this.$emit('handleInsert', this.index - 1, type)
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css" scoped>
  .div-question {
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    width: 850px;
    margin: 0;
    outline: none;
    padding: 20px 0 0;
    border: none;
    display: inline-block;
    border-bottom: 1px solid #E0E0E0;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
  }
  .div-question-append {
    user-select: none;
    width: 760px;
    margin: 0;
    outline: none;
    padding: 5px 0;
    border: none;
    display: inline-block;
    border-top: #E0E0E0 dashed 1px;
    box-sizing: border-box;
  }
  .div-preview {
    margin-bottom: 10px;
    padding: 0 60px;
  }
  .div-preview-append {
    margin-bottom: 10px;
    padding: 0 30px;
  }
  .div-question-content {
    display: block
  }
  .div_ins_question {
    padding-top: 8px;
    color: #666666;
    padding-left: 24px;
    line-height: 18px;
    font-size: 12px;
    clear: both;
    word-break: break-all;
  }
  .div-operate {
    height: 35px;
    width: 100%;
    display: inline-block;
  }
  .link-normal {
    text-decoration: underline;
    color: #1ea0fa !important;
  }
  .spanLeft {
    float: left;
    width: 20%;
  }
  .spanRight {
    float: right;
  }
  .stuff {
    font-size: 12px;
    float: left;
    height: 23px;
    line-height: 23px;
    margin: 6px 0 11px;
  }
  .stuff ul {
    display: block;
    list-style-type: disc;
  }
  .stuff li {
    float: left;
    vertical-align: middle;
    text-align: center;
    margin: 0px 3px;
    border-radius: 2px;
    background: #fff;
    list-style: none;
  }
  .div-desc-title {
    color: #a0aec8;
    line-height: 30px;
    text-align: center;
    font-size: 16px;
    padding: 0 20px;
    margin-left: 0;
    margin-top: 20px;
    word-break: break-all;
  }
  .div-desc {
    color: #a0aec8;
    line-height: 17px;
    text-align: left;
    text-indent: 2em;
    font-size: 14px;
    padding: 0 20px;
    margin-left: 0;
    margin-top: 20px;
    word-break: break-all;
  }
  .div-desc_type {
    overflow: hidden;
    font-weight: bold;
    zoom: 1;
    font-size: 10px;
    color: #a0afb9;
    display: inline-block;
  }
</style>
