<template>
  <el-popover ref="confirmPopover" v-model="confirmVisible" :disabled="disabled" :width="showDescription ? 282 : 200" placement="bottom-start" trigger="click">
    <div style="margin: 10px 0">
      <slot name="condition"/>
    </div>
    <div style="margin-bottom: 5px">选择题目类型：</div>
    <el-button-group>
      <el-button size="mini" @click="handleChoiceConfirm('radio')">单选题</el-button>
      <el-button size="mini" @click="handleChoiceConfirm('checkbox')">多选题</el-button>
    </el-button-group>
    <!-- <el-button-group>
      <el-button size="mini" @click="handleChoiceConfirm('singleCompletion')">单项填空</el-button>
      <el-button size="mini" @click="handleChoiceConfirm('multipleCompletion')">多项填空</el-button>
    </el-button-group>
    <el-button-group>
      <el-button size="mini" @click="handleChoiceConfirm('content')">简答题</el-button>
    </el-button-group>
    <el-button-group v-if="showDescription">
      <el-button size="mini" @click="handleChoiceConfirm('description')">段落说明</el-button>
    </el-button-group>-->
    <div slot="reference" style="display: block">
      <slot name="reference"/>
    </div>
  </el-popover>
</template>
<script>
export default {
  name: 'ElQuestionAddPopover',
  props: {
    disabled: {
      type: Boolean,
      default: () => false
    },
    showDescription: {
      type: Boolean,
      default: () => true
    }
  },
  data() {
    return {
      confirmVisible: false
    }
  },
  methods: {
    handleChoiceConfirm(type) {
      this.confirmVisible = false
      this.$emit('handleChoiceConfirm', type)
    },
    doClose() {
      this.$refs.confirmPopover.doClose()
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css" scoped>
</style>
