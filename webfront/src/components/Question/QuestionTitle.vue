<template>
  <div class="div_title_question_all">
    <!--<div class="div_topic_question"><span v-if="required" class="req">*</span>{{ showNumber }}&lt;!&ndash;<span v-if="isAppend" v-text="'.' + appendIndex"/>&ndash;&gt;.</div>
    <div class="div_title_question" >
      <slot/>
    </div>
    <div class="div_title_type">
      <span>【{{ getTypeName(type) }}】</span>
      <span v-if="isAppend && appendMark !== ''" style="color: #EFA030;" v-text="'(【' + appendMark + '】选择时该题显示 )'"/>
      <span v-if="titleMark !== ''" style="color: #EFA030;" v-text="'(【' + titleMark + '】)'"/>
      <span v-if="userDisplay === '1'" style="color: #EFA030;" v-text="'(【问卷提交后该题结果将以＜饼状图＞反馈】)'"/>
      <span v-if="userDisplay === '2'" style="color: #EFA030;" v-text="'(【问卷提交后该题结果将以＜柱状图＞反馈】)'"/>
    </div>-->
    <div class="div-title-style">
      <span v-if="required" class="req">*</span><span>{{ showNumber }}</span><span v-show="showNumber && showNumber !== ''">.</span>
      <span>
        <slot/>
      </span>
      <!--<span class="div_title_type">【{{ getTypeName(type) }}】</span>-->
      <el-tag type="info" effect="plain" size="mini">{{ getTypeName(type) }}</el-tag>
      <!--      <span v-if="isAppend && appendMark !== ''" class="div_title_type" style="color: #EFA030;" v-text="'(【' + appendMark + '】选择时该题显示 )'"/>-->
      <!--      <span v-if="titleMark !== ''" class="div_title_type" style="color: #EFA030;" v-text="'(【' + titleMark + '】)'"/>-->
      <!--      <span v-if="questionRelativeMark !== ''" class="div_title_type" style="color: #EFA030;" v-text="'(【' + questionRelativeMark + '】)'"/>-->
      <!--      <span v-if="userDisplay === '1'" class="div_title_type" style="color: #EFA030;" v-text="'(【问卷提交后该题结果将以＜饼状图＞反馈】)'"/>-->
      <!--      <span v-if="userDisplay === '2'" class="div_title_type" style="color: #EFA030;" v-text="'(【问卷提交后该题结果将以＜柱状图＞反馈】)'"/>-->
    </div>
    <div style="padding-left: 50px">
      <el-photo-multi-upload v-if="imageList && imageList.length > 0" :editable="false" :img-data-list="imageList"/>
    </div>
  </div>
</template>

<script>
import { QuestionTypes } from '@/components'
export default {
  name: 'ElQuestionTitle',
  props: {
    type: {
      type: String,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    showNumber: {
      type: String,
      required: true
    },
    isAppend: {
      type: Boolean,
      default: () => false
    },
    appendIndex: {
      type: Number,
      default: () => 0
    },
    appendMark: {
      type: String,
      default: () => ''
    },
    userDisplay: {
      type: String,
      default: () => '0'
    },
    titleMark: {
      type: String,
      default: () => ''
    },
    questionRelativeMark: {
      type: String,
      default: () => ''
    },
    required: {
      type: Boolean,
      default: () => false
    },
    imageList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
    }
  },
  methods: {
    getTypeName(type) {
      for (const qt of QuestionTypes) {
        const option = qt.options.filter(o => o.value === type)
        if (option && option.length > 0) {
          return option[0].label
        }
      }
      return type
    }
  }
}
</script>

<style rel="stylesheet/css" lang="css" scoped>
  .req {
    color: red;
    font-weight: bold;
    margin-right: 5px;
    top: 3px;
  }
  .div_title_question_all {
    padding-top: 2px;
    font-size: 15px;
    /*color: #444444;*/
    font-weight: bold;
    height: auto;
    line-height: 20px;
    position: relative;
  }
  .div_topic_question {
    width: 50px;
    float: left;
    font-weight: bold;
    text-align: right;
    padding-right: 10px;
  }
  .div_title_question {
    overflow: hidden;
    zoom: 1;
    margin-left: 3px;
    display: inline-block;
  }
  .div_title_type {
    overflow: hidden;
    zoom: 1;
    margin-left: 3px;
    font-size: 10px;
    color: #a0afb9;
    line-height: 12px;
    display: inline-block;
  }
  .div-title-style {
    font-size: 15px;
    font-weight: bold;
  }
</style>
