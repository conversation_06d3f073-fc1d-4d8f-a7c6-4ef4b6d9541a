<template>
  <div class="div_title_question_all">
    <div v-if="type === 'radio' || type === 'checkbox'">
      <div v-for="(option, index) in options" :key="index" class="div-ques-option">
        <el-radio v-if="type === 'radio'" v-model="radioValue" :label="index">
          <span v-text="String.fromCharCode(65 + index) + '.'"/>
          <el-fill-box v-if="option.fillbox === true" :text="option.text"/>
          <span v-else v-text="option.text"/>
        </el-radio>
        <el-checkbox v-if="type === 'checkbox'" v-model="checkboxValue" :label="index">
          <span v-text="String.fromCharCode(65 + index) + '.'"/>
          <el-fill-box v-if="option.fillbox === true" :text="option.text"/>
          <span v-else v-text="option.text"/>
        </el-checkbox>
        <span v-if="type === 'radio'" class="jump-text" v-text="jumpText(option.jump, option)"/>
        <span v-if="type === 'radio' || type === 'checkbox'" class="jump-text" v-text="showRelationText(option.relationOptions)"/>
        <span v-if="option.quota === true" class="jump-text" v-text="quotaText(option.quotaCount)"/>
      </div>
    </div>
    <div v-if="type === 'singleCompletion'" class="div-ques-option">
      <el-input v-model="inputValue" style="margin-top: 10px"/>
    </div>
    <div v-if="type === 'multipleCompletion'" class="div-ques-option">
      <div v-for="(option, index) in options" :key="index" class="div-ques-option-block">
        <span v-if="option.fillbox !== true" v-text="option.text"/>
        <el-fill-box v-if="option.fillbox === true" :size="Number(option.fillboxSize) || 15"/>
      </div>
    </div>
    <div v-if="type === 'scaleRating'" class="div-ques-option">
      <div class="scale-rating-text">
        <span>{{ startOptionText }}</span>
        <span>{{ endOptionText }}</span>
      </div>
      <el-radio-group v-model="scale" class="scale-rating-option">
        <el-radio v-for="(o, i) in options" :key="i" :label="o.scale">{{ o.scale }}</el-radio>
      </el-radio-group>
    </div>
    <div v-if="type === 'content'" class="div-ques-option">
      <el-input v-model="inputValue" :rows="2" type="textarea"/>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElQuestionPar',
  props: {
    type: {
      type: String,
      required: true
    },
    questionList: {
      type: Array,
      required: true
    },
    options: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      radioValue: '',
      checkboxValue: [],
      inputValue: '',
      textareaValue: '',
      cascaderValue: [],
      scale: 0,
      npsScale: 0
    }
  },
  computed: {
    startOptionText() {
      return this.options && this.options.length > 0 ? this.options[0].text : ''
    },
    endOptionText() {
      return this.options && this.options.length > 0 ? this.options[this.options.length - 1].text : ''
    }
  },
  methods: {
    jumpText(jump, option) {
      if (jump) {
        if (jump !== '-99') {
          const questionTitle = this.getQuestionTitle(jump)
          if (questionTitle === null) {
            option.jump = ''
            return ''
          }
          return '( 选择该选项时，跳转到【' + questionTitle + '】题 )'
        } else {
          return '( 选择该选项时，结束作答 )'
        }
      }
    },
    getQuestionTitle(uuid) {
      const question = this.questionList.filter(q => q.uuid === uuid)[0]
      if (!question) {
        return null
      }
      return question.number + '.' + question.title
    },
    showRelationText(relationOptions) {
      if (relationOptions) {
        const relation = relationOptions.split(':')
        const question = this.questionList.filter((q) => q.uuid === relation[0])[0]
        if (question) {
          const targetQuestionTitle = question.number + '.' + question.title
          if (question.type === 'radio' || question.type === 'checkbox') {
            const targetOptionsText = []
            question.options.forEach((e1, index) => {
              if (relation[1].indexOf(String(index)) !== -1) {
                targetOptionsText.push(String.fromCharCode(65 + index) + '. ' + e1.text)
              }
            })
            return `( 选择该选项时，第【${targetQuestionTitle}】题不能够选择【${targetOptionsText.join('、')}】选项 )`
          } else {
            return `( 选择该选项时，第【${targetQuestionTitle}】题不能作答 )`
          }
        }
      }
      return ''
    },
    hasOption(questionId) {
      const ques = this.questionList.filter(e => e.uuid === questionId)[0]
      return ques && (ques.type === 'radio' || ques.type === 'checkbox')
    },
    quotaText(quotaCount) {
      if (!quotaCount && quotaCount !== 0) {
        return '( 配额: 不限 )'
      } else {
        return '( 配额: ' + quotaCount + ' )'
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.scale-rating-option {
  .el-radio__inner {
    width: 20px !important;
    height: 20px !important;
  }
}
</style>

<style rel="stylesheet/css" lang="css" scoped>
.div_title_question_all {
  min-height: 50px;
}
.div-ques-option {
  margin: 2px 30px;
  line-height: 28px;
}
.div-ques-option-block {
  display: inline-block;
}
.jump-text {
  /*margin-left: -10px;*/
  color: #EFA030;
  font-size: 10px;
  font-weight: bold;
}
.scale-rating-text {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  padding: 8px 10px;
  border-bottom: 1px solid #e3e3e3;
}
.scale-rating-option {
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-around;
}
.nps-scale-rating {

}
</style>
