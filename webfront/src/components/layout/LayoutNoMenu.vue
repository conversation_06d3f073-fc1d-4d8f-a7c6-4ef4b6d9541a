<template>
  <div class="pass_layout">
    <div class="title">
      <div class="title-div">
        <b style="text-align: left">- 狴犴调研问卷 -</b>
      </div>
      <div class="title-button">
        <el-button type="text" @click="gotoLogin">登录页面</el-button>
      </div>
    </div>
    <app-main/>
    <app-footer/>
  </div>
</template>

<script>
import { AppMain, AppFooter } from './components'

export default {
  name: 'LayoutNoMenu',
  components: {
    AppMain,
    AppFooter
  },
  methods: {
    gotoLogin: function() {
      this.$router.push('/login')
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .pass_layout {
    background: #FDFDFD;

    .title {
      height: 50px;
      line-height: 50px;
      box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
      background: #FDFDFD;
      position: fixed;
      top: 0;
      right: 0;
      z-index: 9;
      width: 100%;
    }

    .title-div {
      margin: 0 auto;
      border: 0px solid #000;
      width: 60%;
      min-width: 1024px;
      height: 50px
    }

    .title-button {
      display: block;
      float: right;
      height: 50px;
      margin: 0px 10px 0 2px;
      margin-top:-50px;
      width:23%;
      font-size: 14px;
      color: #2c82ff;
    }
  }
</style>
