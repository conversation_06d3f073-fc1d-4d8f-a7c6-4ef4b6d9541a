<template>
  <div class="has-logo">
    <logo :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :show-timeout="200"
        :default-active="activeMenu($route, $route.path)"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        mode="vertical"
        @select="handleSelect"
      >
        <sidebar-item v-for="route in routes" v-if="route.show !== false" :key="route.path" :item="route" :base-path="route.path"/>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import variables from '@/assets/styles/variables.scss'
import SidebarItem from './SidebarItem'
import Logo from './Logo'

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapGetters([
      'sidebar'
    ]),
    routes() {
      return this.$router.options.routes
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  methods: {
    activeMenu(router, path) {
      if (router.meta && router.meta.activeMenu) {
        return router.meta.activeMenu.path
      }
      return path
    },
    handleSelect(){
        //刷新keep-alive页面
        this.$bus.$emit('refresh-page', true)
    }
  }
}
</script>
