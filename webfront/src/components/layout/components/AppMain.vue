<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <!-- or name="fade" -->
      <!-- <router-view :key="key"></router-view> -->
      <div>
        <keep-alive v-if="!refresh">
          <router-view v-if="this.$route.meta.keepAlive" />
        </keep-alive>
        <router-view v-if="!this.$route.meta.keepAlive" />
      </div>
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    // key() {
    //   return this.$route.name !== undefined ? this.$route.name + +new Date() : this.$route + +new Date()
    // }
  },
  mounted() {
    this.$bus.$on('refresh-page',data=>{
      this.refresh = true;
      this.$nextTick(()=>{
        this.refresh = false;
      })
		})
  },
  data(){
    return {
      refresh:false
    }
  }
}
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  padding: 70px 10px 0 10px;
  min-height: calc(100vh - 50px);
  position: relative;
  overflow: hidden;
}
</style>
