<template>
  <div :style="{'width': (device === 'mobile' ? '100%': '')}" class="navbar">
    <hamburger :toggle-click="toggleSideBar" :is-active="sidebar.opened" class="hamburger-container"/>
    <breadcrumb :pt-show="ptShow"/>
    <el-dropdown class="avatar-container" trigger="click">
      <div class="avatar-wrapper">
        <span><svg-icon icon-class="user" style="margin-right: 10px"/><b class="navbar-user-info">{{ userId }} [{{ userName }}]</b></span>
        <i class="el-icon-caret-bottom" />
      </div>
      <el-dropdown-menu slot="dropdown" class="user-dropdown">
        <router-link class="inlineBlock" to="/">
          <el-dropdown-item>
            首页
          </el-dropdown-item>
        </router-link>
        <a class="inlineBlock" @click="about">
          <el-dropdown-item>
            发布版本
          </el-dropdown-item>
        </a>
        <a class="inlineBlock" @click="logout">
          <el-dropdown-item divided>
            退出
          </el-dropdown-item>
        </a>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'

export default {
  components: {
    Breadcrumb,
    Hamburger
  },
  data() {
    return {
      userId: this.$store.getters.userId,
      userName: this.$store.getters.userName,
      ptShow: '',
      user: {
        accountId: this.$store.getters.userId,
        accountName: this.$store.getters.userName
      }
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'device'
    ])
  },
  watch: {
  },
  created() {
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('ToggleSideBar')
    },
    logout() {
      this.$store.dispatch('LogOut').then(() => {
        location.reload() // Vue-routerオブジェクトを再実装するためにバグを避ける
      })
    },
    about() {
      this.$store.dispatch('About', this)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.hideSidebar {
  .navbar {
    width: calc(100% - 35px);
  }
}
.navbar {
  height: 50px;
  line-height: 50px;
  background: #FDFDFD;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - 220px);
  .hamburger-container {
    line-height: 50px;
    height: 50px;
    float: left;
    margin-top: 5px;
    padding: 0 10px;
    cursor: pointer;
    // transition: background .3s;
    // -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }
  .screenfull {
    position: absolute;
    right: 90px;
    top: 16px;
    color: red;
  }
  .icon-profile::before {
    content: "\e97e";
  }
  .avatar-container {
    height: 50px;
    display: inline-block;
    position: absolute;
    right: 35px;
    .avatar-wrapper {
      cursor: pointer;
      /*margin-top: 13px;*/
      line-height: 50px;
      position: relative;
      /*.user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 10px;
      }*/
      /*.el-icon-caret-bottom {
        position: absolute;
        right: -20px;
        top: 25px;
        font-size: 12px;
      }*/
    }
  }
}
</style>
