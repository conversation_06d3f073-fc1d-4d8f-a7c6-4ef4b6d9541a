import Vue from 'vue'

import { QuestionTypes } from './Question/QuestionTypes.js'

import ElQuestionPanel from './Question/QuestionPanel'
import ElQuestionTitle from './Question/QuestionTitle'
import ElQuestionPar from './Question/QuestionPar'
import ElQuestionAdd from './Question/QuestionAdd'
import ElQuestionnaireTitle from './Question/QuestionnaireTitle'
import ElQuestionTypeSelect from './Question/QuestionTypeSelect'
import ElQuestionToolItem from './Question/QuestionToolItem'
import ElQuestionnaireTitleDialog from './Question/QuestionnaireTitleDialog'
import ElQuestionAddPopover from './Question/QuestionAddPopover'
import ElFillBox from './Question/Fillbox'

Vue.component(ElQuestionTitle.name, ElQuestionTitle)
Vue.component(ElQuestionPar.name, ElQuestionPar)
Vue.component(ElQuestionPanel.name, ElQuestionPanel)
Vue.component(ElQuestionAdd.name, ElQuestionAdd)
Vue.component(ElQuestionnaireTitle.name, ElQuestionnaireTitle)
Vue.component(ElQuestionTypeSelect.name, ElQuestionTypeSelect)
Vue.component(ElQuestionToolItem.name, ElQuestionToolItem)
Vue.component(ElQuestionnaireTitleDialog.name, ElQuestionnaireTitleDialog)
Vue.component(ElQuestionAddPopover.name, ElQuestionAddPopover)
Vue.component(ElFillBox.name, ElFillBox)

Vue.prototype.$QuestionTypes = QuestionTypes

export {
  QuestionTypes
}
