import request from '@/common/request'
import Qs from "qs";
import utils from "@/utils";

//广播----
export function broadcast_list_api(param){
    return request({
        url:"/broadcast/list",
        method:'get',
        params:param
    })
}

export function broadcast_add_api(param){
    return request({
        url:"/broadcast/create",
        method:'post',
        data: Qs.stringify(param)
    })
}

export function broadcast_edit_api(param){
    return request({
        url:"/broadcast/edit",
        method:'post',
        data: Qs.stringify(param)
    })
}

export function broadcast_delete_api(id){
    let params = {
        id
    }
    return request({
        url:"/broadcast/delete",
        method:'get',
        params: params
    })
}

export function broadcast_init_api(id){
    let params = {
        id
    }
    return request({
        url:"/broadcast/init",
        method:'get',
        params: params
    })
}

//证书----
export function certificate_list_api(param){
    return request({
        url:"/certificate/list",
        method:'get',
        params:param
    })
}

export function certificate_init_api(id){
    let params = {
        id
    }
    return request({
        url:"/certificate/init",
        method:'get',
        params: params
    })
}

export function certificate_add_api(param){
    console.log(param)
    return request({
        url:"/certificate/create",
        method:'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: utils.formData(param)
    })
}

export function certificate_edit_api(param){
    return request({
      url:"/certificate/edit",
      method:'post',
      headers: { 'Content-Type': 'multipart/form-data' },
      data: utils.formData(param)
    })
}

export function certificate_delete_api(id){
    let params = {
        id
    }
    return request({
        url:"/certificate/delete",
        method:'get',
        params: params
    })
}

//资讯----
export function information_list_api(param){
    return request({
        url:"/info/list",
        method:'get',
        params:param
    })
}

export function information_add_api(param){
    return request({
        url:"/info/create",
        method:'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: utils.formData(param)
    })
}

export function information_edit_api(param){
  return request({
    url:"/info/edit",
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: utils.formData(param)
  })
}

export function information_init_api(id){
    let params = {
        id
    }
    return request({
        url:"/info/init",
        method:'get',
        params: params
    })
}

export function information_delete_api(id){
    let params = {
        id
    }
    return request({
        url:"/info/delete",
        method:'get',
        params: params
    })
}

//资讯--详情--
export function information_detail_list_api(param){
    return request({
        url:"/info/list-details",
        method:'get',
        params:param
    })
}

export function information_detail_add_api(param){
    return request({
        url:"/info/create-details",
        method:'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: utils.formData(param)
    })
}

export function information_detail_edit_api(param){
    return request({
      url:"/info/edit-details",
      method:'post',
      headers: { 'Content-Type': 'multipart/form-data' },
      data: utils.formData(param)
    })
}

export function information_detail_init_api(id){
    let params = {
        id
    }
    return request({
        url:"/info/init-details",
        method:'get',
        params: params
    })
}

export function information_detail_delete_api(id){
    let params = {
        id
    }
    return request({
        url:"/info/delete-details",
        method:'get',
        params: params
    })
}
