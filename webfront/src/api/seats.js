import request from '@/common/request'
// import Qs from 'qs'
import utils from '@/utils'

export function list_search_api(form, pageNumber, sorts) {
  const param = {
    seatNo: form.seatNo,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/seats/list',
    method: 'get',
    params: param
  })
}

export function delete_api(id) {
  return request({
    url: '/seats/delete/' + id,
    method: 'delete'
  })
}

export function cancel_api(id) {
  return request({
    url: '/seats/cancel/' + id,
    method: 'get'
  })
}

export function seat_get_api(id) {
  return request({
    url: '/seats/get/' + id,
    method: 'get'
  })
}

export function seat_edit_api(optType, form, deploy) {
  const data = {
    id: form.id,
    seatNo: form.seatNo,
    seatName: form.seatName,
    seatSize: form.seatSize,
    material: form.material,
    introduce: form.introduce,
    price: form.price,
    image: form.image,
    imageList: form.imageList,
    deploy: deploy || false
  }

  return request({
    url: optType === 'add' ? '/seats/create' : '/seats/update',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: utils.formData(data)
  })
}
