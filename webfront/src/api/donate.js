import request from '@/common/request'
import Qs from 'qs'
import utils from "@/utils";

export function list_init_api(form, pageNumber, sorts) {
  const param = {
    donateNo: form.donateNo,
    seatNo: form.seatNo,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/donate/list',
    method: 'get',
    params: param
  })
}

export function donate_save_api(optType, form) {
  const param = {
    id: form.id,
    donateNo: form.donateNo,
    seatNo: form.seatNo,
    seatName: form.seatName,
    seatSize: form.seatSize,
    seatMaterial: form.seatMaterial,
    seatIntroduce: form.seatIntroduce,
    seatPrice: form.seatPrice,
    ownerUnit: form.ownerUnit,
    constructUnit: form.constructUnit,
    seatImage: form.seatImage,
    seatImageList: form.seatImageList,
    province: form.province,
    city: form.city,
    strict: form.strict,
    street: form.street,
    gpsLng: form.gpsLng,
    gpsLat: form.gpsLat,
    adress: form.adress,
    validTime: form.validTime,
    status: form.status,
  }

  return request({
    url: optType === 'add' ? '/donate/add' : '/donate/edit',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: utils.formData(param)
  })
}

export function donate_save_status_api(id, status) {
  const param = {
    id: id,
    status: status
  }

  return request({
    url: '/donate/updStatus',
    method: 'post',
    data: Qs.stringify(param)
  })
}

export function donate_init_api(optType, id) {
  return request({
    url: '/donate/init',
    method: 'get',
    params: { id, optType }
  })
}

export function seat_get_api(id) {
  return request({
    url: '/donate/seat/' + id,
    method: 'get'
  })
}

export function list_apply_init_api(init, form, pageNumber, sorts) {
  const param = {
    donateId: form.donateId,
    donateNo: form.donateNo,
    status: form.status,
    name: form.name,
    init: init,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/donate-apply/list',
    method: 'get',
    params: param
  })
}

export function donate_apply_save_api(optType, form) {

  const param = {
    id: form.id,
    donateNo: form.donateNo,
    payedPrice:form.payedPrice,
    predictPrice: form.predictPrice,
    payedPriceRemain: form.payedPriceRemain,
    shareNum: form.shareNum,
    realNum: form.realNum,
    name: form.name,
    idCardNo: form.idCardNo,
    sex: form.sex,
    age: form.age,
    tel: form.tel,
    province: form.province,
    city: form.city,
    strict: form.strict,
    type: form.type,
    personCertificateImage: form.personCertificateImage,
    aptitudeCertificateImage: form.aptitudeCertificateImage,
    channel: form.channel,
    reason: form.reason,
    donationWord: form.donationWord,
    donationWordFlag:form.donationWordFlag,
    applyTime: form.applyTime,
    applyPassTime: form.applyPassTime,
    validTime: form.validTime,
    status: form.status
  }

  return request({
    url: optType === 'add' ? '/donate-apply/add' : '/donate-apply/edit',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: utils.formData(param)
  })
}

export function donate_apply_init_api(optType, id) {
  return request({
    url: '/donate-apply/init',
    method: 'get',
    params: { id, optType }
  })
}

export function donate_apply_save_status_api(id, status, payedPrice, realNum) {
  const param = {
    id: id,
    status: status,
    payedPrice: payedPrice,
    realNum: realNum
  }

  return request({
    url: '/donate-apply/updStatus',
    method: 'post',
    data: Qs.stringify(param)
  })
}

export function donate_export_api(form) {
  const param = {
    donateNo: form.donateNo,
    seatNo: form.seatNo,
    status: form.status,
    name: form.name,
  }
  return request({
    url: '/donate/export',
    method: 'get',
    responseType: 'arraybuffer',
    params: param
  })
}

export function donate_apply_export_api(form) {
  const param = {
    donateNo: form.donateNo,
    status: form.status,
    name: form.name
  }
  return request({
    url: '/donate-apply/export',
    method: 'get',
    responseType: 'arraybuffer',
    params: param
  })
}

export function delete_api(id) {
  return request({
    url: '/donate/delete/' + id,
    method: 'delete'
  })
}

export function delete_apply_api(id) {
  return request({
    url: '/donate-apply/delete/' + id,
    method: 'delete'
  })
}

export function cancel_api(id) {
  return request({
    url: '/donate/cancel/' + id,
    method: 'get'
  })
}

export function download_qrcode_api(id) {
  return request({
    url: '/qrCode/download/' + id,
    responseType: 'arraybuffer',
    method: 'get'
  })
}

export function list_comment_init_api(init, form, pageNumber, sorts) {
  const param = {
    donateId: form.donateId,
    status: form.status,
    init: init,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/donate-comment/list',
    method: 'get',
    params: param
  })
}

export function delete_comment_api(id) {
  return request({
    url: '/donate-comment/delete/' + id,
    method: 'delete'
  })
}

export function donate_comment_save_status_api(id, status) {
  const param = {
    id: id,
    status: status
  }

  return request({
    url: '/donate-comment/updStatus',
    method: 'post',
    data: Qs.stringify(param)
  })
}

export function seat_get_donate_no_api(donateNo) {
  return request({
    url: '/donate-apply/getSeatInfoByDonateNo/' + donateNo,
    method: 'get'
  })
}

