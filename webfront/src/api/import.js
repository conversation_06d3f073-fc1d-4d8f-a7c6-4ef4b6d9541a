import request from '@/common/request'
import utils from "@/utils";

export function list_init_api(form, pageNumber, sorts) {
  const param = {
    telephone: form.telephone,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/import/list',
    method: 'get',
    params: param
  })
}

export function import_upkeep_user_api(wjId, form) {
  // const fd = new FormData()
  // fd.append('mode', form.mode)
  // Utils.fillFile(true, fd, form.file)
  //
  // return request({
  //   url: '/wenjuan-qrCode/create/' + wjId,
  //   method: 'post',
  //   headers: { 'Content-Type': 'multipart/form-data' },
  //   responseType: 'arraybuffer',
  //   data: fd
  // })
}


export function add_upkeep_user(param) {
  //添加养护人员
  return request({
    url: '/adopt-upkeep-user/add',
    method: 'post',
    headers: { 'Content-Type': 'application/json; charset=UTF-8' },
    data: param
  })
}

export function delete_upkeep_user(userId) {
  //删除养护人员
  return request({
    url: '/adopt-upkeep-user/delete/'+userId,
    method: 'delete',
  })
}
