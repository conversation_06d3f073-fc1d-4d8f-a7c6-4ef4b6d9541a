import request from '@/common/request'
import Qs from 'qs'
import utils from "@/utils";

export function list_init_api(init, form, pageNumber, sorts) {
  const param = {
    init: init,
    donateNo: form.donateNo,
    adoptNo: form.adoptNo,
    status: form.status,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/upkeep-assign/list',
    method: 'get',
    params: param
  })
}

export function upkeep_init_api(id, init) {
  return request({
    url: '/upkeep-assign/init',
    method: 'get',
    params: { id, init }
  })
}

export function upkeep_save_api(form) {
  const param = {
    id: form.id,
    upkeepDateBegin: form.upkeepDateBegin,
    upkeepDateEnd: form.upkeepDateEnd,
    upkeepUserId: form.upkeepUserId
  }

  return request({
    url: '/upkeep-assign/edit',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: utils.formData(param)
  })
}

export function list_seat_init_api(init, form, pageNumber, sorts) {
  const param = {
    init: init,
    upkeepId: form.upkeepId,
    upkeepName: form.upkeepName,
    upkeepCompany: form.upkeepCompany,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/upkeep-assign/list-seat',
    method: 'get',
    params: param
  })
}

export function delete_upkeep_api(id) {
  return request({
    url: '/upkeep-assign/delete-seat/' + id,
    method: 'delete'
  })
}

export function upkeep_seat_init_api(id) {
  return request({
    url: '/upkeep-assign/init-seat',
    method: 'get',
    params: { id }
  })
}
