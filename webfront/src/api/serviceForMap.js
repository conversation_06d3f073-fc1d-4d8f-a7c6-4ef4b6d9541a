import axios from "axios"

const mapService = axios.create({
    baseURL: "/qq/ws",
    timeout: 50 * 10000, // timeout
    withCredentials: true,
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true
    }
})

const KEY = "Z4TBZ-5V73U-CNQV5-BCPTB-THZFK-M7FK7"

// Request Interceptor
mapService.interceptors.request.use(
    config => {
        if (config.method = 'get') {
            config.params.key = KEY;
        }
        return config
    },
    error => {
        console.log(error) // for debug
        Promise.reject(error)
    }
)

mapService.interceptors.response.use(
    response => {
        let res = response.data
        if (response.status !== 200) {
            return Promise.reject(res)
        } else {
            return res
        }
    },
    error => {
        return Promise.reject(error)
    }
)


//行政区划
export function getDistrict(keyword) {
    return mapService({
        url: '/district/v1/search',
        method: 'get',
        params: {
            keyword: keyword
        }
    })
}

//行政区划列表
export function getDistrictList() {
    return mapService({
        url: '/district/v1/getchildren',
        method: 'get',
        params: {
            id: "310000"
        }
    })
}

//地址解析
export function geocoder(address) {
    return mapService({
        url: '/geocoder/v1/',
        method: 'get',
        params: {
            address
        }
    })
}