import request from '@/common/request'
import Qs from 'qs'

export function login_api(username, password) {
  const param = {
    username: username,
    password: password
  }
  return request({
    url: '/auth/login',
    method: 'post',
    data: Qs.stringify(param)
  })
}

export function healthCheck_api(toLogin) {
  return request({
    url: `/session/check?toLogin=${toLogin}`,
    method: 'get'
  })
}

export function about_api() {
  return request({
    url: '/version',
    method: 'get'
  })
}

export function logout_api() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}
