// import utils from '@/utils'
import request from '@/common/request'
import Qs from 'qs'

export function vote_search_api(init, form, pageNumber) {
  const param = {
    init: init,
    title: form.title,
    status: form.status,
    feedback: true,
    pageNumber: pageNumber
  }
  return request({
    url: '/vote/search',
    method: 'post',
    data: Qs.stringify(param)
  })
}

export function vote_get_api(id) {
  return request({
    url: '/vote/get/' + id,
    method: 'get'
  })
}

export function vote_save_api(optType, param, questionList) {
  const data = {
    optType: optType,
    ...param,
    questionList: questionList
  }

  return request({
    url: '/vote/save',
    method: 'post',
    headers: { 'Content-Type': 'application/json;charset=utf-8' },
    data: data
  })
}

export function vote_response_api(id) {
  return request({
    url: '/vote/response/' + id,
    method: 'get'
  })
}

export function feedback_search_api(id, init, form, pageNumber) {
  const param = {
    init: init,
    wjId: form.wjId,
    startTime: form.dateRange.length > 0 ? form.dateRange[0] : '',
    endTime: form.dateRange.length > 1 ? form.dateRange[1] : '',
    status: form.status,
    pageNumber: pageNumber
  }
  return request({
    url: '/vote/response/' + id,
    method: 'post',
    data: Qs.stringify(param)
  })
}

export function feedback_delete_api(id) {
  return request({
    url: '/vote/response/' + id,
    method: 'delete'
  })
}

export function delete_api(id) {
  return request({
    url: '/vote/delete/' + id,
    method: 'delete'
  })
}

export function cancel_api(id) {
  return request({
    url: '/vote/cancel/' + id,
    method: 'cancel'
  })
}
