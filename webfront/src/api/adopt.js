import request from '@/common/request'
import Qs from "qs";
import utils from "@/utils";

export function list_init_api(form, pageNumber, sorts) {
  const param = {
    adoptNo: form.adoptNo,
    donateNo: form.donateNo,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/adopt/list',
    method: 'get',
    params: param
  })
}

export function adopt_save_api(optType, form) {

  const param = {
    id: form.id,
    adoptNo: form.adoptNo,
    donateNo: form.donateNo,
    seatNo: form.seatNo,
    seatName: form.seatName,
    seatSize: form.seatSize,
    seatMaterial: form.seatMaterial,
    ownerUnit: form.ownerUnit,
    constructUnit: form.constructUnit,
    seatIntroduce: form.seatIntroduce,
    seatPrice: form.seatPrice,
    seatImage: form.seatImage,
    seatImageList: form.seatImageList,
    province: form.province,
    city: form.city,
    strict: form.strict,
    street: form.street,
    gpsLng: form.gpsLng,
    gpsLat: form.gpsLat,
    adress: form.adress,
    adoptPrice: form.adoptPrice,
    adoptTerm: form.adoptTerm
  }

  return request({
    url: optType === 'add' ? '/adopt/add' : '/adopt/edit',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: utils.formData(param)
  })
}

export function adopt_init_api(optType, id) {

  return request({
    url: '/adopt/init?id=' + id,
    method: 'get',
    params: { id, optType }
  })
}

export function seat_get_api(id) {
  return request({
    url: '/adopt/seat/' + id,
    method: 'get'
  })
}

export function list_apply_init_api(init, form, pageNumber, sorts) {
  const param = {
    adoptId: form.adoptId,
    donateNo: form.donateNo,
    status: form.status,
    name: form.name,
    init:init,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/adopt-apply/list',
    method: 'get',
    params: param
  })
}

export function adopt_apply_save_api(optType, form) {

  const param = {
    id: form.id,
    name: form.name,
    idCardNo: form.idCardNo,
    sex: form.sex,
    age: form.age,
    tel: form.tel,
    province: form.province,
    city: form.city,
    strict: form.strict,
    channel: form.channel,
    reason: form.reason,
    donationWord: form.donationWord,
    donationWordFlag:form.donationWordFlag,
    status: form.status
  }

  return request({
    url: optType === 'add' ? '/adopt-apply/add' : '/adopt-apply/edit',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: utils.formData(param)
  })
}


export function adopt_apply_init_api(id) {
  return request({
    url: '/adopt-apply/init?id=' + id,
    method: 'get'
  })
}

export function list_upkeep_init_api(init, form, pageNumber, sorts) {
  const param = {
    adoptId: form.adoptId,
    donateNo: form.donateNo,
    status: form.status,
    name: form.name,
    init:init,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/adopt-upkeep/list',
    method: 'get',
    params: param
  })
}

export function adopt_upkeep_save_api(optType, form) {
  return request({
    url: '/adopt-upkeep/edit',
    method: 'post',
    data: Qs.stringify(form)
  })
}

export function adopt_upkeep_init_api(id) {
  return request({
    url: '/adopt-upkeep/init?id=' + id,
    method: 'get'
  })
}

export function adopt_save_status_api(id, status) {
  const param = {
    id: id,
    status: status
  }

  return request({
    url: '/adopt/updStatus',
    method: 'post',
    data: Qs.stringify(param)
  })
}

export function adopt_apply_save_status_api(id, status) {
  const param = {
    id: id,
    status: status
  }

  return request({
    url: '/adopt-apply/updStatus',
    method: 'post',
    data: Qs.stringify(param)
  })
}

export function adopt_export_api(form) {
  const param = {
    adoptNo: form.adoptNo,
    donateNo: form.donateNo,
    status: form.status,
    name: form.name,
  }
  return request({
    url: '/adopt/export',
    method: 'get',
    responseType: 'arraybuffer',
    params: param
  })
}

export function adopt_apply_export_api(form) {
  const param = {
    donateNo: form.donateNo,
    status: form.status,
    name: form.name,
  }
  return request({
    url: '/adopt-apply/export',
    method: 'get',
    responseType: 'arraybuffer',
    params: param
  })
}

export function upkeep_apply_export_api(form) {
  const param = {
    donateNo: form.donateNo,
    status: form.status,
    name: form.name,
  }
  return request({
    url: '/adopt-upkeep/export',
    method: 'get',
    responseType: 'arraybuffer',
    params: param
  })
}

export function delete_api(id) {
  return request({
    url: '/adopt/delete/' + id,
    method: 'delete'
  })
}

export function delete_apply_api(id) {
  return request({
    url: '/adopt-apply/delete/' + id,
    method: 'delete'
  })
}

export function delete_upkeep_api(id) {
  return request({
    url: '/adopt-upkeep/delete/' + id,
    method: 'delete'
  })
}

export function cancel_api(id) {
  return request({
    url: '/adopt/cancel/' + id,
    method: 'get'
  })
}
