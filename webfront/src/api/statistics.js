import request from '@/common/request'

export function list_user_api(form, pageNumber, sorts) {
  const param = {
    name: form.name,
    idCardNo: form.idCardNo,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/statistics/user/list',
    method: 'get',
    params: param
  })
}

export function list_seat_api(init, form, pageNumber, sorts) {
  const param = {
    areaType: form.areaType,
    init: init,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/statistics/seat/list',
    method: 'get',
    params: param
  })
}

export function list_payment_api(init, form, pageNumber, sorts) {
  const param = {
    areaType: form.areaType,
    init: init,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/statistics/payment/list',
    method: 'get',
    params: param
  })
}

export function list_user_adopt_api(form, pageNumber, sorts) {
  const param = {
    name: form.name,
    idCardNo: form.idCardNo,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/statistics/user-adopt/list',
    method: 'get',
    params: param
  })
}

export function list_seat_adopt_api(init, form, pageNumber, sorts) {
  const param = {
    areaType: form.areaType,
    sortType: form.sortType,
    init: init,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/statistics/seat-adopt/list',
    method: 'get',
    params: param
  })
}

export function list_user_all_api(form, pageNumber, sorts) {
  const param = {
    name: form.name,
    idCardNo: form.idCardNo,
    pageNumber: pageNumber,
    sorts: sorts
  }
  return request({
    url: '/statistics/user-all/list',
    method: 'get',
    params: param
  })
}

export function list_user_export_api(form) {
  const param = {
    name: form.name,
    idCardNo: form.idCardNo
  }
  return request({
    url: '/statistics/export/user',
    method: 'get',
    responseType: 'arraybuffer',
    params: param
  })

}

export function list_seat_export_api(form) {
  const param = {
    areaType: form.areaType,
    sortType: form.sortType
  }
  return request({
    url: '/statistics/export/seat',
    method: 'get',
    responseType: 'arraybuffer',
    params: param
  })

}

export function list_payment_export_api(form) {
  const param = {
    areaType: form.areaType,
    sortType: form.sortType
  }
  return request({
    url: '/statistics/export/payment',
    method: 'get',
    responseType: 'arraybuffer',
    params: param
  })

}

export function list_user_adopt_export_api(form) {
  const param = {
    name: form.name,
    idCardNo: form.idCardNo
  }
  return request({
    url: '/statistics/export/user-adopt',
    method: 'get',
    responseType: 'arraybuffer',
    params: param
  })

}

export function list_seat_adopt_export_api(form) {
  const param = {
    areaType: form.areaType,
    sortType: form.sortType
  }
  return request({
    url: '/statistics/export/seat-adopt',
    method: 'get',
    responseType: 'arraybuffer',
    params: param
  })

}

export function list_user_all_export_api(form) {
  const param = {
    name: form.name,
    idCardNo: form.idCardNo
  }
  return request({
    url: '/statistics/export/user-all',
    method: 'get',
    responseType: 'arraybuffer',
    params: param
  })

}

