import router from '.'
import store from '@/common/store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import Cookies from 'js-cookie'
import { Message } from 'element-ui'

NProgress.configure({ showSpinner: false })// NProgress configuration

const whiteList = ['/password/change']
router.beforeEach(async(to, from, next) => {
  NProgress.start()
  document.title = to.meta.title + ' - Shared seats'

  setTimeout(() => Message.closeAll(), 200)
  const token = Cookies.get('SEATS_LOGIN_TOKEN')
  if (token) {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      const toLogin = to.path === '/login'
      const toTop = to.path === '/top'
      store.dispatch('HealthCheck', toLogin || toTop).then(res => {
        if (toLogin) {
          next({ path: '/top' })
          document.title = '首页 - Shared seats'
        } else {
          next()
        }
      }).catch(() => {
        toLogin && next()
        toTop && next('/login')
        NProgress.done()
      })
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1 || to.path === '/login') {
      next()
    } else {
      // next(`/login?redirect=${to.path}`)
      next('/login')
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
