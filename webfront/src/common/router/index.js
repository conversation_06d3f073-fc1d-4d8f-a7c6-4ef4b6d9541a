import Vue from 'vue'
import Router from 'vue-router'
import Layout from '@/components/layout/Layout'
import store from '@/common/store'
import event from '@/common/router/RouterEvent'

Vue.use(Router)

const constantRouterMap = [
  {
    path: '/login',
    component: () => import('@/views/login'),
    meta: { title: '登录' },
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    meta: { title: '404', hidden: true }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/top',
    children: [
      {
        path: '/top',
        component: () => import('@/views/top'),
        meta: { title: '首页', icon: 'home' }
      }
    ]
  },
  // {
  //   path: '/seats',
  //   component: Layout,
  //   redirect: '/seats/list',
  //   name: '座椅管理',
  //   alwaysShow: true,
  //   meta: { title: '座椅管理', icon: 'statistic' },
  //   children: [
  //     {
  //       path: '/seats/list',
  //       component: () => import('@/views/seats/main'),
  //       meta: { title: '座椅列表', icon: 'statistic' }
  //     },
  //     {
  //       path: '/seats/add',
  //       name: 'seats_add',
  //       hidden: true,
  //       component: () => import('@/views/seats/edit'),
  //       meta: { title: '座椅添加', activeMenu: { path: '/seats/list', title: '座椅列表' } }
  //     },
  //     {
  //       path: '/seats/edit/:key',
  //       name: 'seats_edit',
  //       hidden: true,
  //       component: () => import('@/views/seats/edit'),
  //       meta: { title: '座椅编辑', activeMenu: { path: '/seats/list', title: '座椅列表' } }
  //     }
  //   ]
  // },
  {
    path: '/seats-donate',
    component: Layout,
    redirect: '/seats-donate/list',
    name: '捐赠管理',
    alwaysShow: true,
    meta: { title: '捐赠管理', icon: 'donate' },
    children: [
      {
        path: '/seats-donate/list',
        component: () => import('@/views/donate/main'),
        meta: { title: '捐赠列表', icon: 'list', keepAlive: true }
      },
      {
        path: '/seats-donate/add',
        name: 'seats-donate_add',
        hidden: true,
        component: () => import('@/views/donate/edit'),
        meta: { title: '新增捐赠', activeMenu: { path: '/seats-donate/list', title: '捐赠列表' } }
      },
      {
        path: '/seats-donate/edit/:key',
        name: 'seats-donate_edit',
        hidden: true,
        component: () => import('@/views/donate/edit'),
        meta: { title: '修改捐赠', activeMenu: { path: '/seats-donate/list', title: '捐赠列表' } }
      },
      {
        path: '/seats-donate/apply',
        component: () => import('@/views/donate/main_apply'),
        meta: { title: '捐赠反馈', icon: 'apply', keepAlive: true  }
      },
      {
        path: '/seats-donate/applyInfo/:key',
        name: 'seats-donate_apply_info',
        hidden: true,
        component: () => import('@/views/donate/main_apply'),
        meta: { title: '捐赠反馈', icon: 'apply', keepAlive: true }
      },
      {
        path: '/seats-donate/commentInfo/:key',
        name: 'seats-donate_comment_info',
        hidden: true,
        component: () => import('@/views/donate/main_comment'),
        meta: { title: '评论详情', icon: 'comment' }
      },
      {
        path: '/seats-donate/add_apply',
        name: 'seats-donate_add_apply',
        hidden: true,
        component: () => import('@/views/donate/edit_apply'),
        meta: { title: '新增捐赠反馈', activeMenu: { path: '/seats-donate/apply', title: '捐赠反馈' } }
      },
      {
        path: '/seats-donate/edit_apply/:key',
        name: 'seats-donate_edit_apply',
        hidden: true,
        component: () => import('@/views/donate/edit_apply'),
        meta: { title: '修改捐赠反馈', activeMenu: { path: '/seats-donate/apply', title: '捐赠反馈' } }
      }
    ]
  },
  {
    path: '/seats-adopt',
    component: Layout,
    redirect: '/seats-adopt/list',
    name: '认养管理',
    alwaysShow: true,
    meta: { title: '认养管理', icon: 'adopt' },
    children: [
      {
        path: '/seats-adopt/list',
        component: () => import('@/views/adopt/main'),
        meta: { title: '认养列表', icon: 'list', keepAlive: true }
      },
      {
        path: '/seats-adopt/add',
        name: 'seats-adopt_add',
        hidden: true,
        component: () => import('@/views/adopt/edit'),
        meta: { title: '新增认养', activeMenu: { path: '/seats-adopt/list', title: '认养列表' } }
      },
      {
        path: '/seats-adopt/edit/:key',
        name: 'seats-adopt_edit',
        hidden: true,
        component: () => import('@/views/adopt/edit'),
        meta: { title: '修改认养', activeMenu: { path: '/seats-adopt/list', title: '认养列表' } }
      },
      {
        path: '/seats-adopt/apply',
        component: () => import('@/views/adopt/main_apply'),
        meta: { title: '认养反馈', icon: 'apply', keepAlive: true }
      },
      {
        path: '/seats-adopt/applyInfo/:key',
        name: 'seats-adopt_apply_info',
        hidden: true,
        component: () => import('@/views/adopt/main_apply'),
        meta: { title: '认养反馈', icon: 'apply', keepAlive: true }
      },
      {
        path: '/seats-adopt/edit_apply/:key',
        name: 'seats-adopt_edit_apply',
        hidden: true,
        component: () => import('@/views/adopt/edit_apply'),
        meta: { title: '修改认养反馈', activeMenu: { path: '/seats-adopt/apply', title: '认养反馈' } }
      },
      {
        path: '/seats-upkeep/upkeep',
        component: () => import('@/views/upkeep/main'),
        meta: { title: '养护管理', icon: 'list', keepAlive: true }
      },
      {
        path: '/seats-upkeep/edit/:key',
        name: 'upkeep-assign',
        hidden: true,
        component: () => import('@/views/upkeep/edit'),
        meta: { title: '分配任务', icon: 'upkeep' }
      },
      {
        path: '/upkeep-assign/list-seat/:key',
        name: 'upkeep-assign-seat-list',
        hidden: true,
        component: () => import('@/views/upkeep/main_upkeep'),
        meta: { title: '维护记录', icon: 'upkeep' }
      },
      {
        path: '/upkeep-assign/init-seat/:key',
        name: 'upkeep-assign-seat-init',
        hidden: true,
        component: () => import('@/views/upkeep/edit_upkeep'),
        meta: { title: '维护详情', icon: 'upkeep' }
      },
      {
        path: '/seats-adopt/upkeepInfo/:key',
        name: 'seats-adopt_upkeep_info',
        hidden: true,
        component: () => import('@/views/adopt/main_upkeep'),
        meta: { title: '维护反馈', icon: 'upkeep' }
      },
      {
        path: '/seats-adopt/edit_upkeep/:key',
        name: 'seats-adopt_edit_upkeep',
        hidden: true,
        component: () => import('@/views/adopt/edit_upkeep'),
        meta: { title: '查询维护记录', activeMenu: { path: '/seats-adopt/upkeep', title: '维护列表' } }
      }
    ]
  },
  {
    path: '/vote',
    component: Layout,
    redirect: '/vote/list',
    name: '投票管理',
    meta: { title: '投票管理', icon: 'form' },
    children: [
      {
        path: '/vote/list',
        component: () => import('@/views/vote/main'),
        meta: { title: '投票问卷', icon: 'list', keepAlive: true }
      },
      {
        path: '/vote/add',
        name: 'vote_add',
        hidden: true,
        component: () => import('@/views/vote/edit'),
        meta: { title: '问卷添加', activeMenu: { path: '/vote/list', title: '投票问卷' } }
      },
      {
        path: '/vote/edit/:key',
        name: 'vote_edit',
        hidden: true,
        component: () => import('@/views/vote/edit'),
        meta: { title: '问卷修改', activeMenu: { path: '/vote/list', title: '投票问卷' } }
      },
      {
        path: '/vote/response',
        component: () => import('@/views/vote/response'),
        meta: { title: '投票反馈', icon: 'apply3' }
      },
      {
        path: '/vote/response-detail/:key',
        component: () => import('@/views/vote/response-detail'),
        hidden: true,
        meta: { title: '投票反馈详细', activeMenu: { path: '/vote/response', title: '投票反馈' }, keepAlive: true }
      }
    ]
  },
  {
    path: '/statistics-donate',
    component: Layout,
    redirect: '/statistics-donate/list',
    name: '捐赠统计',
    alwaysShow: true,
    meta: { title: '捐赠统计', icon: 'donate' },
    children: [
      {
        path: '/statistics-donate-user/list',
        component: () => import('@/views/statistics/main_user'),
        meta: { title: '用户统计', icon: 'list', keepAlive: true }
      },
      {
        path: '/statistics-donate-seat/list',
        component: () => import('@/views/statistics/main_seat'),
        meta: { title: '座椅统计', icon: 'list', keepAlive: true }
      },
      {
        path: '/statistics-donate-payment/list',
        component: () => import('@/views/statistics/main_payment'),
        meta: { title: '款项统计', icon: 'list', keepAlive: true }
      },
    ]
  },
  {
    path: '/statistics-adopt',
    component: Layout,
    redirect: '/statistics-adopt/list',
    name: '认养统计',
    alwaysShow: true,
    meta: { title: '认养统计', icon: 'adopt' },
    children: [
      {
        path: '/statistics-adopt-user/list',
        component: () => import('@/views/statistics/main_user_adopt'),
        meta: { title: '用户统计', icon: 'list', keepAlive: true }
      },
      {
        path: '/statistics-adopt-seat/list',
        component: () => import('@/views/statistics/main_seat_adopt'),
        meta: { title: '座椅统计', icon: 'list', keepAlive: true }
      }
    ]
  },
  // {
  //   path: '/statistics-user-all',
  //   component: Layout,
  //   redirect: '/statistics-all/user',
  //   name: '用户统计',
  //   alwaysShow: true,
  //   meta: { title: '用户统计', icon: 'user' },
  //   children: [
  //     {
  //       path: '/statistics-user-all/list',
  //       component: () => import('@/views/statistics/main_user_all'),
  //       meta: { title: '捐养统计', icon: 'list' }
  //     }
  //   ]
  // },
  {
    path: '/import',
    component: Layout,
    redirect: '/import/user',
    name: '导入管理',
    meta: { title: '导入管理', icon: 'system' },
    children: [
      {
        path: '/import/user',
        name: 'user',
        component: () => import('@/views/import/upkeep_user'),
        meta: { title: '养护导入', icon: 'user' }
      }, {
        path: '/import/user/add',
        name: 'user_add',
        hidden: true,
        component: () => import('@/views/import/upkeep_user_edit'),
        meta: { title: '养护人员添加', activeMenu: { path: '/import/user', title: '养护导入' } }
      },
    ]
  },
  {
    path: '/other',
    component: Layout,
    redirect: '/other/management',
    name: '其他功能',
    meta: { title: '其他功能', icon: 'system' },
    children: [
      {
        path: '/other/broadcast',
        name: 'broadcast',
        component: () => import('@/views/other/broadcast'),
        meta: { title: '广播管理', icon: 'list' }
      },
      {
        path: '/other/broadcast/add',
        name: 'broadcast_add',
        hidden: true,
        component: () => import('@/views/other/broadcast_edit'),
        meta: { title: '广播添加', activeMenu: { path: '/other/broadcast', title: '广播管理' } }
      },
      {
        path: '/other/broadcast/edit/:key',
        name: 'broadcast_edit',
        hidden: true,
        component: () => import('@/views/other/broadcast_edit'),
        meta: { title: '广播修改', activeMenu: { path: '/other/broadcast', title: '广播管理' } }
      },
      {
        path: '/other/info',
        name: 'info',
        component: () => import('@/views/other/information'),
        meta: { title: '资讯管理', icon: 'list' }
      },
      {
        path: '/other/info/add',
        name: 'info_add',
        hidden: true,
        component: () => import('@/views/other/information_edit'),
        meta: { title: '资讯添加', activeMenu: { path: '/other/info', title: '资讯管理' } }
      },
      {
        path: '/other/info/edit/:key',
        name: 'info_edit',
        hidden: true,
        component: () => import('@/views/other/information_edit'),
        meta: { title: '资讯修改', activeMenu: { path: '/other/info', title: '资讯管理' } }
      },
      {
        path: '/other/info_detail',
        name: 'info_detail',
        component: () => import('@/views/other/information_detail'),
        meta: { title: '资讯详情管理', icon: 'list' }
      },
      {
        path: '/other/info_detail/add',
        name: 'info_detail_add',
        hidden: true,
        component: () => import('@/views/other/information_detail_edit'),
        meta: { title: '资讯详情添加', activeMenu: { path: '/other/info_detail', title: '资讯详情管理' } }
      },
      {
        path: '/other/info_detail/edit/:key',
        name: 'info_detail_edit',
        hidden: true,
        component: () => import('@/views/other/information_detail_edit'),
        meta: { title: '资讯详情修改', activeMenu: { path: '/other/info_detail', title: '资讯详情管理' } }
      },
      {
        path: '/other/certificate',
        name: 'certificate',
        hidden: true,
        component: () => import('@/views/other/certificate'),
        meta: { title: '证书管理', icon: 'list' }
      },
      {
        path: '/other/certificate/add',
        name: 'certificate_add',
        hidden: true,
        component: () => import('@/views/other/certificate_edit'),
        meta: { title: '证书添加' }
      },
      {
        path: '/other/certificate/edit/:key',
        name: 'certificate_edit',
        hidden: true,
        component: () => import('@/views/other/certificate_edit'),
        meta: { title: '证书修改', activeMenu: { path: '/other/certificate', title: '证书管理' } }
      },
    ]
  },
  // {
  //   path: '/system',
  //   component: Layout,
  //   redirect: '/system/user',
  //   name: '系统管理',
  //   meta: { title: '系统管理', icon: 'system' },
  //   children: [
  //     {
  //       path: '/system/user',
  //       name: 'user',
  //       component: () => import('@/views/system/user'),
  //       meta: { title: '用户管理', icon: 'user' }
  //     },
  //     {
  //       path: '/system/role',
  //       name: 'role',
  //       component: () => import('@/views/system/role'),
  //       meta: { title: '权限管理', icon: 'role' }
  //     }
  //   ]
  // },
  { path: '*', redirect: '/404', hidden: true }
]

const filterRoutes = function (routes, authorities) {
  function hasPermission(route) {
    if (route.meta && route.meta.authorities) {
      return authorities && authorities.some(auth => route.meta.authorities.includes(auth))
    } else {
      return true
    }
  }
  function filter(children) {
    let num = 0
    if (children) {
      children.forEach(r => {
        if (r.hidden !== true) {
          num++
        }
      })
    }
    return num === 0
  }
  const res = []
  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(tmp)) {
      if (tmp.children) {
        tmp.children = filterRoutes(tmp.children, authorities)
      }
      if (filter(tmp.children)) {
        // tmp.children = null
        tmp.show = false
      }
      res.push(tmp)
    }
  })
  return res
}

const createRouter = () => {
  const accessRoutes = filterRoutes(constantRouterMap, store.getters.authorities)
  // saveRouter(accessRoutes)
  return new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: accessRoutes
  })
}

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
  router.options.routes = newRouter.options.routes
}

event.$on('resetRouter', () => resetRouter())

export default router
