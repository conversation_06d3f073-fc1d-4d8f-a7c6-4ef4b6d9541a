import { login_api, logout_api, healthCheck_api, about_api } from '@/api/login'
import event from '@/common/router/RouterEvent'
import { MessageBox } from 'element-ui'
import Cookies from 'js-cookie'

const auth = {
  state: {
    user: {}
  },

  mutations: {
    SET_USER_INFO: (state, user) => {
      state.user = user
    },
    SET_PT_NAME: (state, partnerId) => {
      if (state.user) {
        state.user.partnerId = partnerId
      }
    }
  },

  actions: {
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      return new Promise((resolve, reject) => {
        login_api(username, userInfo.password).then(response => {
          const { sessionId, userId, userName, userEmail, roles, authorities } = response.value
          commit('SET_USER_INFO', { userId, userName, userEmail, roles, authorities })
          Cookies.set('SEATS_LOGIN_TOKEN', sessionId)
          event.$emit('resetRouter')
          resolve(userId)
        }).catch(error => {
          reject(error)
        })
      })
    },
    HealthCheck({ commit }, toLogin) {
      return new Promise((resolve, reject) => {
        healthCheck_api(toLogin).then(response => {
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },

    About() {
      return new Promise((resolve, reject) => {
        about_api().then(res => {
          const data = res.value
          const message = '<span style="width:80px"><strong>Version:</strong></span><span>' + data.version + '</span></br>' +
            '<span style="width:80px"><strong>BuildTime:</strong></span><span>' + data.timestamp + '</span>'
          MessageBox.confirm(
            message,
            'バージョン',
            {
              showConfirmButton: false,
              showCancelButton: false,
              dangerouslyUseHTMLString: true,
              type: 'info'
            }
          ).catch(() => {
          })
        }).catch(err => {
          reject(err)
        })
      })
    },

    LogOut({ commit }) {
      return new Promise((resolve, reject) => {
        logout_api().then(() => {
          Cookies.remove('SEATS_LOGIN_TOKEN')
          commit('SET_USER_INFO', {})
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_USER_INFO', {})
        Cookies.remove('SEATS_LOGIN_TOKEN')
        resolve()
      })
    }
  }
}

export default auth
