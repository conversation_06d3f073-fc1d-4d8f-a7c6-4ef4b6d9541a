import axios from 'axios'
import { Message, MessageBox } from 'element-ui'
import Utils from '@/utils'
import store from '@/common/store'
import Qs from 'qs'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style

NProgress.configure({ showSpinner: false })// NProgress configuration

const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // api 的 base_url
  timeout: 50 * 10000, // timeout
  withCredentials: true,
  paramsSerializer: function(params) {
    return Qs.stringify(params)
  },
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
    'Access-Control-Allow-Origin': process.env.BASE_API,
    'Access-Control-Allow-Credentials': true
  }
})

// Request Interceptor
service.interceptors.request.use(
  config => {
    NProgress.start()
    const uuid = Utils.guid()
    sessionStorage.setItem('X-REQUEST-ID', uuid)
    config.headers['X-REQUEST-ID'] = uuid
    return config
  },
  error => {
    // Do something with request error
    console.log(error) // for debug
    Promise.reject(error)
  }
)

// response Interceptor
service.interceptors.response.use(
  response => {
    NProgress.done()
    let res = response.data
    if (response.config.responseType === 'arraybuffer') {
      const contentType = response.headers['content-type']
      if (contentType.startsWith('application/json')) {
        const data = new Uint8Array(res)
        res = JSON.parse(Utils.utf8ArrayToString(data))
      } else {
        Utils.download(response)
        return Promise.resolve(res)
      }
    }

    if (res.statusCode !== '20000') {
      // 50008:invalid token;50012:用户权限发生变化。50014:Session过期。
      let message = ''
      if (res.statusCode === '50008') {
        message = '非法操作。'
      } else if (res.statusCode === '50012') {
        message = '用户权限发生了变化。'
      } else if (res.statusCode === '50014') {
        message = '长时间未操作。'
      }
      if (res.statusCode === '50008' || res.statusCode === '50012' || res.statusCode === '50014') {
        MessageBox.confirm(
          message + '请重新登录。',
          '退出',
          {
            confirmButtonText: '重新登录',
            showCancelButton: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            closeOnHashChange: false,
            showClose: false,
            type: 'warning'
          }
        ).then(() => {
          store.dispatch('FedLogOut').then(() => {
            location.reload()
          })
        })
        return Promise.reject(res)
      } else if (res.statusCode === '20008' && res.custom === false) {
        let msg = ''
        const length = res.messages.length
        res.messages.forEach((m, index) => {
          // if () {}
          let color = ''
          if (m.type === 'E') {
            color = '#F00'
          } else if (m.type === 'W') {
            color = '#FFA027'
          } else if (m.type === 'I') {
            color = '#198BFF'
          }
          msg += '<div style="margin-bottom: 1px; color: ' + color + ';">・' + m.message + '</div>'
          if (index < length - 1) msg += '<br/>'
        })

        let type = ''
        if (res.type === 'E') {
          type = 'error'
        } else if (res.type === 'W') {
          type = 'warning'
        } else if (res.type === 'I') {
          type = 'success'
        } else {
          type = 'info'
        }
        const requestId = sessionStorage.getItem('X-REQUEST-ID')
        const xRequestId = response.config.headers['X-REQUEST-ID']
        if (requestId === xRequestId) {
          // 业务异常发生的时候
          setTimeout(function() {
            Message({ showClose: true, dangerouslyUseHTMLString: true, message: msg, type: type })
          }, 200)
          if (res.type === 'S' || res.type === 'I') {
            return res
          } else {
            return Promise.reject(res)
          }
        } else {
          // TODO
        }
      } else {
        return Promise.reject(res)
      }
    } else {
      return res
    }
  },
  error => {
    NProgress.done()
    console.log('err: ' + error) // for debug
    let message = ''
    if (error && error.message === 'Network Error') {
      message = '网络异常。'
    } else {
      message = error.message
    }
    setTimeout(function() {
      Message({ showClose: true, message: message, type: 'error', duration: 5 * 1000 })
    }, 500)
    return Promise.reject(error)
  }
)

export default service
