{"name": "shared-seats-webui", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode dev", "build": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint"}, "dependencies": {"async-validator": "^4.0.7", "axios": "^0.24.0", "core-js": "^3.6.5", "echarts": "^5.4.0", "element-china-area-data": "^6.1.0", "element-ui": "2.15.8", "js-cookie": "^3.0.1", "nprogress": "^0.2.0", "vue": "^2.6.11", "vue-baidu-map": "^0.21.22", "vue-clipboard2": "^0.3.3", "vue-pdf": "4.2.0", "vue-router": "^3.2.0", "vue-slide-up-down": "^2.0.1", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-preset-es2015": "^6.24.1", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "^2.1.5", "svg-sprite-loader": "^6.0.11", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"space-before-function-paren": 0, "camelcase": 0, "no-debugger": 1, "no-irregular-whitespace": 0, "vue/no-use-v-if-with-v-for": 0, "vue/order-in-components": 2, "vue/attributes-order": 2, "vue/html-indent": 2, "vue/no-multi-spaces": 2}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}