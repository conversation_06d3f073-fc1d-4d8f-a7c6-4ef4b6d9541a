<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.web.seats.SeatRestMapper">

  <select id="selectList" resultType="cn.xzxx.seats.web.seats.entity.SeatRecordEntity">
    select
        a.*
    from
        seat_info a
    <where>
      <if test="param.seatNo != null and param.seatNo != ''">
        and a.seat_no = #{param.seatNo}
      </if>
    </where>
  </select>
</mapper>
