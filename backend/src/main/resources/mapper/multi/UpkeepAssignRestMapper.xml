<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.web.upkeep.UpkeepAssignRestMapper">

  <select id="selectList" resultType="cn.xzxx.seats.web.upkeep.entity.UpkeepAssignRecordEntity">
    select * from (
        select
          ua.id,
          ua.adopt_no,
          ua.donate_no,
          si.seat_no,
          si.name as seat_name,
          si.price as seat_price,
          dsi.province,
          dsi.city,
          dsi.strict,
          dsi.street,
          dsi.adress,
          ua.status,
          ua.created_at
        from
            upkeep_assign ua,donate_seat_info dsi,seat_info si
        <where>
            ua.seat_type = 1
            and ua.seat_id = dsi.seat_id
            and ua.seat_id = si.id
            <if test="param.donateNo != null and param.donateNo != ''">
                and ua.donate_no = #{param.donateNo}
            </if>
            <if test="param.adoptNo != null and param.adoptNo != ''">
                and ua.adopt_no = #{param.adoptNo}
            </if>
            <if test="param.status != null and param.status != ''">
                and ua.status = #{param.status}
            </if>
        </where>

        union

          select
          ua.id,
          ua.adopt_no,
          ua.donate_no,
          si.seat_no,
          si.name as seat_name,
          si.price as seat_price,
          dsi.province,
          dsi.city,
          dsi.strict,
          dsi.street,
          dsi.adress,
          ua.status,
          ua.created_at
          from
          upkeep_assign ua,adopt_seat_info dsi,seat_info_adopt si
          <where>
              ua.seat_type = 2
              and ua.seat_id = dsi.seat_id
              and ua.seat_id = si.id
              <if test="param.donateNo != null and param.donateNo != ''">
                  and ua.donate_no = #{param.donateNo}
              </if>
              <if test="param.adoptNo != null and param.adoptNo != ''">
                  and ua.adopt_no = #{param.adoptNo}
              </if>
              <if test="param.status != null and param.status != ''">
                  and ua.status = #{param.status}
              </if>
          </where>
        ) total
  </select>

    <select id="selectSeatList" resultType="cn.xzxx.seats.web.upkeep.entity.UpkeepAssignSeatRecordEntity">
        select
        au.id,
        au.upkeep_name,
        au.upkeep_company,
        au.upkeep_time,
        au.memo
        from
        adopt_upkeep au
        <where>
            au.upkeep_id = #{param.upkeepId}
            <if test="param.upkeepName != null and param.upkeepName != ''">
                and au.upkeep_name = #{param.upkeepName}
            </if>
            <if test="param.upkeepCompany != null and param.upkeepCompany != ''">
                and au.upkeep_company = #{param.upkeepCompany}
            </if>
        </where>
    </select>
</mapper>
