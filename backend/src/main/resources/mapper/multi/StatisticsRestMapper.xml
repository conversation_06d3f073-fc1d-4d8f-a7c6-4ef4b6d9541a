<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.web.statistics.StatisticsRestMapper">

  <select id="selectUserList" resultType="cn.xzxx.seats.web.statistics.entity.StatisticsUserRecordEntity">
    select
      id_card_no,
      name,
      IFNULL(count( id_card_no ), 0 ) donate_count,
      sum( payed_price ) donate_total_amount
      from
      donate_seat_apply
    <where>
        status = 2
        and delete_flag = false
        <if test="param.name != null and param.name != ''">
            and name = #{param.name}
        </if>
        <if test="param.idCardNo != null and param.idCardNo != ''">
            and id_card_no = #{param.idCardNo}
        </if>
    </where>
      GROUP BY id_card_no, name
  </select>

    <select id="selectSeatList" resultType="cn.xzxx.seats.web.statistics.entity.StatisticsSeatRecordEntity">
        SELECT
            dsi.province,
            dsi.city,
            dsi.strict,
            dsi.street,
            IFNULL(count( dsi.id ), 0 ) seat_count,
            IFNULL( count( dsa.id ), 0 ) donate_count_total,
            IFNULL( sum( dsa.payed_price ), 0 ) payed_price_total
        FROM
            donate_seat_info dsi
            LEFT JOIN donate_seat_apply dsa ON dsi.id = dsa.donate_id and dsa.status=2
        <where>
            <![CDATA[ dsi.status > 1 ]]>
            and dsi.delete_flag = false
        </where>
        GROUP BY
            dsi.province
            <if test="param.areaType == 2">
                ,dsi.city
            </if>
            <if test="param.areaType == 3">
                ,dsi.city, dsi.strict
            </if>
            <if test="param.areaType == 4">
                ,dsi.city ,dsi.strict, dsi.street
            </if>
    </select>

    <select id="selectUserAdoptList" resultType="cn.xzxx.seats.web.statistics.entity.StatisticsUserRecordEntity">
        select
        id_card_no,
        name,
        IFNULL(count( id_card_no ), 0 ) adopt_count
        from
        adopt_seat_apply
        <where>
            status = 2
            <if test="param.name != null and param.name != ''">
                and name = #{param.name}
            </if>
            <if test="param.idCardNo != null and param.idCardNo != ''">
                and id_card_no = #{param.idCardNo}
            </if>
        </where>
        GROUP BY id_card_no, name
    </select>

    <select id="selectSeatAdoptList" resultType="cn.xzxx.seats.web.statistics.entity.StatisticsSeatRecordEntity">
        SELECT
        dsi.province,
        dsi.city,
        dsi.strict,
        dsi.street,
        IFNULL(count( dsi.id ), 0 ) seat_count,
        IFNULL( count( dsa.id ), 0 ) adopt_count_total
        FROM
        adopt_seat_info dsi
        LEFT JOIN adopt_seat_apply dsa ON dsi.id = dsa.adopt_id and dsa.status=2
        <where>
            <![CDATA[ dsi.status > 1 ]]>
        </where>
        GROUP BY
        dsi.province
        <if test="param.areaType == 2">
            ,dsi.city
        </if>
        <if test="param.areaType == 3">
            ,dsi.city, dsi.strict
        </if>
        <if test="param.areaType == 4">
            ,dsi.city ,dsi.strict, dsi.street
        </if>
    </select>

    <select id="selectUserAllList" resultType="cn.xzxx.seats.web.statistics.entity.StatisticsUserRecordEntity">
        SELECT
            dsa.id_card_no id_card_no,
            dsa.NAME NAME,
            IFNULL( count( dsa.id_card_no ), 0 ) donate_count,
            sum( dsa.payed_price ) donate_total_amount,
            IFNULL( asa.adopt_count , 0 ) adopt_count
        FROM
            donate_seat_apply dsa
            LEFT JOIN (SELECT id_card_no, NAME, count( id_card_no ) adopt_count FROM adopt_seat_apply WHERE STATUS = 2 GROUP BY id_card_no, NAME ) asa
                ON asa.NAME = dsa.NAME AND asa.id_card_no = dsa.id_card_no
        WHERE
            dsa.STATUS = 2
            and dsa.delete_flag = false
            <if test="param.name != null and param.name != ''">
                and dsa.NAME = #{param.name}
            </if>
            <if test="param.idCardNo != null and param.idCardNo != ''">
                and dsa.id_card_no = #{param.idCardNo}
            </if>
        GROUP BY
            dsa.id_card_no,
            dsa.NAME

        UNION ALL

        SELECT
            asa.id_card_no id_card_no,
            asa.NAME NAME,
            0 donate_count,
            0 donate_total_amount,
            IFNULL( count( asa.id_card_no ), 0 ) adopt_count
        FROM
            adopt_seat_apply asa
        WHERE
            asa.STATUS = 2
            AND (asa.NAME NOT IN ( SELECT NAME FROM donate_seat_apply WHERE STATUS = 2 )
                OR asa.id_card_no NOT IN ( SELECT id_card_no FROM donate_seat_apply WHERE STATUS = 2 ))
            <if test="param.name != null and param.name != ''">
                and asa.NAME = #{param.name}
            </if>
            <if test="param.idCardNo != null and param.idCardNo != ''">
                and asa.id_card_no = #{param.idCardNo}
            </if>
        GROUP BY
            asa.id_card_no,
            asa.NAME
    </select>
</mapper>
