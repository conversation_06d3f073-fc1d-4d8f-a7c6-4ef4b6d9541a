<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.web.adopt.AdoptRestMapper">

  <select id="selectList" resultType="cn.xzxx.seats.web.adopt.entity.AdoptRecordEntity">
    select
        ai.id,
        ai.adopt_no,
        ai.status,
        ai.donate_no,
        ai.adopt_price,
        ai.adopt_term,
        ai.province,
        ai.city,
        ai.strict,
        ai.street,
        ai.adress,
        ai.gps_lng,
        ai.gps_lat,
        ai.created_at,
        si.seat_no,
        si.name as seat_name,
        si.price as seat_price,
        si.`size` as seat_size,
        si.material as seat_material,
        si.image as seat_image,
        si.introduce as seat_introduce
    from
        adopt_seat_info ai, seat_info_adopt si
    <where>
        ai.seat_id = si.id
        <if test="param.donateNo != null and param.donateNo != ''">
        and ai.donate_no = #{param.donateNo}
        </if>
        <if test="param.adoptNo != null and param.adoptNo != ''">
            and ai.adopt_no = #{param.adoptNo}
        </if>
    </where>
  </select>

    <select id="selectApplyList" resultType="cn.xzxx.seats.web.adopt.entity.AdoptApplyRecordEntity">
        select
        aa.id,
        aa.name,
        aa.open_id,
        aa.id_card_no,
        aa.status,
        aa.apply_time,
        aa.apply_pass_time,
        ai.donate_no,
        ai.province,
        ai.city,
        ai.strict,
        ai.street,
        si.seat_no,
        si.name as seat_name,
        si.price as seat_price
        from
        adopt_seat_apply aa, adopt_seat_info ai, seat_info_adopt si
        <where>
            aa.adopt_id = ai.id
            and ai.seat_id = si.id
            <if test="param.adoptId != null and param.adoptId != ''">
                and aa.adopt_id = #{param.adoptId}
            </if>
            <if test="param.donateNo != null and param.donateNo != ''">
                and ai.donate_no = #{param.donateNo}
            </if>
            <if test="param.status != null and param.status != ''">
                and aa.status = #{param.status}
            </if>
            <if test="param.name != null and param.name != ''">
                and aa.name = #{param.name}
            </if>
        </where>
    </select>

    <select id="selectUpkeepList" resultType="cn.xzxx.seats.web.adopt.entity.AdoptUpkeepRecordEntity">
        select
        au.id,
        au.upkeep_time,
        au.upkeep_image_before,
        au.upkeep_image_after,
        au.memo,
        au.province,
        au.city,
        au.strict,
        au.street,
        au.adress,
        au.gps_lng,
        au.gps_lat,
        aa.name,
        aa.id_card_no,
        aa.status,
        aa.valid_time,
        ai.donate_no,
        ai.province seat_province,
        ai.city seat_city,
        ai.strict seat_strict,
        ai.street seat_street,
        si.seat_no,
        si.name as seat_name,
        si.price as seat_price
        from
        adopt_upkeep au, adopt_seat_apply aa, adopt_seat_info ai, seat_info_adopt si
        <where>
            au.adopt_id = aa.adopt_id
            and aa.adopt_id = ai.id
            and ai.seat_id = si.id
            and aa.status = 2
            <if test="param.adoptId != null and param.adoptId != ''">
                and au.adopt_id = #{param.adoptId}
            </if>
            <if test="param.donateNo != null and param.donateNo != ''">
                and ai.donate_no = #{param.donateNo}
            </if>
            <if test="param.status != null and param.status != ''">
                and au.status = #{param.status}
            </if>
            <if test="param.name != null and param.name != ''">
                and aa.name = #{param.name}
            </if>
        </where>
    </select>
</mapper>
