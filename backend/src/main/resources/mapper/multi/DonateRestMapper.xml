<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.web.donate.DonateRestMapper">

  <select id="selectList" resultType="cn.xzxx.seats.web.donate.entity.DonateRecordEntity">
    select
        di.id,
        di.donate_no,
        di.province,
        di.city,
        di.strict,
        di.street,
        di.adress,
        di.gps_lng,
        di.gps_lat,
        di.valid_time,
        di.status,
        di.created_at,
        si.seat_no,
        si.name as seat_name,
        si.price as seat_price,
        si.`size` as seat_size,
        si.material as seat_material,
        si.image as seat_image,
        si.introduce as seat_introduce
    from
        donate_seat_info di,seat_info si
    <where>
      di.seat_id = si.id
      <if test="param.donateNo != null and param.donateNo != ''">
        and di.donate_no = #{param.donateNo}
        and di.delete_flag = false
      </if>
      <if test="param.seatNo != null and param.seatNo != ''">
        and si.seat_no = #{param.seatNo}
      </if>
    </where>
  </select>

    <select id="selectApplyList" resultType="cn.xzxx.seats.web.donate.entity.DonateApplyRecordEntity">
        select
        da.id,
        da.name,
        da.open_id,
        da.id_card_no,
        da.share_num,
        da.status,
        da.apply_time,
        da.apply_pass_time,
        di.donate_no,
        di.province,
        di.city,
        di.strict,
        di.street,
        di.valid_time,
        si.seat_no,
        si.name as seat_name,
        si.price as seat_price
        from
        donate_seat_apply da, donate_seat_info di, seat_info si
        <where>
            da.donate_id = di.id
            and di.seat_id = si.id
            and da.delete_flag = false
            and di.delete_flag = false
            <if test="param.donateId != null and param.donateId != ''">
                and da.donate_id = #{param.donateId}
            </if>
            <if test="param.donateNo != null and param.donateNo != ''">
                and di.donate_no = #{param.donateNo}
            </if>
            <if test="param.status != null and param.status != ''">
                and da.status = #{param.status}
            </if>
            <if test="param.name != null and param.name != ''">
                and da.name = #{param.name}
            </if>
        </where>
    </select>

    <select id="selectCommentList" resultType="cn.xzxx.seats.web.donate.entity.DonateCommentRecordEntity">
        select
        dc.id,
        dc.donate_id,
        dc.comment,
        dc.open_id,
        dc.name,
        dc.status,
        dc.created_at,
        dc.updated_at,
        dc.updated_by
        from
        donate_comment dc
        <where>
            1 = 1
            <if test="param.donateId != null and param.donateId != ''">
                and dc.donate_id = #{param.donateId}
            </if>
            <if test="param.status != null">
                and dc.status = #{param.status}
            </if>
        </where>
    </select>
</mapper>
