<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.web.vote.VoteRestMapper">

  <select id="selectList" resultType="cn.xzxx.seats.web.vote.entity.VoteRecordEntity">
    select
    a.id,
    a.title,
    a.status,
    a.start_time as startTime,
    a.end_time as endTime,
    a.introduce,
    a.created_at as createdAt,
    b.quesCount,
    c.feedbackCount
    from
    vote_info a
    left join (
    select vote_id, count(*) as quesCount from vote_question group by vote_id
    ) b on a.id = b.vote_id
    left join (
    select vote_id, count(*) as feedbackCount from vote_response_info group by vote_id
    ) c on a.id = c.vote_id
    where a.status !=  9
    <if test="param.title != null and param.title != ''">
      and a.`title` like CONCAT('%', #{param.title}, '%')
    </if>
    <if test="param.status != null and param.status != ''">
      and a.`status` = #{param.status}
    </if>
    <if test="param.released == true">
      and (a.`status` = 1 or a.`status` = 2)
    </if>
  </select>

  <select id="selectFeedbackList" resultType="cn.xzxx.seats.web.vote.entity.VoteFeedbackEntity">
    select
    a.id,
    a.vote_id,
    a.open_id,
    a.start_time,
    a.end_time,
    a.value_flg,
    b.name as wx_name
    from
    vote_response_info a
    left join wx_user_info b on a.open_id = b.open_id
    <where>
      a.`vote_id` = #{param.voteId}
      <if test="param.startTime != null and param.startTime != ''">
        and a.`end_time` >= #{param.startTime}
      </if>
      <if test="param.endTime != null and param.endTime != ''">
        <![CDATA[ and a.`end_time` <= #{param.endTime} ]]>
      </if>
      <if test="param.status != null and param.status != ''">
        and a.`value_flg` = #{param.status}
      </if>
    </where>
  </select>
</mapper>
