<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.SysRoleAuthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.SysRoleAuthEntity">
        <id column="id" property="id" />
        <result column="role_id" property="roleId" />
        <result column="auth_id" property="authId" />
        <result column="role_auth_value" property="roleAuthValue" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_id, auth_id, role_auth_value, created_at, updated_at, updated_by
    </sql>

</mapper>
