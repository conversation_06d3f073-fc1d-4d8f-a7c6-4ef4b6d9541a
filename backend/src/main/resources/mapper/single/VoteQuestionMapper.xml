<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.VoteQuestionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.VoteQuestionEntity">
        <id column="id" property="id" />
        <result column="vote_id" property="voteId" />
        <result column="question_no" property="questionNo" />
        <result column="uuid" property="uuid" />
        <result column="type" property="type" />
        <result column="title" property="title" />
        <result column="title_css" property="titleCss" />
        <result column="title_image" property="titleImage" />
        <result column="title_image2" property="titleImage2" />
        <result column="title_image3" property="titleImage3" />
        <result column="details" property="details" />
        <result column="display_order" property="displayOrder" />
        <result column="display" property="display" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, vote_id, question_no, uuid, type, title, title_css, title_image, title_image2, title_image3, details, display_order, display, created_at, updated_at, updated_by
    </sql>

</mapper>
