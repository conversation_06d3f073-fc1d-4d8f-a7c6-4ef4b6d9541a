<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.DonateSeatInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.DonateSeatInfoEntity">
        <id column="id" property="id" />
        <result column="donate_no" property="donateNo" />
        <result column="seat_id" property="seatId" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="strict" property="strict" />
        <result column="street" property="street" />
        <result column="adress" property="adress" />
        <result column="gps_lng" property="gpsLng" />
        <result column="gps_lat" property="gpsLat" />
        <result column="gps_lng_bmap" property="gpsLngBmap" />
        <result column="gps_lat_bmap" property="gpsLatBmap" />
        <result column="valid_time" property="validTime" />
        <result column="status" property="status" />
        <result column="special_type" property="specialType" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, donate_no, seat_id, province, city, strict, street, adress, gps_lng, gps_lat, gps_lng_bmap, gps_lat_bmap, valid_time, status, special_type, created_at, updated_at, updated_by, delete_flag
    </sql>

</mapper>
