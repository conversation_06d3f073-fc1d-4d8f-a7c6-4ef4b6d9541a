<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.SeatInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.SeatInfoEntity">
        <id column="id" property="id" />
        <result column="seat_no" property="seatNo" />
        <result column="name" property="name" />
        <result column="size" property="size" />
        <result column="material" property="material" />
        <result column="image" property="image" />
        <result column="introduce" property="introduce" />
        <result column="status" property="status" />
        <result column="price" property="price" />
        <result column="owner_unit" property="ownerUnit" />
        <result column="construct_unit" property="constructUnit" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="special_type" property="specialType" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, seat_no, name, size, material, image, introduce, status, price, owner_unit, construct_unit, start_time, end_time, special_type, created_at, updated_at, updated_by
    </sql>

</mapper>
