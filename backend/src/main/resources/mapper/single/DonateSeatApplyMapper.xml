<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.DonateSeatApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.DonateSeatApplyEntity">
        <id column="id" property="id" />
        <result column="donate_id" property="donateId" />
        <result column="open_id" property="openId" />
        <result column="share_num" property="shareNum" />
        <result column="real_num" property="realNum" />
        <result column="payed_price" property="payedPrice" />
        <result column="predict_price" property="predictPrice" />
        <result column="name" property="name" />
        <result column="id_card_no" property="idCardNo" />
        <result column="sex" property="sex" />
        <result column="age" property="age" />
        <result column="tel" property="tel" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="strict" property="strict" />
        <result column="street" property="street" />
        <result column="type" property="type" />
        <result column="person_certificate_image" property="personCertificateImage" />
        <result column="aptitude_certificate_image" property="aptitudeCertificateImage" />
        <result column="channel" property="channel" />
        <result column="reason" property="reason" />
        <result column="donation_word" property="donationWord" />
        <result column="donation_word_flag" property="donationWordFlag" />
        <result column="status" property="status" />
        <result column="apply_time" property="applyTime" />
        <result column="apply_pass_time" property="applyPassTime" />
        <result column="special_type" property="specialType" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="code" property="code" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, donate_id, open_id, share_num, real_num, payed_price, predict_price, name, id_card_no, sex, age, tel, province, city, strict, street, type, person_certificate_image, aptitude_certificate_image, channel, reason, donation_word, donation_word_flag, status, apply_time, apply_pass_time, special_type, created_at, updated_at, updated_by, code, delete_flag
    </sql>

</mapper>
