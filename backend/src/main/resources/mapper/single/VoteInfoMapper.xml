<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.VoteInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.VoteInfoEntity">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="title_image" property="titleImage" />
        <result column="status" property="status" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="introduce" property="introduce" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, title_image, status, start_time, end_time, introduce, created_at, updated_at, updated_by
    </sql>

</mapper>
