<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.UpkeepAssignMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.UpkeepAssignEntity">
        <id column="id" property="id" />
        <result column="adopt_no" property="adoptNo" />
        <result column="donate_no" property="donateNo" />
        <result column="seat_type" property="seatType" />
        <result column="seat_id" property="seatId" />
        <result column="upkeep_user_id" property="upkeepUserId" />
        <result column="upkeep_date_begin" property="upkeepDateBegin" />
        <result column="upkeep_date_end" property="upkeepDateEnd" />
        <result column="status" property="status" />
        <result column="special_type" property="specialType" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, adopt_no, donate_no, seat_type, seat_id, upkeep_user_id, upkeep_date_begin, upkeep_date_end, status, special_type, created_at, updated_at, updated_by
    </sql>

</mapper>
