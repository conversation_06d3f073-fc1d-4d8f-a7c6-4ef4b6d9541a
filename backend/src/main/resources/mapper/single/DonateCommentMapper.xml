<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.DonateCommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.DonateCommentEntity">
        <id column="id" property="id" />
        <result column="donate_id" property="donateId" />
        <result column="comment" property="comment" />
        <result column="open_id" property="openId" />
        <result column="name" property="name" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, donate_id, comment, open_id, name, status, created_at, updated_at, updated_by
    </sql>

</mapper>
