<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.AdoptSeatApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.AdoptSeatApplyEntity">
        <id column="id" property="id" />
        <result column="adopt_id" property="adoptId" />
        <result column="seat_id" property="seatId" />
        <result column="open_id" property="openId" />
        <result column="name" property="name" />
        <result column="id_card_no" property="idCardNo" />
        <result column="sex" property="sex" />
        <result column="age" property="age" />
        <result column="tel" property="tel" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="strict" property="strict" />
        <result column="street" property="street" />
        <result column="code" property="code" />
        <result column="reason" property="reason" />
        <result column="channel" property="channel" />
        <result column="donation_word" property="donationWord" />
        <result column="donation_word_flag" property="donationWordFlag" />
        <result column="valid_time" property="validTime" />
        <result column="apply_time" property="applyTime" />
        <result column="apply_pass_time" property="applyPassTime" />
        <result column="status" property="status" />
        <result column="special_type" property="specialType" />
        <result column="upkeep_pass" property="upkeepPass" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, adopt_id, seat_id, open_id, name, id_card_no, sex, age, tel, province, city, strict, street, code, reason, channel, donation_word, donation_word_flag, valid_time, apply_time, apply_pass_time, status, special_type, upkeep_pass, created_at, updated_at, updated_by, delete_flag
    </sql>

</mapper>
