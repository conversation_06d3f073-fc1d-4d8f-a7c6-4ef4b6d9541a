<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.SysAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.SysAreaEntity">
        <id column="id" property="id" />
        <result column="id_province" property="idProvince" />
        <result column="name_province" property="nameProvince" />
        <result column="id_city" property="idCity" />
        <result column="name_city" property="nameCity" />
        <result column="id_district" property="idDistrict" />
        <result column="name_district" property="nameDistrict" />
        <result column="id_street" property="idStreet" />
        <result column="name_street" property="nameStreet" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, id_province, name_province, id_city, name_city, id_district, name_district, id_street, name_street, created_at, updated_at, updated_by
    </sql>

</mapper>
