<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.SysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.SysUserEntity">
        <id column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="password" property="password" />
        <result column="nick_name" property="nickName" />
        <result column="remark" property="remark" />
        <result column="login_time" property="loginTime" />
        <result column="login_ip" property="loginIp" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, user_name, password, nick_name, remark, login_time, login_ip, created_at, updated_at, updated_by
    </sql>

</mapper>
