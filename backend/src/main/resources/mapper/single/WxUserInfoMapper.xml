<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.WxUserInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.WxUserInfoEntity">
        <id column="id" property="id" />
        <result column="open_id" property="openId" />
        <result column="name" property="name" />
        <result column="sex" property="sex" />
        <result column="age" property="age" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="tel" property="tel" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="strict" property="strict" />
        <result column="street" property="street" />
        <result column="send_flg" property="sendFlg" />
        <result column="wx_status" property="wxStatus" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, open_id, name, sex, age, avatar_url, tel, province, city, strict, street, send_flg, wx_status, updated_by, updated_at, created_at
    </sql>

</mapper>
