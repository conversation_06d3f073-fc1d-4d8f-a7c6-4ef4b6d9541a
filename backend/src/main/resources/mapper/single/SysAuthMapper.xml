<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.SysAuthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.SysAuthEntity">
        <id column="id" property="id" />
        <result column="auth_code" property="authCode" />
        <result column="auth_name" property="authName" />
        <result column="parent_id" property="parentId" />
        <result column="show_sequence" property="showSequence" />
        <result column="group_key" property="groupKey" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, auth_code, auth_name, parent_id, show_sequence, group_key, created_at, updated_at, updated_by
    </sql>

</mapper>
