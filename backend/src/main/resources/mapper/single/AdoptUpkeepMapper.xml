<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.AdoptUpkeepMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.AdoptUpkeepEntity">
        <id column="id" property="id" />
        <result column="upkeep_id" property="upkeepId" />
        <result column="adopt_id" property="adoptId" />
        <result column="donate_id" property="donateId" />
        <result column="seat_type" property="seatType" />
        <result column="seat_id" property="seatId" />
        <result column="open_id" property="openId" />
        <result column="upkeep_open_id" property="upkeepOpenId" />
        <result column="upkeep_name" property="upkeepName" />
        <result column="upkeep_company" property="upkeepCompany" />
        <result column="upkeep_time" property="upkeepTime" />
        <result column="upkeep_image_before" property="upkeepImageBefore" />
        <result column="upkeep_image_after" property="upkeepImageAfter" />
        <result column="memo" property="memo" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="strict" property="strict" />
        <result column="street" property="street" />
        <result column="adress" property="adress" />
        <result column="gps_lng" property="gpsLng" />
        <result column="gps_lat" property="gpsLat" />
        <result column="gps_lng_bmap" property="gpsLngBmap" />
        <result column="gps_lat_bmap" property="gpsLatBmap" />
        <result column="special_type" property="specialType" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, upkeep_id, adopt_id, donate_id, seat_type, seat_id, open_id, upkeep_open_id, upkeep_name, upkeep_company, upkeep_time, upkeep_image_before, upkeep_image_after, memo, province, city, strict, street, adress, gps_lng, gps_lat, gps_lng_bmap, gps_lat_bmap, special_type, created_at, updated_at, updated_by
    </sql>

</mapper>
