<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.SysRoleEntity">
        <id column="id" property="id" />
        <result column="role_name" property="roleName" />
        <result column="ident_key" property="identKey" />
        <result column="role_del_flg" property="roleDelFlg" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_name, ident_key, role_del_flg, created_at, updated_at, updated_by
    </sql>

</mapper>
