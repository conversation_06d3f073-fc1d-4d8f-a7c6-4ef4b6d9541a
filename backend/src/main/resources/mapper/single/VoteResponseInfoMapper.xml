<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.xzxx.seats.repository.mapper.VoteResponseInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.xzxx.seats.repository.entity.VoteResponseInfoEntity">
        <id column="id" property="id" />
        <result column="vote_id" property="voteId" />
        <result column="open_id" property="openId" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="value_flg" property="valueFlg" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, vote_id, open_id, start_time, end_time, value_flg, created_at, updated_at, updated_by
    </sql>

</mapper>
