spring.profiles.active=@profiles.active@

build.showinfo=false
build.version=@project.version@
build.timestamp=@maven.build.timestamp@

# for datasource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connectionTestQuery=SELECT 1

# for spring exception
spring.mvc.throw-exception-if-no-handler-found=true
spring.resources.add-mappings=false

# for test auto recreate table
spring.datasource.initialization-mode=never
spring.jpa.open-in-view=false


spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=60MB

# message
spring.messages.basename=i18n/messages

# session timeout (600m)
server.servlet.session.timeout=36000s
server.servlet.contextPath=/rest
# server port
server.port=30208

server.tomcat.max-http-post-size=5MB
server.max-http-header-size=64KB

# swagger
spring.fox.enable=false
excel.template.path=classpath:templates
# page size
paging.size=10
