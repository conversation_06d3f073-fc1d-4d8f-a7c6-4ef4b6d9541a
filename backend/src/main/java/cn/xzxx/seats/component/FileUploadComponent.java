package cn.xzxx.seats.component;

import cn.xzxx.seats.common.base.UploadFiles.RawFile;
import cn.xzxx.seats.common.exception.SystemException;
import cn.xzxx.seats.common.utils.StringUtil;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;

import cn.xzxx.seats.utils.ImageUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
@Log4j2
public class FileUploadComponent {

  @Value("${file.upload.base-path}")
  private String basePath;

  @Value("${file.upload.base-url}")
  private String baseURL;

  public String add(RawFile file) {

    return add(file.getRaw());
  }

  public String add(MultipartFile file) {
    String image = StringUtil.getId();
    copy(file, new File(basePath, image));
    return image;
  }

  public void delete(String image) {
    if(StringUtils.isEmpty(image)) {
      return;
    }
    try {
      Files.delete(new File(basePath, image).toPath());
    } catch (IOException e) {
      log.error(e.getMessage(), e);
    }
  }

  public void delete(RawFile file) {
    delete(file.getFileId());
  }

  private void copy(MultipartFile file, File dest) {

    File fileCompress = null;
    try {

      if (!dest.getParentFile().exists()) {
        dest.getParentFile().mkdirs();
      }

      // 图片压缩
      fileCompress=File.createTempFile("tmp", null);
      file.transferTo(fileCompress);
      ImageUtils.compressPicForScale(fileCompress, 300, 0.8,800,600);
      InputStream is = new FileInputStream(fileCompress);

      // 文件拷贝
      Files.copy(is, dest.toPath());

    } catch (IOException e) {
      throw new SystemException(e, "文件保存失败。");
    } finally {
      if(fileCompress != null && fileCompress.exists()) {
        fileCompress.delete();
      }
    }
  }

  public String getImageUrl(String image) {
    if (StringUtils.isEmpty(image)) {
      return null;
    }
    return StringUtil.concatUrl(baseURL, image);
  }
}
