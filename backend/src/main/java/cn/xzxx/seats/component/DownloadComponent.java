package cn.xzxx.seats.component;

import cn.xzxx.seats.common.message.MessageConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Component
public class DownloadComponent {

  private static final Logger LOGGER = LoggerFactory.getLogger(DownloadComponent.class);

  @Autowired
  ExcelCreateComponent excelCreateComponent;

  public BaseResponse exportExcel(HttpServletResponse response, List<?> dataList, String template,
    String fileName) {
    Map<String, Object> dataMap = new HashMap<>();
    dataMap.put("dataList", dataList);
    return exportExcel(response, dataMap, template, fileName);
  }

  public BaseResponse exportExcel(HttpServletResponse response, Map<String, Object> dataMap,
    String template, String fileName) {
    try {
      String name = getExcelName(fileName);

      dataMap.put("date", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
      try (ServletOutputStream os = response.getOutputStream()) {
        response
          .setContentType(String.format("application/x-msdownload;filename=%s", URLEncoder.encode(name, "UTF-8")));

        excelCreateComponent.exportExcel(template, os, dataMap);
        response.setHeader("Content-Length", String.valueOf(response.getBufferSize()));
        os.flush();
      }
    } catch (Exception e) {
      LOGGER.error("Excel做成失败", e);
      return MessageResponse.newInstance().addMessage(MessageConstants.MESSAGE_E0081);
    }
    return null;
  }

  public BaseResponse exportExcelDynamicGrid(HttpServletResponse response,
    String template,
    String fileName,
    Map<String, Object> dataMap,
    List<String> headers,
    List<String> objectProps,
    List<Object> data,
    List<MergeCells> mergeCells) {
    try {
      String name = getExcelName(fileName);

      dataMap.put("date", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
      try (ServletOutputStream os = response.getOutputStream()) {
        String suffix = data.size() <= 2000 ? ".xlsx" : ".zip";
        response.setContentType(
          String.format("application/x-msdownload;filename=%s", URLEncoder.encode(name + suffix, "UTF-8")));

        if (data.size() <= 2000) {
          excelCreateComponent.exportExcelDynamicGrid(template, os, dataMap, headers, objectProps, data, mergeCells);
        } else {
          Object title = dataMap.get("questionnaireTitle");
          try (ZipOutputStream zipos = new ZipOutputStream(new BufferedOutputStream(os))) {
            zipos.setMethod(ZipOutputStream.DEFLATED);
            List<List<Object>> zipFileDataList = getZipFileDataList(data);
            for (int i = 0; i < zipFileDataList.size(); i++) {
              zipos.putNextEntry(new ZipEntry(fileName + "_" + (i + 1) + ".xlsx"));
              dataMap.put("questionnaireTitle", title + "_" + (i + 1) + "_");
              try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                excelCreateComponent
                  .exportExcelDynamicGrid(template, baos, dataMap, headers, objectProps, zipFileDataList.get(i),
                    mergeCells);
                baos.flush();
                zipos.write(baos.toByteArray());
                zipos.closeEntry();
              }
            }
          }
        }
        response.setHeader("Content-Length", String.valueOf(response.getBufferSize()));
        os.flush();
      }
    } catch (Exception e) {
      LOGGER.error("Excel做成失败", e);
      return MessageResponse.newInstance().addMessage(MessageConstants.MESSAGE_E0081);
    }
    return null;
  }

  private List<List<Object>> getZipFileDataList(List<Object> data) {
    List<List<Object>> zipFileList = new ArrayList<>();
    for (int i = 0; i < data.size(); i += 2000) {
      List<Object> dataList = data.subList(i, Math.min(data.size(), i + 2000));
      zipFileList.add(dataList);
    }
    return zipFileList;
  }

  @NotNull
  private String getExcelName(String fileName) {
    String date = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
//        String suffix = ".xlsx";
//        String name;
//        if (!fileName.endsWith(suffix)) {
//            name = fileName + "_" + date + suffix;
//        } else {
//            name = fileName.replace(suffix, "_") + date + suffix;
//        }
    return fileName + "_" + date;
  }
}
