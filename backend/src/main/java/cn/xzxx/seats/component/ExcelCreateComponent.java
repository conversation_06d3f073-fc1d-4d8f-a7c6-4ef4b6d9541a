package cn.xzxx.seats.component;

import cn.xzxx.seats.common.exception.SystemException;
import cn.xzxx.seats.common.utils.ListUtils;
import com.google.common.base.Joiner;
import org.jxls.area.Area;
import org.jxls.command.GridCommand;
import org.jxls.common.CellRef;
import org.jxls.common.Context;
import org.jxls.transform.Transformer;
import org.jxls.util.JxlsHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;

import java.io.*;
import java.util.List;
import java.util.Map;

@Component
public class ExcelCreateComponent {

  @Value("${excel.template.path}")
  private String templatePath;

  public void exportExcel(String templateName, OutputStream os, Map<String, Object> model)
    throws IOException {
    try (InputStream fileInputStream = getTemplate(templateName)) {
      exportExcel(fileInputStream, os, model);
    }
  }

  public void exportExcelDynamicGrid(String templateName, OutputStream os, Map<String, Object> model,
    List<String> headers, List<String> objectProps, List<Object> data, List<MergeCells> mergeCells)
    throws IOException {
    try (InputStream is = getTemplate(templateName)) {
      Context context = new Context();
      context.putVar("headers", headers);
      context.putVar("data", data);
      if (model != null) {
        for (Map.Entry<String, Object> entry : model.entrySet()) {
          context.putVar(entry.getKey(), entry.getValue());
        }
      }
      JxlsHelper jxlsHelper = JxlsHelper.getInstance();
      processGridTemplate(jxlsHelper, is, os, context, Joiner.on(",").join(objectProps), mergeCells);
    }
  }

  private void processGridTemplate(JxlsHelper jxlsHelper, InputStream is, OutputStream os, Context context,
    String objectProps, List<MergeCells> mergeCells) throws IOException {
    Transformer transformer = jxlsHelper.createTransformer(is, os);
    jxlsHelper.getAreaBuilder().setTransformer(transformer);
    List<Area> xlsAreaList = jxlsHelper.getAreaBuilder().build();
    for (Area xlsArea : xlsAreaList) {
      GridCommand gridCommand = (GridCommand) xlsArea.getCommandDataList().get(0).getCommand();
      gridCommand.setProps(objectProps);
      xlsArea.applyAt(new CellRef(xlsArea.getStartCellRef().getCellName()), context);
    }
    if (ListUtils.isNotEmpty(mergeCells)) {
      mergeCells
        .forEach(c -> transformer.mergeCells(new CellRef("Sheet1", c.getX(), c.getY()), c.getRows(), c.getCols()));
    }
    transformer.write();
  }

  private void exportExcel(InputStream is, OutputStream os, Map<String, Object> model)
    throws IOException {
    Context context = new Context();
    if (model != null) {
      for (Map.Entry<String, Object> entry : model.entrySet()) {
        context.putVar(entry.getKey(), entry.getValue());
      }
    }
    JxlsHelper jxlsHelper = JxlsHelper.getInstance();
    Transformer transformer = jxlsHelper.createTransformer(is, os);
    // JexlExpressionEvaluator evaluator = (JexlExpressionEvaluator)
    // transformer.getTransformationConfig().getExpressionEvaluator();
    // Map<String, Object> funcs = new HashMap<>();
    // funcs.put("utils", new JxlsUtils());
    // evaluator.getJexlEngine().setFunctions(funcs);
    jxlsHelper.processTemplate(context, transformer);
  }

  private InputStream getTemplate(String name) {
    if (!name.endsWith(".xlsx")) {
      name = name + ".xlsx";
    }
    try {
      if (templatePath.startsWith(ResourceUtils.CLASSPATH_URL_PREFIX)) {
        String path = templatePath.substring(ResourceUtils.CLASSPATH_URL_PREFIX.length());
        ClassPathResource resource = new ClassPathResource(path(path, name));
        return resource.getInputStream();
      } else if (templatePath.startsWith(ResourceUtils.FILE_URL_PREFIX)) {
        String path = templatePath.substring(ResourceUtils.FILE_URL_PREFIX.length());
        File file = new File(path(path, name));
        return new FileInputStream(file);
      } else {
        throw new SystemException(null, "property [excel.template.path] = [%s]】 配置有误， 必须[classpath:]或者[file:]开头",
          templatePath);
      }
    } catch (IOException e) {
      throw new SystemException(e, "property [excel.template.path] = [%s]】 配置有误", templatePath);
    }
  }

  private String path(String path, String name) {
    if (path.endsWith("/")) {
      return path + name;
    } else {
      return path + "/" + name;
    }
  }
}
