package cn.xzxx.seats.component;

import cn.xzxx.seats.code.*;
import cn.xzxx.seats.common.response.PulldownItem;
import cn.xzxx.seats.repository.entity.SysAreaEntity;
import cn.xzxx.seats.repository.mapper.SysAreaMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PulldownComponent {

  @Autowired
  private SysAreaMapper sysAreaMapper;

  public List<PulldownItem> voteStatusPulldown() {
    List<PulldownItem> pulldown = new ArrayList<>();
    for (VoteStatus status : VoteStatus.values()) {
      if (!VoteStatus.DELETED.is(status.status())) {
        pulldown.add(new PulldownItem(status.statusName(), String.valueOf(status.status())));
      }
    }
    return pulldown;
  }

  public List<PulldownItem> getAreaPulldown() {
    List<PulldownItem> rolePulldown = new ArrayList<>();
    List<SysAreaEntity> sysAreaEntities = sysAreaMapper.selectList(
      new QueryWrapper<>()/*.ne("name_street", "不限")*/);
    sysAreaEntities.stream().collect(Collectors.groupingBy(SysAreaEntity::getIdProvince)).forEach((k1, v1) -> {
      PulldownItem pulldownItem1 = new PulldownItem(v1.get(0).getNameProvince(), v1.get(0).getNameProvince());
      rolePulldown.add(pulldownItem1);
      v1.stream().collect(Collectors.groupingBy(SysAreaEntity::getIdCity)).forEach((k2, v2) -> {
        PulldownItem pulldownItem2 = new PulldownItem(v2.get(0).getNameCity(), v2.get(0).getNameCity());
        pulldownItem1.getOrCreateChildren().add(pulldownItem2);
        v2.stream().filter(e -> StringUtils.isNotEmpty(e.getIdDistrict())).collect(Collectors.groupingBy(SysAreaEntity::getIdDistrict)).forEach((k3, v3) -> {
          PulldownItem pulldownItem3 = new PulldownItem(v3.get(0).getNameDistrict(), v3.get(0).getNameDistrict());
          pulldownItem2.getOrCreateChildren().add(pulldownItem3);
          v3.stream().filter(e -> StringUtils.isNotEmpty(e.getIdStreet())).forEach(v ->
            pulldownItem3.getOrCreateChildren().add(new PulldownItem(v.getNameStreet(), v.getNameStreet())));
        });
      });
    });

    sort(rolePulldown);
    return rolePulldown;
  }

  private void sort(List<PulldownItem> pulldownList) {
    if (pulldownList != null) {
      pulldownList.sort(Comparator.comparing(e -> String.valueOf(e.getValue())));
      for (PulldownItem pulldown : pulldownList) {
        sort(pulldown.getChildren());
      }
    }
  }

  public List<PulldownItem> getDonateStatusPulldown() {
    List<PulldownItem> pulldownItems = new ArrayList<>();
    for (DonateStatus s : DonateStatus.values()) {
      pulldownItems.add(new PulldownItem(s.statusName(), String.valueOf(s.status())));
    }

    return pulldownItems;
  }

  public List<PulldownItem> getDonateApplyStatusPulldown() {
    List<PulldownItem> pulldownItems = new ArrayList<>();
    for (DonateApplyStatus s : DonateApplyStatus.values()) {
      pulldownItems.add(new PulldownItem(s.statusName(), String.valueOf(s.status())));
    }

    return pulldownItems;
  }

  public List<PulldownItem> getAdoptApplyStatusPulldown() {
    List<PulldownItem> pulldownItems = new ArrayList<>();
    for (AdoptApplyStatus s : AdoptApplyStatus.values()) {
      pulldownItems.add(new PulldownItem(s.statusName(), String.valueOf(s.status())));
    }

    return pulldownItems;
  }

  public List<PulldownItem> getAnswerStatusPulldown() {
    List<PulldownItem> pulldownItems = new ArrayList<>();
    for (ResponseStatus s : ResponseStatus.values()) {
      pulldownItems.add(new PulldownItem(s.label(), String.valueOf(s.status())));
    }

    return pulldownItems;
  }

  public List<PulldownItem> getAreaTypeStatusPulldown() {
    List<PulldownItem> pulldownItems = new ArrayList<>();
    for (AreaTypeStatus s : AreaTypeStatus.values()) {
      pulldownItems.add(new PulldownItem(s.statusName(), String.valueOf(s.status())));
    }

    return pulldownItems;
  }

  public List<PulldownItem> getSortTypeDonateStatusPulldown() {
    List<PulldownItem> pulldownItems = new ArrayList<>();
    for (SortTypeDonateStatus s : SortTypeDonateStatus.values()) {
      pulldownItems.add(new PulldownItem(s.statusName(), String.valueOf(s.status())));
    }

    return pulldownItems;
  }

  public List<PulldownItem> getSortTypeAdoptStatusPulldown() {
    List<PulldownItem> pulldownItems = new ArrayList<>();
    for (SortTypeAdoptStatus s : SortTypeAdoptStatus.values()) {
      pulldownItems.add(new PulldownItem(s.statusName(), String.valueOf(s.status())));
    }

    return pulldownItems;
  }

  public List<PulldownItem> getDonateCommentStatusPulldown() {
    List<PulldownItem> pulldownItems = new ArrayList<>();
    for (DonateCommentStatus s : DonateCommentStatus.values()) {
      pulldownItems.add(new PulldownItem(s.statusName(), String.valueOf(s.status())));
    }

    return pulldownItems;
  }

  public List<PulldownItem> getBroadcastStatusPulldown() {
    List<PulldownItem> pulldownItems = new ArrayList<>();
    for (BroadcastStatus s : BroadcastStatus.values()) {
      pulldownItems.add(new PulldownItem(s.statusName(), String.valueOf(s.status())));
    }
    return pulldownItems;
  }

  public List<PulldownItem> getUpkeepAssignStatusPulldown() {
    List<PulldownItem> pulldownItems = new ArrayList<>();
    for (UpkeepAssignStatus s : UpkeepAssignStatus.values()) {
      pulldownItems.add(new PulldownItem(s.statusName(), String.valueOf(s.status())));
    }

    return pulldownItems;
  }

  public List<PulldownItem> getDonateApplyTypePulldown() {
    List<PulldownItem> pulldownItems = new ArrayList<>();
    for (DonateApplyTypeStatus s : DonateApplyTypeStatus.values()) {
      pulldownItems.add(new PulldownItem(s.statusName(), String.valueOf(s.status())));
    }

    return pulldownItems;
  }
}
