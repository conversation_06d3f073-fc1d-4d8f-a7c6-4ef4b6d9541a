package cn.xzxx.seats.wechat.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VoteQuestionOptionsVo {

    /**
     * 投票选项ID
     */
    private Integer voteId;

    /**
     * 投票问题ID
     */
    private Integer questionId;

    /**
     * 投票选择项数 0，1，2
     */
    private Integer answerId;

    /**
     * 投票选项内容
     */
    private String title;

    /**
     * 投票选项图片
     */
    private String titleImage;

    /**
     * 当前投票数目
     */
    private Long voteNum;

    /**
     * 是否历史投票
     */
    private Boolean historyVoteFlag;
}
