package cn.xzxx.seats.wechat.vo;

import lombok.Data;

/**
 * 捐赠用户信息
 */
@Data
public class DonateUserVo {

    /**
     * 捐赠申请Id
     */
    private Integer donateApplyId;

    /**
     * 微信openId
     */
    private String openId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 捐赠份额（1-100）
     */
    private Integer shareNum;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 了解渠道
     */
    private String channel;

    /**
     * 认养公共座椅原因
     */
    private String reason;

    /**
     * 寄语
     */
    private String donationWord;

    /**
     * 寄语是否显示 0-否 1-是
     */
    private Boolean donationWordFlag;

    /**
     * 捐赠状态
     */
    private Integer status;
}
