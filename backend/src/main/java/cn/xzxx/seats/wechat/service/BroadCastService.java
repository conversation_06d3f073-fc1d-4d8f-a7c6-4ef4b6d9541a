package cn.xzxx.seats.wechat.service;

import cn.xzxx.seats.code.BroadcastStatus;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.repository.entity.BroadcastSettingsEntity;
import cn.xzxx.seats.repository.mapper.BroadcastSettingsMapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class BroadCastService {

    private final BroadcastSettingsMapper broadcastSettingsMapper;

    public BaseResponse getBroadcastSettings() {
        List<BroadcastSettingsEntity> list = new LambdaQueryChainWrapper<>(broadcastSettingsMapper)
                .eq(BroadcastSettingsEntity::getStatus, BroadcastStatus.Broad_OPEN.status())
                .eq(BroadcastSettingsEntity::getDeleteFlag, Boolean.FALSE)
                .list();
        return DataResponse.success(list);
    }
}
