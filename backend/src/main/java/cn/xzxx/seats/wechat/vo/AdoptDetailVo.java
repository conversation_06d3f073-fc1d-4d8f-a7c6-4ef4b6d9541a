package cn.xzxx.seats.wechat.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdoptDetailVo {

    /**
     * 认养座椅ID
     */
    private Integer adoptId;

    /**
     * 认养ID
     */
    private Integer adoptApplyId;

    /**
     * 可认养座椅ID
     */
    private Integer seatId;

    /**
     * 认养人open_id
     */
    private String openId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 区划代码
     */
    private String code;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * GPS经度
     */
    private Double gpsLng;

    /**
     * GPS纬度
     */
    private Double gpsLat;

    /**
     * 认养公共座椅原因
     */
    private String reason;

    /**
     * 了解渠道 1:
     */
    private String channel;

    /**
     * 认养寄语
     */
    private String donationWord;

    /**
     * 认养有效期（默认一年）
     */
    private String validTime;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 申请通过时间
     */
    private String applyPassTime;

    /**
     * 认养状态 1:申请中 2:审核通过 3:已过期
     */
    private Integer status;

    /**
     * 椅子缩略图
     */
    private String image;

    /**
     * 椅子详情图
     */
    private List<String> imageDetail;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

    /**
     * 座椅大小
     */
    private String seatSize;

    /**
     * 座椅材质
     */
    private String seatMaterial;

    /**
     * 座椅介绍
     */
    private String seatIntroduce;

    /**
     * 座椅状态 0:未发布 1:发布 2:下架
     */
    private Integer seatStatus;

    /**
     * 座椅价格 单位(分)
     */
    private Integer seatPrice;

    /**
     * 发布开始时间
     */
    private String startTime;

    /**
     * 发布结束时间
     */
    private String endTime;

    /**
     * 认养期限 1-一年 2-两年 3-三年
     */
    private Integer adoptTerm;

    /**
     * 特殊标识 0-全部 1-浦东新区专用
     */
    private Integer specialType;


}
