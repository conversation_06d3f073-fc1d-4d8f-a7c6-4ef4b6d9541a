package cn.xzxx.seats.wechat.controller;

import cn.xzxx.seats.common.message.MessageConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.config.ProjectConfig;
import cn.xzxx.seats.config.security.ResponseSupport;
import cn.xzxx.seats.utils.QRCodeUtil;
import cn.xzxx.seats.web.orcode.QrCodeService;
import cn.xzxx.seats.wechat.bo.UserSeatCareBo;
import cn.xzxx.seats.wechat.service.CertificateService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

@RestController
@RequestMapping("wechat")
@RequiredArgsConstructor
public class CertificateController {

    private final CertificateService certificateService;
    private final QrCodeService qrCodeService;

    /**
     * 获取公益名片
     * @param openId
     */
    @GetMapping("getPublicWelfareBusinessCard/{openId}")
    public BaseResponse getPublicWelfareBusinessCard(@PathVariable String openId) throws Exception {
        return certificateService.getPublicWelfareBusinessCard(openId);
    }

    /**
     * 获取我的证书
     * @param openId
     */
    @GetMapping("getCertificateSettings/{openId}")
    public BaseResponse getCertificateSettings(@PathVariable String openId) {
        return certificateService.getCertificateSettings(openId);
    }

    /**
     * 下载我的证书
     * @param id 证书id
     * @param response
     */
    @PostMapping("downloadCertificate/{id}")
    public void downloadCertificate(@PathVariable Long id,HttpServletResponse response) throws IOException {
        certificateService.downloadCertificate(id, response);
    }

    /**
     * 创建海报
     */
    @PostMapping("creatPoster")
    public BaseResponse creatPoster(@RequestBody @Validated UserSeatCareBo bo) {
        return certificateService.creatPoster(bo);
    }

    /**
     * create a new qrCode
     */
    @GetMapping(value = { "/qrCode/getBase64"})
    public BaseResponse qrCodeDownload() throws IOException {

        BufferedInputStream is = null;
        try {

            //获取接口调用凭证access_token
            String appId = ProjectConfig.APP_ID;//小程序id
            String appKey = ProjectConfig.APP_KEY;//小程序密钥
            String token = qrCodeService.postToken(appId, appKey);
            //生成二维码
            is = qrCodeService.generateQrCode( "pages/adopt/chairForYHList/chairForYHList",  "1", token);
            BufferedImage image = ImageIO.read(is);
            String res = QRCodeUtil.BufferedImageToBase64(image);
            return DataResponse.success(res);
        } catch (Exception e) {
            return DataResponse.fail(MessageConstants.MESSAGE_E0083);
        } finally {
            if (is != null) {
                is.close();
            }
        }
    }


}
