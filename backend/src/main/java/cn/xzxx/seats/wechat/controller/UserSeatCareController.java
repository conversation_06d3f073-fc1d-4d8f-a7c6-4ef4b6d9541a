package cn.xzxx.seats.wechat.controller;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.wechat.bo.UserSeatCareBo;
import cn.xzxx.seats.wechat.service.UserSeatCareService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.parameters.P;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("wechat")
@RequiredArgsConstructor
public class UserSeatCareController {

    private final UserSeatCareService userSeatCareService;

    /**
     * 获取用户关注的座椅
     * @param openId
     * @return
     */
    @GetMapping("getOwnerUserSeatCare/{openId}")
    public BaseResponse getUserSeatCare(@PathVariable String openId) {
        return userSeatCareService.getOwnerCare(openId);
    }

    /**
     * 收藏座椅
     */
    @PostMapping("collectSeat")
    public BaseResponse collectSeat(@RequestBody @Validated UserSeatCareBo bo) {
        return userSeatCareService.collectSeat(bo);
    }

    /**
     * 取消收藏座椅
     */
    @PostMapping("cancelCollectSeat")
    public BaseResponse cancelCollectSeat(@RequestBody UserSeatCareBo bo) {
        return userSeatCareService.cancelCollectSeat(bo);
    }

    /**
     * 查看当前座椅是否被收藏
     */
    @PostMapping("isCollectSeat")
    public BaseResponse isCollectSeat(@RequestBody @Validated UserSeatCareBo bo) {
        return userSeatCareService.isCollectSeat(bo);
    }

}
