package cn.xzxx.seats.wechat.service;

import cn.hutool.core.collection.CollUtil;
import cn.xzxx.seats.code.UserIdentityStatus;
import cn.xzxx.seats.common.constants.ICommonConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.common.utils.BeanCopyUtils;
import cn.xzxx.seats.common.utils.OkHttpUtils;
import cn.xzxx.seats.repository.entity.AdoptSeatApplyEntity;
import cn.xzxx.seats.repository.entity.AdoptUpkeepUserEntity;
import cn.xzxx.seats.repository.entity.DonateSeatApplyEntity;
import cn.xzxx.seats.repository.entity.WxUserInfoEntity;
import cn.xzxx.seats.repository.mapper.AdoptSeatApplyMapper;
import cn.xzxx.seats.repository.mapper.AdoptUpkeepUserMapper;
import cn.xzxx.seats.repository.mapper.DonateSeatApplyMapper;
import cn.xzxx.seats.repository.mapper.WxUserInfoMapper;
import cn.xzxx.seats.wechat.vo.WxUserInfoVo;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserService {

    private final WxUserInfoMapper wxUserInfoMapper;
    private final DonateSeatApplyMapper donateSeatApplyMapper;
    private final AdoptSeatApplyMapper adoptSeatApplyMapper;
    private final AdoptUpkeepUserMapper adoptUpkeepUserMapper;

    public BaseResponse getOpenId(String code) {
        try {
//            log.info("code:"+code);
            String res = OkHttpUtils.builder()
                    .url(ICommonConstants.WX_OAUTH2_URL)
                    .addParam("appid", ICommonConstants.WX_APPID)
                    .addParam("secret", ICommonConstants.WX_APPSECRET)
                    .addParam("js_code", code)
                    .addParam("grant_type", "authorization_code")
                    .get()
                    .sync();
//            log.info("res:"+res);
//            JSONObject jsonObject = JSONObject.parseObject(res);
//            String openId = jsonObject.getString("openid");
            return DataResponse.success(res);
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
            return MessageResponse.newInstance("获取openId失败!失败原因:"+e.getMessage());
        }
    }

    public BaseResponse addUserInfo(WxUserInfoEntity wxUserInfoEntity) {
        List<WxUserInfoEntity> list = new LambdaQueryChainWrapper<>(wxUserInfoMapper)
                .eq(WxUserInfoEntity::getOpenId, wxUserInfoEntity.getOpenId())
                .list();
        if (CollectionUtils.isNotEmpty(list)) {
            wxUserInfoEntity.setId(list.get(0).getId());
            wxUserInfoMapper.updateById(wxUserInfoEntity);
        } else {
            wxUserInfoMapper.insert(wxUserInfoEntity);
        }
        return new BaseResponse();
    }

    public BaseResponse getUserInfo(String openId) {
        List<WxUserInfoEntity> list = new LambdaQueryChainWrapper<>(wxUserInfoMapper)
                .eq(WxUserInfoEntity::getOpenId, openId)
                .list();
        if (CollUtil.isNotEmpty(list)) {
            WxUserInfoEntity wxUserInfoEntity = list.get(0);
            WxUserInfoVo vo = BeanCopyUtils.copy(wxUserInfoEntity, WxUserInfoVo.class);
            // 判断是否有共建或者领养的身份
            List<DonateSeatApplyEntity> donateSeatApplyEntityList = new LambdaQueryChainWrapper<>(donateSeatApplyMapper)
                    .eq(DonateSeatApplyEntity::getOpenId, openId)
                    .list();
            if (CollUtil.isNotEmpty(donateSeatApplyEntityList)) {
                vo.setIdentityStatus(UserIdentityStatus.DONATE_OR_ADOPTER);
            } else {
                List<AdoptSeatApplyEntity> adoptSeatApplyEntityList = new LambdaQueryChainWrapper<>(adoptSeatApplyMapper)
                        .eq(AdoptSeatApplyEntity::getOpenId, openId)
                        .list();
                if (CollUtil.isNotEmpty(adoptSeatApplyEntityList)) {
                    vo.setIdentityStatus(UserIdentityStatus.DONATE_OR_ADOPTER);
                } else {
                    // 判断是否是养护人员
                    List<AdoptUpkeepUserEntity> adoptUpkeepUserEntityList = new LambdaQueryChainWrapper<>(adoptUpkeepUserMapper)
                            .eq(AdoptUpkeepUserEntity::getOpenId, openId)
                            .list();
                    if (CollUtil.isNotEmpty(adoptUpkeepUserEntityList)) {
                        vo.setIdentityStatus(UserIdentityStatus.MAINTENANCE);
                    } else {
                        vo.setIdentityStatus(UserIdentityStatus.TOURIST);
                    }
                }
            }
            return DataResponse.success(vo);
        } else {
            return MessageResponse.newInstance("无用户信息");
        }
    }
}
