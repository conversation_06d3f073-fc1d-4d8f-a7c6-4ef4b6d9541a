package cn.xzxx.seats.wechat.bo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class CheckAdoptUpkeepBo {

    /**
     * 座椅分类 1-共建 2-认养
     */
    @NotNull(message = "座椅分类不能为空")
    private Integer seatType;

    /**
     * 捐赠/认养座椅ID
     */
    @NotNull(message = "座椅ID不能为空")
    private Integer seatId;

    /**
     * 手机号
     */
    @NotEmpty(message = "手机号")
    private String tel;

    /**
     * 特殊标识 0-全部 1-浦东新区专用
     */
    private Integer specialType;
}
