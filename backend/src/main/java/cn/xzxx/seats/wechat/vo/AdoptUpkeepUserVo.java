package cn.xzxx.seats.wechat.vo;

import lombok.Data;

import java.util.List;

@Data
public class AdoptUpkeepUserVo {

    /**
     * 是否养护人
     */
    private Boolean isUpkeepUser;

    /**
     * 是否可以被养护
     */
    private Boolean isAdoptUpkeep;

    /**
     * 养护人人员ID
     */
    private Integer id;

    /**
     * 手机号
     */
    private Integer telephone;

    /**
     * 养护人open_id
     */
    private String openId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 单位
     */
    private String company;

    /**
     * 认养座椅id
     */
    private Integer adoptId;

    /**
     * 养护记录
     */
    private List<AdoptUpkeepVo> adoptUpkeepVoList;
}
