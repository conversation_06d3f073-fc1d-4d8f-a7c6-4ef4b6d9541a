package cn.xzxx.seats.wechat.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DonateInfoVo {
    /**
     * 可捐赠座椅ID
     */
    private Integer id;

    /**
     * 座椅ID
     */
    private Integer seatId;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * 座椅大小
     */
    private String size;

    /**
     * 座椅材质
     */
    private String material;

    /**
     * 座椅介绍
     */
    private String introduce;

    /**
     * 座椅价格 单位(元)
     */
    private Integer price;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

    /**
     * 发布开始时间
     */
    private String startTime;

    /**
     * 区划代码
     */
    private String code;

    /**
     * GPS经度
     */
    private Double gpsLng;

    /**
     * GPS维度
     */
    private Double gpsLat;

    /**
     * 认捐截止时间（到达截止时间 且 认捐份额>=100 ）
     */
    private String validTime;

    /**
     * 捐赠状态 0:已取消 1:未发布 2:已发布 3:捐赠中 4:捐赠完成 5:建造中 6:建造完成
     */
    private Integer status;

    /**
     * 捐赠份额
     */
    private Integer shareNum;

    /**
     * 图片缩略图
     */
    private String image;

    /**
     * 图片详细图
     */
    private List<String> imageDetail;

    /**
     * 捐赠人信息
     */
    private List<DonateUserVo> userVoList;

    /**
     * 养护人信息
     */
    private List<AdoptMaintainerVo> adoptMaintainerVoList;

    /**
     * 建造单位
     */
    private String ownerUnit;

    /**
     * 施工单位
     */
    private String constructUnit;

    /**
     * 是否本人共建
     */
    private Boolean ownerFlag;


}
