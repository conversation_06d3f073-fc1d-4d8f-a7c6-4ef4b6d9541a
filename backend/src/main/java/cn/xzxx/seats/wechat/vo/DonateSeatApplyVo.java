package cn.xzxx.seats.wechat.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class DonateSeatApplyVo {

    /**
     * 捐赠ID
     */
    private Integer id;

    /**
     * 可捐赠座椅ID
     */
    @NotNull
    private Integer donateId;

    /**
     * 座椅id
     */
    private Integer seatId;

    /**
     * 捐赠人open_id
     */
    @NotEmpty
    private String openId;

    /**
     * 捐赠份额（1-100）
     */
    @NotNull
    private Integer shareNum;

    /**
     * 预估金额 用于记录用户填写数据
     */
    private String predictPrice;

    /**
     * 实际份额（1-100）
     */
    private Integer realNum;

    /**
     * 姓名
     */
    @NotEmpty
    private String name;

    /**
     * 身份证号
     */
    @NotEmpty
    private String idCardNo;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * GPS经度
     */
    private Double gpsLng;

    /**
     * GPS维度
     */
    private Double gpsLat;

    /**
     * GPS经度-baidu
     */
    private Double gpsLngBmap;

    /**
     * GPS维度-baidu
     */
    private Double gpsLatBmap;

    /**
     * 区划代码
     */
    private String code;

    /**
     * 捐赠属性 1:街道 2:个人
     */
    @NotNull
    private Integer type;

    /**
     * 身份证明图片URL
     */
    private String personCertificateImage;

    /**
     * 资质证明图片URL
     */
    private String aptitudeCertificateImage;

    /**
     * 了解渠道 1:
     */
    private String channel;

    /**
     * 捐赠公共座椅原因
     */
    private String reason;

    /**
     * 寄语
     */
    private String donationWord;

    /**
     * 捐赠状态 -1:不显示(自行创建) 0:已取消 1:未发布 2:已发布 3:捐赠中 4:捐赠完成 5:建造中 6:建造完成
     */
    private Integer status;

    /**
     * 座椅捐赠状态 -1:不显示 1:申请中 2:捐赠审核通过 3:不通过 4:无效 5:份额确认中 6:份额已确认
     */
    private Integer donateStatus;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 申请通过时间
     */
    private String applyPassTime;

    /**
     * 缩略图
     */
    private String image;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

    /**
     * 座椅大小
     */
    private String size;

    /**
     * 座椅材质
     */
    private String material;

    /**
     * 座椅介绍
     */
    private String introduce;

    /**
     * 座椅价格
     */
    private Integer price;

    /**
     * 详细图
     */
    private List<String> imageDetail;


}
