package cn.xzxx.seats.wechat.service;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.common.utils.BeanCopierUtil;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.repository.entity.AdoptSeatInfoEntity;
import cn.xzxx.seats.repository.entity.DonateSeatInfoEntity;
import cn.xzxx.seats.repository.entity.SeatImageEntity;
import cn.xzxx.seats.repository.entity.SeatInfoEntity;
import cn.xzxx.seats.repository.mapper.AdoptSeatInfoMapper;
import cn.xzxx.seats.repository.mapper.DonateSeatInfoMapper;
import cn.xzxx.seats.repository.mapper.SeatImageMapper;
import cn.xzxx.seats.repository.mapper.SeatInfoMapper;
import cn.xzxx.seats.wechat.vo.SeatInfoVo;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SeatService {

    @Resource
    private SeatInfoMapper seatInfoMapper;

    @Resource
    private SeatImageMapper seatImageMapper;

    @Resource
    private DonateSeatInfoMapper donateSeatInfoMapper;

    @Resource
    private AdoptSeatInfoMapper adoptSeatInfoMapper;

    @Resource
    private FileUploadComponent fileUploadComponent;


    /**
     * 座椅详情(可捐赠 可认养)
     * @param seatId
     * @return
     */
    public BaseResponse seatApply(String seatId) {
        SeatInfoEntity seatInfoEntity = new LambdaQueryChainWrapper<>(seatInfoMapper)
                .eq(SeatInfoEntity::getId, seatId)
                .one();
        if (seatInfoEntity == null) return MessageResponse.newInstance("座椅不存在!");

        List<SeatImageEntity> seatImageEntityList = new LambdaQueryChainWrapper<>(seatImageMapper)
                .eq(SeatImageEntity::getSeatId, seatId)
                .list();

        AdoptSeatInfoEntity adoptSeatInfoEntity = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
                .eq(AdoptSeatInfoEntity::getSeatId, seatId)
                // 捐赠状态 1:未发布 2:已发布(待认养) 3:被认养
                .eq(AdoptSeatInfoEntity::getStatus, 2)
                .one();

        DonateSeatInfoEntity donateSeatInfoEntity = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
                .eq(DonateSeatInfoEntity::getSeatId, seatId)
                // 捐赠状态 0:已取消 1:未发布 2:已发布 3:捐赠中 4:捐赠完成 5:建造中 6:建造完成
                .in(DonateSeatInfoEntity::getStatus, Stream.of(2,3,4,5,6,7).collect(Collectors.toList()))
                .one();

        SeatInfoVo seatInfoVo = new SeatInfoVo();
        BeanCopierUtil.copy(seatInfoEntity, seatInfoVo);
        // 是否可捐赠
        seatInfoVo.setDonateAvailableFlag(donateSeatInfoEntity == null? Boolean.FALSE:Boolean.TRUE);
        // 是否可认养
        seatInfoVo.setAdoptAvailableFlag(adoptSeatInfoEntity == null? Boolean.FALSE:Boolean.TRUE);
        if (CollectionUtils.isNotEmpty(seatImageEntityList)) {
            seatInfoVo.setImageDeatil(seatImageEntityList.stream()
                    .map(t -> fileUploadComponent.getImageUrl(t.getImage()))
            .collect(Collectors.toList()));
        }
        return DataResponse.success(seatInfoVo);
    }
}
