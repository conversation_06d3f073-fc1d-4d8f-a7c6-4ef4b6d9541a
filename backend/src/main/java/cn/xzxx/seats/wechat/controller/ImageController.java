package cn.xzxx.seats.wechat.controller;

import cn.xzxx.seats.common.constants.ICommonConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.wechat.service.ImageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 图片业务
 */
@RestController
@RequestMapping("wechat")
public class ImageController {

    @Resource
    private ImageService imageService;

    @PostMapping("uploadImage")
    public BaseResponse uploadImage(MultipartFile file) {
        if (file.isEmpty()) {
            return new BaseResponse(ICommonConstants.RESPONSE_STATUS_CODE_FAILED_BIZ, "入参异常");
        }
        return imageService.uploadImage(file);
    }

    @PostMapping("deleteImage")
    public BaseResponse deleteImage(@RequestBody String image) {
        if (StringUtils.isBlank(image)) {
            return new BaseResponse(ICommonConstants.RESPONSE_STATUS_CODE_FAILED_BIZ, "入参异常");
        }
        imageService.deleteImage(image);
        return new BaseResponse();
    }
}
