package cn.xzxx.seats.wechat.controller;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.repository.entity.SeatInfoEntity;
import cn.xzxx.seats.wechat.service.SeatService;
import cn.xzxx.seats.wechat.vo.SeatInfoVo;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 座椅业务
 */
@RestController
@RequestMapping("wechat")
public class SeatController {

    @Resource
    private SeatService seatService;

//    /**
//     * 设置公共座椅
//     * @param seatInfoVo
//     * @return
//     */
//    @PostMapping("insertSeatInfo")
//    public BaseResponse insertSeatInfo(@RequestBody @Validated SeatInfoVo seatInfoVo) {
//        seatService.insertSeatInfo(seatInfoVo);
//        return new BaseResponse();
//    }
//
//    /**
//     * 公布公共座椅
//     * @param seatInfoEntity
//     * @return
//     */
//    @PostMapping("releaseSeat")
//    public BaseResponse releaseSeat(@RequestBody SeatInfoEntity seatInfoEntity) {
//        if (seatInfoEntity == null || seatInfoEntity.getId() == null) {
//            return MessageResponse.newInstance("入参异常");
//        }
//        seatService.releaseSeat(seatInfoEntity);
//        return new BaseResponse();
//    }
//
//    /**
//     * 下架公共座椅
//     * @param seatInfoEntity
//     * @return
//     */
//    @PostMapping("offSeat")
//    public BaseResponse offSeat(@RequestBody SeatInfoEntity seatInfoEntity) {
//        if (seatInfoEntity == null || seatInfoEntity.getId() == null) {
//            return MessageResponse.newInstance("入参异常");
//        }
//        seatService.offSeat(seatInfoEntity);
//        return new BaseResponse();
//    }

    /**
     * 座椅详情(可捐赠 可认养)
     * @param seatId
     * @return
     */
    @GetMapping("seatApply")
    public BaseResponse seatApply(@RequestParam String seatId) {
        return seatService.seatApply(seatId);
    }
}
