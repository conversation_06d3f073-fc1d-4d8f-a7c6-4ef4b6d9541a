package cn.xzxx.seats.wechat.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeatInfoVo {

    /**
     * 座椅ID
     */
    private Integer id;

    /**
     * 座椅编号
     */
    @NotBlank
    @Length(max = 20)
    private String seatNo;

    /**
     * 座椅名称
     */
    @NotBlank
    private String name;

    /**
     * 座椅大小
     */
    private String size;

    /**
     * 座椅材质
     */
    private String material;

    /**
     * 座椅主图URL
     */
    @NotBlank
    private String image;

    /**
     * 座椅详细图URL
     */
    private List<String> imageDeatil;

    /**
     * 座椅介绍
     */
    private String introduce;

    /**
     * 座椅状态 0:未发布 1:发布 2:下架
     */
    @JsonIgnore
    private Integer status;

    /**
     * 座椅价格 单位(分)
     */
    @NotNull
    private Integer price;

    /**
     * 是否可认养
     */
    private Boolean adoptAvailableFlag;

    /**
     * 是否可捐赠
     */
    private Boolean donateAvailableFlag;

//    /**
//     * 发布开始时间
//     */
//    private LocalDateTime startTime;
//
//    /**
//     * 发布结束时间
//     */
//    private LocalDateTime endTime;
}
