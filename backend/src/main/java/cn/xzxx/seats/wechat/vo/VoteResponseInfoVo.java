package cn.xzxx.seats.wechat.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoteResponseInfoVo {

    /**
     * 问卷ID
     */
    @NotNull
    private Integer voteId;

    /**
     * 用户openId
     */
    @NotEmpty
    private String openId;

    /**
     * 投票填写开始时间
     */
    @NotNull
    private String startTime;

    /**
     * 投票填写结束时间
     */
    private String endTime;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer valueFlg;

    /**
     * 投票详情
     */
    @Valid
    @NotNull
    private List<VoteResponseDeatilsVo> deatilsList;
}
