package cn.xzxx.seats.wechat.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class AdoptUpkeepVo {

    /**
     * 维护ID
     */
    private Integer id;

    /**
     * 认养座椅ID
     */
    private Integer adoptId;

    /**
     * 共建座椅ID
     */
    private Integer donateId;

    /**
     * 座椅ID
     */
    private Integer seatId;

    /**
     * 座椅类型 1-共建 2-认养
     */
    private Integer seatType;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

    /**
     * 座椅url
     */
    private String seatImageUrl;

    /**
     * 认养人open_id
     */
    private String openId;

    /**
     * 养护人open_id
     */
    private String upkeepOpenId;

    /**
     * 养护人姓名
     */
    private String upkeepName;

    /**
     * 养护单位
     */
    private String upkeepCompany;

    /**
     * 维护时间
     */
    private String upkeepTime;

    /**
     * 维护前图片
     */
    private String upkeepImageBefore;

    /**
     * 维护后图片
     */
    private String upkeepImageAfter;

    /**
     * 维护备注
     */
    private String memo;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * GPS经度
     */
    private Double gpsLng;

    /**
     * GPS维度
     */
    private Double gpsLat;

    /**
     * GPS经度-baidu
     */
    private Double gpsLngBmap;

    /**
     * GPS维度-baidu
     */
    private Double gpsLatBmap;

    /**
     * 特殊标识 0-全部 1-浦东新区专用
     */
    private Integer specialType;
}
