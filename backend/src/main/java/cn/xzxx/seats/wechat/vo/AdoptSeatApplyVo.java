package cn.xzxx.seats.wechat.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class AdoptSeatApplyVo {

    /**
     * 认养ID
     */
    @NotNull
    private Integer id;

    /**
     * 认养座椅ID
     */
    @NotNull
    private Integer adoptId;

    /**
     * 可认养座椅ID
     */
    @NotNull
    private Integer seatId;

    /**
     * 认养人open_id
     */
    @NotEmpty
    private String openId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    @NotEmpty
    private String idCardNo;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 认养公共座椅原因
     */
    private String reason;

    /**
     * 了解渠道 1:
     */
    private Integer channel;

    /**
     * 认养寄语
     */
    private String donationWord;

    /**
     * 认养有效期（默认一年）
     */
    private String validTime;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 申请通过时间
     */
    private LocalDateTime applyPassTime;

    /**
     * 认养状态 1:申请中 2:审核通过 3:已过期
     */
    private Integer status;

    /**
     * 区划代码
     */
    private String code;

    /**
     * 特殊标识 0-全部 1-浦东新区专用
     */
    private Integer specialType;
}
