package cn.xzxx.seats.wechat.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VoteQuestionVo {

    /**
     * 投票问题ID
     */
    private Integer id;

    /**
     * 投票ID
     */
    private Integer voteId;

    /**
     * 投票编号(表示用)
     */
    private String questionNo;

    /**
     * 唯一标识UUID
     */
    private String uuid;

    /**
     * 问题种类 1-单选 2-多选
     */
    private String type;

    /**
     * 投票问题名称
     */
    private String title;

    /**
     * 问题样式
     */
    private String titleCss;

    /**
     * 问题图片
     */
    private String titleImage;

    /**
     * 问题图片2
     */
    private String titleImage2;

    /**
     * 问题图片3
     */
    private String titleImage3;

    /**
     * 描述
     */
    private String details;

    /**
     * 题目表示顺序
     */
    private Integer displayOrder;

    /**
     * 是否表示 0-非表示 1-表示
     */
    private Integer display;

    /**
     * 问题可选项详情
     */
    private List<VoteQuestionOptionsVo> optionsList;



}
