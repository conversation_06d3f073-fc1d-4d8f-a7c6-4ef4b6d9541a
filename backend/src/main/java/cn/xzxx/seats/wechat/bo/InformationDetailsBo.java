package cn.xzxx.seats.wechat.bo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InformationDetailsBo {

    /**
     * 资讯ID
     */
    private Integer id;

    /**
     * 资讯分类 null:全部 1:新闻 2:公告 3:活动
     */
    private Integer type;

    /**
     * 封面图片URL
     */
    private String imageUrl;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 跳转链接
     */
    private String linkUrl;

}
