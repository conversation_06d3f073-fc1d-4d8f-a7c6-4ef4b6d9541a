package cn.xzxx.seats.wechat.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.xzxx.seats.code.*;
import cn.xzxx.seats.common.exception.BusinessException;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.common.utils.*;
import cn.xzxx.seats.common.utils.GpsUtils.GPS;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.*;
import cn.xzxx.seats.wechat.bo.CheckAdoptUpkeepBo;
import cn.xzxx.seats.wechat.vo.*;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdoptService {

    private final AdoptSeatInfoMapper adoptSeatInfoMapper;
    private final AdoptSeatApplyMapper adoptSeatApplyMapper;
    private final SeatInfoMapper seatInfoMapper;
    private final SeatImageMapper seatImageMapper;
    private final AdoptUpkeepMapper adoptUpkeepMapper;
    private final FileUploadComponent fileUploadComponent;
    private final SysAreaMapper sysAreaMapper;
    private final AdoptUpkeepUserMapper adoptUpkeepUserMapper;
    private final SeatInfoAdoptMapper seatInfoAdoptMapper;
    private final SeatImageAdoptMapper seatImageAdoptMapper;
    private final UpkeepAssignMapper upkeepAssignMapper;
    private final WxUserInfoMapper wxUserInfoMapper;
    private final DonateSeatInfoMapper donateSeatInfoMapper;

    /**
     * 获取所有认养座椅
     * @return
     */
    public BaseResponse getAdoptSeatInfo(String strict, String openId, Integer specialType) {
//        // 根据中文区名称获取区code
//        String idDistrict = null;
//        if (StringUtils.isNotBlank(strict)) {
//            List<SysAreaEntity> list = new LambdaQueryChainWrapper<>(sysAreaMapper)
//                    .eq(SysAreaEntity::getNameDistrict, strict)
//                    .list();
//            if (CollectionUtils.isNotEmpty(list)) { idDistrict = list.get(0).getIdDistrict(); }
//        }
        List<AdoptSeatInfoEntity> list = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
                .eq(StringUtils.isNotBlank(strict), AdoptSeatInfoEntity::getStrict, strict)
                .eq(AdoptSeatInfoEntity::getStatus, AdoptStatus.PUBLISH_FINISH.status())
                .eq(AdoptSeatInfoEntity::getDeleteFlag, Boolean.FALSE)
                .eq(specialType != null, AdoptSeatInfoEntity::getSpecialType, specialType)
                .orderByDesc(AdoptSeatInfoEntity::getCreatedAt)
                .list();
        if (CollectionUtils.isEmpty(list)) return new BaseResponse();

        Map<Integer, AdoptSeatApplyEntity> map;
        if (StrUtil.isNotBlank(openId)) {
            List<AdoptSeatApplyEntity> adoptSeatApplyEntityList = new LambdaQueryChainWrapper<>(adoptSeatApplyMapper)
                    .eq(AdoptSeatApplyEntity::getOpenId, openId)
                    .eq(AdoptSeatApplyEntity::getDeleteFlag, Boolean.FALSE)
                    .list();
            if (CollUtil.isNotEmpty(adoptSeatApplyEntityList)) {
                map = StreamUtils.toMap(adoptSeatApplyEntityList, AdoptSeatApplyEntity::getAdoptId, Function.identity());
            } else {
                map = null;
            }
        } else {
            map = null;
        }

        List<Integer> seatIdList = StreamUtils.toList(list, AdoptSeatInfoEntity::getSeatId);
        Map<Integer, SeatInfoAdoptEntity> seatInfoAdoptEntityMap = new LambdaQueryChainWrapper<>(seatInfoAdoptMapper)
                .in(SeatInfoAdoptEntity::getId, seatIdList)
                .list()
                .stream()
                .collect(Collectors.toMap(SeatInfoAdoptEntity::getId, Function.identity()));
        List<AdoptInfoVo> adoptInfoVoList = new ArrayList<>();
        list.forEach(adoptSeatInfoEntity -> {
            AdoptInfoVo adoptInfoVo = new AdoptInfoVo();
            BeanCopierUtil.copy(adoptSeatInfoEntity, adoptInfoVo);
            SeatInfoAdoptEntity seatInfoAdoptEntity = seatInfoAdoptEntityMap.get(adoptSeatInfoEntity.getSeatId());
            if (seatInfoAdoptEntity != null) {
                adoptInfoVo.setSeatNo(seatInfoAdoptEntity.getSeatNo());
                adoptInfoVo.setImage(fileUploadComponent.getImageUrl(seatInfoAdoptEntity.getImage()));
                adoptInfoVo.setSize(seatInfoAdoptEntity.getSize());
                adoptInfoVo.setMaterial(seatInfoAdoptEntity.getMaterial());
                adoptInfoVo.setIntroduce(seatInfoAdoptEntity.getIntroduce());
                adoptInfoVo.setPrice(seatInfoAdoptEntity.getPrice());
                adoptInfoVo.setSeatName(seatInfoAdoptEntity.getName());
                adoptInfoVo.setStartTime(DateUtils.localDateTimeFormat(seatInfoAdoptEntity.getStartTime()));
            }
            if (StrUtil.isNotEmpty(openId)) {
                if (MapUtil.isNotEmpty(map) && map.containsKey(adoptSeatInfoEntity.getId())) {
                    adoptInfoVo.setOwnerFlag(Boolean.TRUE);
                }
            }
            adoptInfoVoList.add(adoptInfoVo);
        });
        return DataResponse.success(adoptInfoVoList);
    }

    /**
     * 获取认养座椅详情
     * @param id
     * @return
     */
    public AdoptDetailVo getAdoptSeatApply(Integer id) {
        AdoptSeatApplyEntity adoptSeatApply = new LambdaQueryChainWrapper<>(adoptSeatApplyMapper)
                .eq(AdoptSeatApplyEntity::getId, id)
                .one();

        if (adoptSeatApply == null) return null;

        Integer seatId = adoptSeatApply.getSeatId();
        SeatInfoAdoptEntity seatInfoAdopt = new LambdaQueryChainWrapper<>(seatInfoAdoptMapper)
                .eq(SeatInfoAdoptEntity::getId, seatId)
                .one();
        List<SeatImageAdoptEntity> seatImageAdoptEntityList = new LambdaQueryChainWrapper<>(seatImageAdoptMapper)
                .eq(SeatImageAdoptEntity::getSeatId, seatId)
                .list();
        AdoptSeatInfoEntity adoptSeatInfo = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
                .eq(AdoptSeatInfoEntity::getSeatId, seatId)
                .one();
        if (seatInfoAdopt == null || adoptSeatInfo == null) return null;

//        List<String> imageDetail = seatImageEntityList.stream().map(t -> fileUploadComponent.getImageUrl(t.getImage())).collect(Collectors.toList());
        return buildAdoptDetailVo(seatId, adoptSeatApply, seatInfoAdopt, seatImageAdoptEntityList, adoptSeatInfo);
    }

    /**
     * 认养座椅
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse adoptSeat(AdoptDetailVo adoptDetailVo) {
        SeatInfoAdoptEntity seatInfoAdopt = new LambdaQueryChainWrapper<>(seatInfoAdoptMapper)
                .eq(SeatInfoAdoptEntity::getId, adoptDetailVo.getSeatId())
                .eq(adoptDetailVo.getSpecialType() != null, SeatInfoAdoptEntity::getSpecialType, adoptDetailVo.getSpecialType())
                .one();
        if (seatInfoAdopt == null) return MessageResponse.newInstance("座椅不存在!");
        if (AdoptApplyStatus.APPROVED.status() == seatInfoAdopt.getStatus()) return MessageResponse.newInstance("座椅已被认养!");

        List<AdoptSeatApplyEntity> approvedList = new LambdaQueryChainWrapper<>(adoptSeatApplyMapper)
                .eq(AdoptSeatApplyEntity::getSeatId, adoptDetailVo.getSeatId())
                .eq(AdoptSeatApplyEntity::getStatus, AdoptApplyStatus.APPROVED.status())
                .eq(AdoptSeatApplyEntity::getDeleteFlag, Boolean.FALSE)
                .list();
        if (CollectionUtils.isNotEmpty(approvedList)) return MessageResponse.newInstance("该座椅已被认养!");

        AdoptSeatApplyEntity adoptSeatApplyEntity = new AdoptSeatApplyEntity();
        BeanCopierUtil.copy(adoptDetailVo, adoptSeatApplyEntity);
        adoptSeatApplyEntity.setStatus(AdoptStatus.PUBLISH_WAIT.status());
        adoptSeatApplyEntity.setApplyTime(LocalDateTime.now());
        // 认养有效期 1:一年 2:两年 3:三年
        if (adoptDetailVo.getAdoptTerm() != null) {
            switch (adoptDetailVo.getAdoptTerm()) {
                case 1: adoptSeatApplyEntity.setValidTime(LocalDateTime.now().plusYears(1L));break;
                case 2: adoptSeatApplyEntity.setValidTime(LocalDateTime.now().plusYears(2L));break;
                case 3: adoptSeatApplyEntity.setValidTime(LocalDateTime.now().plusYears(3L));break;
                default: adoptSeatApplyEntity.setValidTime(LocalDateTime.now().plusYears(1L));break;
            }
        } else {
            adoptSeatApplyEntity.setValidTime(LocalDateTime.now().plusYears(1L));
        }
        adoptSeatApplyEntity.setUpdatedBy("System");
        adoptSeatApplyMapper.insert(adoptSeatApplyEntity);
        return DataResponse.success(adoptSeatApplyEntity);
    }

    /**
     * 我的认养
     * @param openId
     * @return
     */
    public BaseResponse getAdoptDetailByOpenId(String openId, Boolean passFlag, Integer specialType) {
        List<AdoptSeatApplyEntity> adoptSeatApplyEntityList = new LambdaQueryChainWrapper<>(adoptSeatApplyMapper)
                .eq(AdoptSeatApplyEntity::getOpenId, openId)
                .eq(passFlag != null && passFlag, AdoptSeatApplyEntity::getStatus, AdoptApplyStatus.APPROVED.status())
                .eq(specialType != null, AdoptSeatApplyEntity::getSpecialType, specialType)
                .list();
        if (CollectionUtils.isEmpty(adoptSeatApplyEntityList)) return MessageResponse.newInstance("您还没有认养的座椅");

        // 获取图片
        List<Integer> seatIdList = StreamUtils.toList(adoptSeatApplyEntityList, AdoptSeatApplyEntity::getSeatId);
        Map<Integer, SeatInfoAdoptEntity> seatInfoAdoptEntityMap = new LambdaQueryChainWrapper<>(seatInfoAdoptMapper)
                .in(SeatInfoAdoptEntity::getId, seatIdList)
                .list()
                .stream()
                .collect(Collectors.toMap(SeatInfoAdoptEntity::getId, Function.identity()));
        Map<Integer, List<SeatImageAdoptEntity>> seatImageAdoptEntityMap = new LambdaQueryChainWrapper<>(seatImageAdoptMapper)
                .in(SeatImageAdoptEntity::getSeatId, seatIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(SeatImageAdoptEntity::getSeatId));

        // 获取经纬度
        Map<Integer, AdoptSeatInfoEntity> adoptSeatInfoMap = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
                .in(AdoptSeatInfoEntity::getId, adoptSeatApplyEntityList.stream().map(AdoptSeatApplyEntity::getAdoptId).collect(Collectors.toList()))
                .list()
                .stream()
                .collect(Collectors.toMap(AdoptSeatInfoEntity::getId, t -> t));

        List<AdoptDetailVo> list = new ArrayList<>();
        adoptSeatApplyEntityList.forEach(adoptSeatApplyEntity -> {
            Integer seatId = adoptSeatApplyEntity.getSeatId();
            SeatInfoAdoptEntity seatInfoAdoptEntity = seatInfoAdoptEntityMap.get(seatId);
            if (seatInfoAdoptEntity == null) {
                return;
            }
            List<SeatImageAdoptEntity> seatImageEntityList = seatImageAdoptEntityMap.get(seatId);
            AdoptSeatInfoEntity adoptSeatInfoEntity = adoptSeatInfoMap.get(adoptSeatApplyEntity.getAdoptId());
            list.add(this.buildAdoptDetailVo(seatId, adoptSeatApplyEntity, seatInfoAdoptEntity, seatImageEntityList, adoptSeatInfoEntity));
        });
        return DataResponse.success(list);
    }

    /**
     * 我的养护任务
     */
    public BaseResponse getAdoptUpkeepDetailByOpenId(String openId, Integer specialType) {
        List<AdoptUpkeepEntity> adoptUpkeepEntityList = new LambdaQueryChainWrapper<>(adoptUpkeepMapper)
                .eq(AdoptUpkeepEntity::getUpkeepOpenId, openId)
                .eq(specialType != null, AdoptUpkeepEntity::getSpecialType, specialType)
                .list();
        if (CollUtil.isEmpty(adoptUpkeepEntityList)) return DataResponse.success();

        /* 认养 **/
        Set<Integer> adoptSeatIdList = StreamUtils.toSet(adoptUpkeepEntityList, t-> {
            if (SeatTypeStatus.ADOPT.status() == t.getSeatType()) {
                return t.getSeatId();
            }
            return null;
        });

        Map<Integer, SeatInfoAdoptEntity> adoptMap;
        if (CollUtil.isNotEmpty(adoptSeatIdList)) {
            List<SeatInfoAdoptEntity> seatInfoAdoptEntityList = new LambdaQueryChainWrapper<>(seatInfoAdoptMapper)
                    .in(SeatInfoAdoptEntity::getId, adoptSeatIdList)
                    .list();
            if (CollUtil.isNotEmpty(seatInfoAdoptEntityList)) {
                adoptMap = StreamUtils.toMap(seatInfoAdoptEntityList, SeatInfoAdoptEntity::getId, Function.identity());
            } else {
                adoptMap = null;
            }
        } else {
            adoptMap = null;
        }

        /* 共建 **/
        Set<Integer> donateSeatIdList = StreamUtils.toSet(adoptUpkeepEntityList, t -> {
            if (SeatTypeStatus.DONATE.status() == t.getSeatType()) {
                return t.getSeatId();
            }
            return null;
        });

        Map<Integer, SeatInfoEntity> donateMap;
        if (CollUtil.isNotEmpty(donateSeatIdList)) {
            List<SeatInfoEntity> seatInfoEntityList = new LambdaQueryChainWrapper<>(seatInfoMapper)
                    .in(SeatInfoEntity::getId, donateSeatIdList)
                    .list();
            if (CollUtil.isNotEmpty(seatInfoEntityList)) {
                donateMap = StreamUtils.toMap(seatInfoEntityList, SeatInfoEntity::getId, Function.identity());
            } else {
                donateMap = null;
            }
        } else {
            donateMap = null;
        }


        List<AdoptUpkeepVo> list = CollUtil.newArrayList();
        adoptUpkeepEntityList.forEach(adoptUpkeepEntity -> {
            AdoptUpkeepVo adoptUpkeepVo = new AdoptUpkeepVo();
            BeanCopierUtil.copy(adoptUpkeepEntity, adoptUpkeepVo);
            if (SeatTypeStatus.DONATE.is(adoptUpkeepEntity.getSeatType())) {
                if (MapUtil.isNotEmpty(donateMap) && donateMap.containsKey(adoptUpkeepEntity.getSeatId())) {
                    SeatInfoEntity seatInfoEntity = donateMap.get(adoptUpkeepEntity.getSeatId());
                    if (seatInfoEntity != null) {
                        adoptUpkeepVo.setSeatNo(seatInfoEntity.getSeatNo());
                        adoptUpkeepVo.setSeatName(seatInfoEntity.getName());
                        adoptUpkeepVo.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
                    }
                }
            } else if (SeatTypeStatus.ADOPT.is(adoptUpkeepEntity.getSeatType())) {
                if (MapUtil.isNotEmpty(adoptMap) && adoptMap.containsKey(adoptUpkeepEntity.getSeatId())) {
                    SeatInfoAdoptEntity seatInfoAdoptEntity = adoptMap.get(adoptUpkeepEntity.getSeatId());
                    if (seatInfoAdoptEntity != null) {
                        adoptUpkeepVo.setSeatNo(seatInfoAdoptEntity.getSeatNo());
                        adoptUpkeepVo.setSeatName(seatInfoAdoptEntity.getName());
                        adoptUpkeepVo.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoAdoptEntity.getImage()));
                    }
                }
            }
            adoptUpkeepVo.setUpkeepTime(DateUtils.localDateTimeFormat(adoptUpkeepEntity.getUpkeepTime()));
            list.add(adoptUpkeepVo);
        });

        return DataResponse.success(list);
    }

    /**
     * 获取维护列表
     * @param openId
     * @return
     */
    public BaseResponse getAdoptUpkeepList(String openId, Integer specialType) {
        List<AdoptUpkeepEntity> adoptUpkeepEntityList = new LambdaQueryChainWrapper<>(adoptUpkeepMapper)
                .eq(AdoptUpkeepEntity::getOpenId, openId)
                .eq(specialType != null, AdoptUpkeepEntity::getSpecialType, specialType)
                .list();
        if (CollectionUtils.isEmpty(adoptUpkeepEntityList)) return DataResponse.success();

        List<AdoptUpkeepVo> adoptUpkeepVoList = new ArrayList<>();
        adoptUpkeepEntityList.forEach(adoptUpkeepEntity -> {
            AdoptUpkeepVo adoptUpkeepVo = new AdoptUpkeepVo();
            BeanCopierUtil.copy(adoptUpkeepEntity, adoptUpkeepVo);
            adoptUpkeepVo.setUpkeepTime(DateUtils.localDateTimeFormat(adoptUpkeepEntity.getUpkeepTime()));
            if (StringUtils.isNotBlank(adoptUpkeepEntity.getUpkeepImageBefore())) {
                adoptUpkeepVo.setUpkeepImageBefore(fileUploadComponent.getImageUrl(adoptUpkeepEntity.getUpkeepImageBefore()));
            }
            if (StringUtils.isNotBlank(adoptUpkeepEntity.getUpkeepImageAfter())) {
                adoptUpkeepVo.setUpkeepImageAfter(fileUploadComponent.getImageUrl(adoptUpkeepEntity.getUpkeepImageAfter()));
            }
            if (adoptUpkeepEntity.getSeatType() != null && SeatTypeStatus.DONATE.is(adoptUpkeepEntity.getSeatType())) {
                SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(adoptUpkeepEntity.getSeatId());
                if (seatInfoEntity != null) {
                    adoptUpkeepVo.setSeatName(seatInfoEntity.getName());
                    adoptUpkeepVo.setSeatNo(seatInfoEntity.getSeatNo());
                    adoptUpkeepVo.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
                }
            } else if (SeatTypeStatus.ADOPT.is(adoptUpkeepEntity.getSeatType())) {
                SeatInfoAdoptEntity seatInfoAdoptEntity = seatInfoAdoptMapper.selectById(adoptUpkeepEntity.getSeatId());
                if (seatInfoAdoptEntity != null) {
                    adoptUpkeepVo.setSeatName(seatInfoAdoptEntity.getName());
                    adoptUpkeepVo.setSeatNo(seatInfoAdoptEntity.getSeatNo());
                    adoptUpkeepVo.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoAdoptEntity.getImage()));
                }
            }
            adoptUpkeepVoList.add(adoptUpkeepVo);
        });
        return DataResponse.success(adoptUpkeepVoList);
    }

    /**
     * 维护座椅
     *
     * @param adoptUpkeepVo
     * @return
     */
    public BaseResponse maintainSeat(AdoptUpkeepVo adoptUpkeepVo) {
        try {
//            List<AdoptUpkeepEntity> adoptUpkeepEntityList = new LambdaQueryChainWrapper<>(adoptUpkeepMapper)
//                    .eq(AdoptUpkeepEntity::getOpenId, adoptUpkeepVo.getOpenId())
//                    .eq(AdoptUpkeepEntity::getSeatId, adoptUpkeepVo.getSeatId())
//                    // 维护期一月内不让重复维护
//                    .gt(AdoptUpkeepEntity::getUpkeepTime, LocalDateTime.now().minusMonths(1))
//                    .list();
//            if (CollectionUtils.isNotEmpty(adoptUpkeepEntityList)) {
//                return MessageResponse.newInstance("该座椅已维修过");
//            }
            // 匹配养护分配ID
            if (adoptUpkeepVo.getUpkeepOpenId() == null) return MessageResponse.newInstance("养护人openId不能为空");

            List<AdoptUpkeepUserEntity> adoptUpkeepUserEntityList = new LambdaQueryChainWrapper<>(adoptUpkeepUserMapper)
                    .eq(AdoptUpkeepUserEntity::getOpenId, adoptUpkeepVo.getUpkeepOpenId())
                    .list();
            if (CollUtil.isEmpty(adoptUpkeepUserEntityList)) {
                return MessageResponse.newInstance("该养护人员不存在");
            }

            AdoptUpkeepUserEntity adoptUpkeepUserEntity = adoptUpkeepUserEntityList.get(0);
            List<UpkeepAssignEntity> assignEntityList = new LambdaQueryChainWrapper<>(upkeepAssignMapper)
                    .eq(UpkeepAssignEntity::getSeatType, adoptUpkeepVo.getSeatType())
                    .eq(UpkeepAssignEntity::getUpkeepUserId, adoptUpkeepUserEntity.getId())
                    .eq(UpkeepAssignEntity::getSeatId, adoptUpkeepVo.getSeatId())
                    .list();
            if (CollUtil.isEmpty(assignEntityList)) {
                return MessageResponse.newInstance("该座椅没有分配养护人员");
            }
            UpkeepAssignEntity upkeepAssignEntity = assignEntityList.get(0);
            LocalDateTime upkeepTime = DateUtils.localDateTimeParse(adoptUpkeepVo.getUpkeepTime());
            AdoptUpkeepEntity adoptUpkeepEntity = new AdoptUpkeepEntity();
            BeanCopierUtil.copy(adoptUpkeepVo, adoptUpkeepEntity);
            adoptUpkeepEntity.setUpkeepId(upkeepAssignEntity.getId());
            adoptUpkeepEntity.setUpkeepName(adoptUpkeepUserEntity.getName());
            adoptUpkeepEntity.setUpkeepCompany(adoptUpkeepUserEntity.getCompany());
            adoptUpkeepEntity.setId(null);
//            GPS gps = GpsUtils.map_tx2bd(adoptUpkeepVo.getGpsLat().toString(), adoptUpkeepVo.getGpsLng().toString());
            adoptUpkeepEntity.setGpsLatBmap(adoptUpkeepVo.getGpsLatBmap());
            adoptUpkeepEntity.setGpsLngBmap(adoptUpkeepVo.getGpsLngBmap());
            adoptUpkeepEntity.setUpkeepTime(upkeepTime);
            adoptUpkeepEntity.setUpdatedBy("System");
            adoptUpkeepMapper.insert(adoptUpkeepEntity);

            // TODO 更新分配表养护状态
            upkeepAssignEntity.setStatus(UpkeepAssignStatus.UPKEEP_FAIL.status());
            upkeepAssignMapper.updateById(upkeepAssignEntity);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return new BaseResponse();
    }

    /**
     * 修改认养申请记录
     * @param adoptSeatApplyVo
     * @return
     */
    public BaseResponse modifyAdoptApply(AdoptSeatApplyVo adoptSeatApplyVo) {
        AdoptSeatApplyEntity adoptSeatApplyEntity = new LambdaQueryChainWrapper<>(adoptSeatApplyMapper)
                .eq(AdoptSeatApplyEntity::getId, adoptSeatApplyVo.getId())
                .one();
        if (adoptSeatApplyEntity == null) {
            return MessageResponse.newInstance("没有这条申请记录");
        }

        if (AdoptApplyStatus.APPROVED.is(adoptSeatApplyEntity.getStatus())) {
            return MessageResponse.newInstance("该申请已审核完毕,不能修改");
        } else {
            try {
                BeanCopierUtil.copy(adoptSeatApplyVo, adoptSeatApplyEntity);
                adoptSeatApplyEntity.setStatus(AdoptApplyStatus.APPLYING.status());
                adoptSeatApplyEntity.setUpdatedBy("System");
                if (StringUtils.isNotBlank(adoptSeatApplyVo.getValidTime())) {
                    adoptSeatApplyEntity.setValidTime(DateUtils.localDateTimeParse(adoptSeatApplyVo.getValidTime()));
                } else {
                    adoptSeatApplyEntity.setValidTime(LocalDateTime.now().plusYears(1L));
                }
                if (adoptSeatApplyEntity.getStatus() == 1) {
                    adoptSeatApplyMapper.updateById(adoptSeatApplyEntity);
                } else {
                    adoptSeatApplyEntity.setId(null);
                    adoptSeatApplyMapper.insert(adoptSeatApplyEntity);
                }
                return new BaseResponse();
            } catch (Exception e) {
                log.error(ExceptionUtils.getStackTrace(e));
                return MessageResponse.newInstance("修改申请失败!失败原因:"+e.getMessage());
            }
        }
    }

    /**
     * 根据手机号查询是否是养护人
     * 若是养护人返回养护信息
     *
     * @param tel
     * @param openId
     * @param seatId
     * @return
     */
    public BaseResponse getUpkeepUserByTel(String tel, String openId, Integer seatId, Integer specialType) {
        List<AdoptUpkeepUserEntity> adoptUpkeepUserEntityList = new LambdaQueryChainWrapper<>(adoptUpkeepUserMapper)
                .eq(AdoptUpkeepUserEntity::getTelephone, tel)
                .list();
        AdoptUpkeepUserVo adoptUpkeepUserVo = new AdoptUpkeepUserVo();
        List<AdoptSeatInfoEntity> adoptSeatInfoEntityList = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
                .eq(seatId != null, AdoptSeatInfoEntity::getSeatId, seatId)
                .eq(specialType != null, AdoptSeatInfoEntity::getSpecialType, specialType)
                .list();
        if (CollectionUtils.isNotEmpty(adoptSeatInfoEntityList)) {
            AdoptSeatInfoEntity adoptSeatInfoEntity = adoptSeatInfoEntityList.get(0);
            adoptUpkeepUserVo.setAdoptId(adoptSeatInfoEntity.getId());
        }
        if (CollectionUtils.isNotEmpty(adoptUpkeepUserEntityList)) {
            adoptUpkeepUserVo.setIsUpkeepUser(Boolean.TRUE);
            AdoptUpkeepUserEntity adoptUpkeepUserEntity = adoptUpkeepUserEntityList.get(0);
            adoptUpkeepUserEntity.setOpenId(openId);
            adoptUpkeepUserMapper.updateById(adoptUpkeepUserEntity);
            BeanCopierUtil.copy(adoptUpkeepUserEntity, adoptUpkeepUserVo);
            List<AdoptUpkeepEntity> adoptUpkeepEntityList = new LambdaQueryChainWrapper<>(adoptUpkeepMapper)
                    .eq(AdoptUpkeepEntity::getUpkeepOpenId, openId)
                    .eq(seatId != null, AdoptUpkeepEntity::getSeatId, seatId)
                    .list();
            List<AdoptUpkeepVo> list = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(adoptUpkeepEntityList)) {
                adoptUpkeepEntityList.forEach(adoptUpkeepEntity -> {
                    AdoptUpkeepVo adoptUpkeepVo = new AdoptUpkeepVo();
                    BeanCopierUtil.copy(adoptUpkeepEntity, adoptUpkeepVo);
                    adoptUpkeepVo.setUpkeepTime(DateUtils.localDateTimeFormat(adoptUpkeepEntity.getUpkeepTime()));
                    if (StringUtils.isNotBlank(adoptUpkeepEntity.getUpkeepImageBefore())) {
                        adoptUpkeepVo.setUpkeepImageBefore(fileUploadComponent.getImageUrl(adoptUpkeepEntity.getUpkeepImageBefore()));
                    }
                    if (StringUtils.isNotBlank(adoptUpkeepEntity.getUpkeepImageAfter())) {
                        adoptUpkeepVo.setUpkeepImageAfter(fileUploadComponent.getImageUrl(adoptUpkeepEntity.getUpkeepImageAfter()));
                    }
                    if (adoptUpkeepEntity.getSeatType() != null && SeatTypeStatus.DONATE.is(adoptUpkeepEntity.getSeatType())) {
                        SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(adoptUpkeepEntity.getSeatId());
                        if (seatInfoEntity != null) {
                            adoptUpkeepVo.setSeatName(seatInfoEntity.getName());
                            adoptUpkeepVo.setSeatNo(seatInfoEntity.getSeatNo());
                            adoptUpkeepVo.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
                        }
                    } else if (SeatTypeStatus.ADOPT.is(adoptUpkeepEntity.getSeatType())) {
                        SeatInfoAdoptEntity seatInfoAdoptEntity = seatInfoAdoptMapper.selectById(adoptUpkeepEntity.getSeatId());
                        if (seatInfoAdoptEntity != null) {
                            adoptUpkeepVo.setSeatName(seatInfoAdoptEntity.getName());
                            adoptUpkeepVo.setSeatNo(seatInfoAdoptEntity.getSeatNo());
                            adoptUpkeepVo.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoAdoptEntity.getImage()));
                        }
                    }
                    list.add(adoptUpkeepVo);
                });
            }
            adoptUpkeepUserVo.setAdoptUpkeepVoList(list);
            return DataResponse.success(adoptUpkeepUserVo);
        }
        adoptUpkeepUserVo.setIsUpkeepUser(Boolean.FALSE);
        adoptUpkeepUserVo.setIsUpkeepUser(Boolean.FALSE);
        return DataResponse.success(adoptUpkeepUserVo);
    }

    /**
     * 获取认养座椅及认养人信息
     *
     * @param id
     * @return
     */
    public BaseResponse getAdoptInfoAndAdoptUsers(Integer id, Integer specialType) {
        AdoptSeatInfoEntity adoptSeatInfoEntity = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
                .eq(AdoptSeatInfoEntity::getId, id)
                .eq(specialType != null, AdoptSeatInfoEntity::getSpecialType, specialType)
                .one();
        if (adoptSeatInfoEntity == null) {
            return DataResponse.fail("无认养座椅信息");
        }
        SeatInfoAdoptEntity seatInfoAdoptEntity = new LambdaQueryChainWrapper<>(seatInfoAdoptMapper)
                .eq(SeatInfoAdoptEntity::getId, adoptSeatInfoEntity.getSeatId())
                .one();
        if (seatInfoAdoptEntity == null) {
            return DataResponse.fail("无认养座椅信息");
        }
        AdoptInfoVo adoptInfoVo = new AdoptInfoVo();
        BeanCopierUtil.copy(adoptSeatInfoEntity, adoptInfoVo);
        // 填充座椅信息
        adoptInfoVo.setSeatNo(seatInfoAdoptEntity.getSeatNo());
        adoptInfoVo.setSeatName(seatInfoAdoptEntity.getName());
        adoptInfoVo.setSize(seatInfoAdoptEntity.getSize());
        adoptInfoVo.setMaterial(seatInfoAdoptEntity.getMaterial());
        adoptInfoVo.setImage(fileUploadComponent.getImageUrl(seatInfoAdoptEntity.getImage()));
        adoptInfoVo.setIntroduce(seatInfoAdoptEntity.getIntroduce());
        adoptInfoVo.setPrice(seatInfoAdoptEntity.getPrice());
        adoptInfoVo.setOwnerUnit(seatInfoAdoptEntity.getOwnerUnit());
        adoptInfoVo.setConstructUnit(seatInfoAdoptEntity.getConstructUnit());
        // 填充认养人信息
        List<AdoptSeatApplyEntity> seatApplyEntityList = new LambdaQueryChainWrapper<>(adoptSeatApplyMapper)
                .eq(AdoptSeatApplyEntity::getAdoptId, id)
                .orderByDesc(AdoptSeatApplyEntity::getCreatedAt)
                .list();
        List<AdoptUserVo> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(seatApplyEntityList)) {
            seatApplyEntityList.forEach(adoptSeatApplyEntity -> {
                AdoptUserVo adoptUserVo = new AdoptUserVo();
                BeanCopierUtil.copy(adoptSeatApplyEntity, adoptUserVo);
                adoptUserVo.setAdoptApplyId(adoptSeatApplyEntity.getId());
                if (adoptUserVo.getDonationWordFlag() != null && adoptUserVo.getDonationWordFlag()) {
                    adoptUserVo.setDonationWord(adoptSeatApplyEntity.getDonationWord());
                } else {
                    adoptUserVo.setDonationWord("");
                }
                list.add(adoptUserVo);
            });
        }
        adoptInfoVo.setUserVoList(list);
        // 填充养护人信息
        List<AdoptMaintainerVo> list1 = new ArrayList<>();
        List<AdoptUpkeepEntity> upkeepEntityList = new LambdaQueryChainWrapper<>(adoptUpkeepMapper)
                .eq(AdoptUpkeepEntity::getSeatId, adoptSeatInfoEntity.getSeatId())
                .list();
        if (CollectionUtils.isNotEmpty(upkeepEntityList)) {
            list1 = BeanCopyUtils.copyList(upkeepEntityList, AdoptMaintainerVo.class);
        }
        adoptInfoVo.setAdoptMaintainerVoList(list1);
        return DataResponse.success(adoptInfoVo);
    }

    public BaseResponse checkAdoptUpkeepAvailable(CheckAdoptUpkeepBo bo) {
        List<AdoptUpkeepUserEntity> adoptUpkeepUserEntityList = new LambdaQueryChainWrapper<>(adoptUpkeepUserMapper)
                .eq(AdoptUpkeepUserEntity::getTelephone, bo.getTel())
                .list();
        if (CollUtil.isEmpty(adoptUpkeepUserEntityList)) {
            return DataResponse.fail("该手机号未注册养护人");
        }

        List<UpkeepAssignEntity> assignEntityList = new LambdaQueryChainWrapper<>(upkeepAssignMapper)
                .eq(UpkeepAssignEntity::getSeatType, bo.getSeatType())
                .eq(UpkeepAssignEntity::getSeatId, bo.getSeatId())
                .list();
        if (CollUtil.isEmpty(assignEntityList)) {
            return DataResponse.fail("该座椅没有共建或者认养完成，还不能养护");
        }

        // 存在待养护状态的座椅
        boolean available = assignEntityList.stream().anyMatch(upkeepAssignEntity -> {
            if (UpkeepAssignStatus.UPKEEP_WAIT.is(upkeepAssignEntity.getStatus())) {
                return true;
            }
            return false;
        });

        if (!available) {
            return DataResponse.fail("该座椅已被养护或者养护已过期,请联系管理员后台审核");
        } else {
            return DataResponse.success(Boolean.TRUE);
        }

    }

    private AdoptDetailVo buildAdoptDetailVo(Integer seatId, AdoptSeatApplyEntity adoptSeatApply, SeatInfoAdoptEntity seatInfo, List<SeatImageAdoptEntity> seatImageEntityList, AdoptSeatInfoEntity adoptSeatInfoEntity) {
        AdoptDetailVo adoptDetailVo = AdoptDetailVo.builder()
                .seatId(seatId)
                .seatNo(seatInfo.getSeatNo())
                .seatName(seatInfo.getName())
                .seatSize(seatInfo.getSize())
                .seatMaterial(seatInfo.getMaterial())
                .seatIntroduce(seatInfo.getIntroduce())
                .seatStatus(seatInfo.getStatus())
                .seatPrice(seatInfo.getPrice())
                .startTime(DateUtils.localDateTimeFormat(seatInfo.getStartTime()))
                .endTime(DateUtils.localDateTimeFormat(seatInfo.getEndTime()))
                .image(fileUploadComponent.getImageUrl(seatInfo.getImage()))
                .specialType(seatInfo.getSpecialType())
                .adress(adoptSeatInfoEntity != null? adoptSeatInfoEntity.getAdress():"")
                .gpsLat(adoptSeatInfoEntity != null? adoptSeatInfoEntity.getGpsLat():null)
                .gpsLng(adoptSeatInfoEntity != null? adoptSeatInfoEntity.getGpsLng():null)
                .build();
        if (adoptSeatInfoEntity != null && adoptSeatInfoEntity.getSpecialType() != null) {
            adoptDetailVo.setSpecialType(adoptSeatInfoEntity.getSpecialType());
        }
        if (CollectionUtils.isNotEmpty(seatImageEntityList)) {
            adoptDetailVo.setImageDetail(seatImageEntityList.stream().map(t -> fileUploadComponent.getImageUrl(t.getImage())).collect(Collectors.toList()));
        } else {
            adoptDetailVo.setImageDetail(Stream.of(adoptDetailVo.getImage()).collect(Collectors.toList()));
        }
        if (adoptSeatApply != null) {
            adoptDetailVo.setAdoptId(adoptSeatApply.getAdoptId());
            adoptDetailVo.setAdoptApplyId(adoptSeatApply.getId());
            adoptDetailVo.setOpenId(adoptSeatApply.getOpenId());
            adoptDetailVo.setName(adoptSeatApply.getName());
            adoptDetailVo.setIdCardNo(adoptSeatApply.getIdCardNo());
            adoptDetailVo.setSex(adoptSeatApply.getSex());
            adoptDetailVo.setAge(adoptSeatApply.getAge());
            adoptDetailVo.setTel(adoptSeatApply.getTel());
            adoptDetailVo.setProvince(adoptSeatApply.getProvince());
            adoptDetailVo.setCity(adoptSeatApply.getCity());
            adoptDetailVo.setStrict(adoptSeatApply.getStrict());
            adoptDetailVo.setStreet(adoptSeatApply.getStreet());
            adoptDetailVo.setReason(adoptSeatApply.getReason());
            adoptDetailVo.setChannel(adoptSeatApply.getChannel());
            adoptDetailVo.setDonationWord(adoptSeatApply.getDonationWord());
            if (adoptSeatApply.getDonationWordFlag() != null && adoptSeatApply.getDonationWordFlag()) {
                adoptDetailVo.setDonationWord(adoptSeatApply.getDonationWord());
            }
            adoptDetailVo.setValidTime(DateUtils.localDateTimeFormat(adoptSeatApply.getValidTime()));
            adoptDetailVo.setStatus(adoptSeatApply.getStatus());
            adoptDetailVo.setCode(adoptSeatApply.getCode());
            if (adoptSeatApply.getApplyTime() != null) {
                adoptDetailVo.setApplyTime(DateUtils.localDateTimeFormat(adoptSeatApply.getApplyTime()));
            }
            if (adoptSeatApply.getApplyPassTime() != null) {
                adoptDetailVo.setApplyPassTime(DateUtils.localDateTimeFormat(adoptSeatApply.getApplyPassTime()));
            }
            if (adoptSeatInfoEntity != null) {
                adoptDetailVo.setAdoptTerm(adoptSeatInfoEntity.getAdoptTerm());
                adoptDetailVo.setProvince(adoptSeatInfoEntity.getProvince());
                adoptDetailVo.setCity(adoptSeatInfoEntity.getCity());
                adoptDetailVo.setStrict(adoptSeatInfoEntity.getStrict());
                adoptDetailVo.setStreet(adoptSeatInfoEntity.getStreet());
            }
        }
        return adoptDetailVo;
    }

    /**
     * 根据座椅id获取养护记录
     * @param seatId 座椅id
     * @param seatType 座椅类型
     * @return 养护记录
     */
    public BaseResponse getAdoptUpkeepBySeatId(Integer seatId, Integer seatType, Integer specialType) {
        List<AdoptUpkeepEntity> adoptUpkeepEntityList = new LambdaQueryChainWrapper<>(adoptUpkeepMapper)
                .eq(AdoptUpkeepEntity::getSeatId, seatId)
                .eq(AdoptUpkeepEntity::getSeatType, seatType)
                .eq(specialType != null, AdoptUpkeepEntity::getSpecialType, specialType)
                .orderByDesc(AdoptUpkeepEntity::getCreatedAt)
                .list();
        if (CollUtil.isEmpty(adoptUpkeepEntityList)) {
            return DataResponse.success(new ArrayList<>());
        }

        /* 养护 **/
        Map<Integer, SeatInfoAdoptEntity> adoptMap;
        List<SeatInfoAdoptEntity> seatInfoAdoptEntityList = new LambdaQueryChainWrapper<>(seatInfoAdoptMapper)
                .in(SeatInfoAdoptEntity::getId, StreamUtils.toList(adoptUpkeepEntityList, AdoptUpkeepEntity::getSeatId))
                .list();
        if (CollUtil.isNotEmpty(seatInfoAdoptEntityList)) {
            adoptMap = StreamUtils.toMap(seatInfoAdoptEntityList, SeatInfoAdoptEntity::getId, Function.identity());
        } else {
            adoptMap = null;
        }

        /* 共建 **/
        Map<Integer, SeatInfoEntity> donateMap;
        List<SeatInfoEntity> seatInfoEntityList = new LambdaQueryChainWrapper<>(seatInfoMapper)
                .in(SeatInfoEntity::getId, StreamUtils.toList(adoptUpkeepEntityList, AdoptUpkeepEntity::getSeatId))
                .list();
        if (CollUtil.isNotEmpty(seatInfoEntityList)) {
            donateMap = StreamUtils.toMap(seatInfoEntityList, SeatInfoEntity::getId, Function.identity());
        } else {
            donateMap = null;
        }

        List<AdoptUpkeepVo> adoptUpkeepVoList = new ArrayList();
        adoptUpkeepEntityList.forEach(adoptUpkeepEntity -> {
            AdoptUpkeepVo adoptUpkeepVo = new AdoptUpkeepVo();
            BeanCopyUtils.copy(adoptUpkeepEntity, adoptUpkeepVo);
            adoptUpkeepVo.setUpkeepTime(DateUtils.localDateTimeFormat(adoptUpkeepEntity.getUpkeepTime()));
            if (SeatTypeStatus.ADOPT.is(adoptUpkeepEntity.getSeatType())) {
                if (MapUtil.isNotEmpty(adoptMap) && adoptMap.containsKey(adoptUpkeepEntity.getSeatId())) {
                    SeatInfoAdoptEntity seatInfoAdoptEntity = adoptMap.get(adoptUpkeepEntity.getSeatId());
                    if (seatInfoAdoptEntity != null) {
                        adoptUpkeepVo.setSeatNo(seatInfoAdoptEntity.getSeatNo());
                        adoptUpkeepVo.setSeatName(seatInfoAdoptEntity.getName());
                        adoptUpkeepVo.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoAdoptEntity.getImage()));
                    }
                }
            } else if (SeatTypeStatus.DONATE.is(adoptUpkeepEntity.getSeatType())) {
                if (MapUtil.isNotEmpty(donateMap) && donateMap.containsKey(adoptUpkeepEntity.getSeatId())) {
                    SeatInfoEntity seatInfoEntity = donateMap.get(adoptUpkeepEntity.getSeatId());
                    if (seatInfoEntity != null) {
                        adoptUpkeepVo.setSeatNo(seatInfoEntity.getSeatNo());
                        adoptUpkeepVo.setSeatName(seatInfoEntity.getName());
                        adoptUpkeepVo.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
                    }
                }
            }
            adoptUpkeepVoList.add(adoptUpkeepVo);
        });
        return DataResponse.success(adoptUpkeepVoList);
    }

    /**
     * 查询所有被认养的座椅
     */
    public BaseResponse getAllAdoptSeat(String openId, Integer specialType) {

        AdoptUpkeepUserEntity upkeepUser = new LambdaQueryChainWrapper<>(adoptUpkeepUserMapper).eq(AdoptUpkeepUserEntity::getOpenId, openId).one();
        if(upkeepUser == null) {
            return DataResponse.success(new ArrayList<>());
        }
        List<UpkeepAssignEntity> assignEntityList = new LambdaQueryChainWrapper<>(upkeepAssignMapper)
                .eq(UpkeepAssignEntity::getUpkeepUserId, upkeepUser.getId())
                .eq(UpkeepAssignEntity::getStatus, UpkeepAssignStatus.UPKEEP_WAIT.status())
                .le(UpkeepAssignEntity::getUpkeepDateBegin, LocalDate.now())
                .ge(UpkeepAssignEntity::getUpkeepDateEnd, LocalDate.now())
                .eq(specialType != null, UpkeepAssignEntity::getSpecialType, specialType)
                .list();
        if (CollUtil.isEmpty(assignEntityList)) {
            return DataResponse.success(new ArrayList<>());
        }

        /* 共建 **/
        Map<Integer, SeatInfoEntity> seatInfoMap;
        Map<Integer, DonateSeatInfoEntity> donateMap;
        Set<Object> donateIdSet = StreamUtils.toSet(assignEntityList, t -> {
            if (SeatTypeStatus.DONATE.status() == t.getSeatType()) {
                return t.getSeatId();
            }
            return null;
        });

        if (CollUtil.isNotEmpty(donateIdSet)) {
            List<SeatInfoEntity> seatInfoEntityList = new LambdaQueryChainWrapper<>(seatInfoMapper)
                    .in(SeatInfoEntity::getId, donateIdSet)
                    .list();
            if (CollUtil.isNotEmpty(seatInfoEntityList)) {
                seatInfoMap = StreamUtils.toMap(seatInfoEntityList, SeatInfoEntity::getId, Function.identity());
            } else {
                seatInfoMap = null;
            }

            List<DonateSeatInfoEntity> donateSeatInfoEntityList = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
                    .in(DonateSeatInfoEntity::getSeatId, donateIdSet)
                    .list();
            if (CollUtil.isNotEmpty(donateSeatInfoEntityList)) {
                donateMap = StreamUtils.toMap(donateSeatInfoEntityList, DonateSeatInfoEntity::getSeatId, Function.identity());
            } else {
                donateMap = null;
            }
        } else {
            donateMap = null;
            seatInfoMap = null;
        }

        /* 认养 **/
        Map<Integer, SeatInfoAdoptEntity> seatInfoAdoptMap;
        Map<Integer, AdoptSeatInfoEntity> adoptMap;
        Set<Object> adoptIdSet = StreamUtils.toSet(assignEntityList, t -> {
            if (SeatTypeStatus.ADOPT.status() == t.getSeatType()) {
                return t.getSeatId();
            }
            return null;
        });

        if (CollUtil.isNotEmpty(adoptIdSet)) {
            List<SeatInfoAdoptEntity> seatInfoAdoptEntityList = new LambdaQueryChainWrapper<>(seatInfoAdoptMapper)
                    .in(SeatInfoAdoptEntity::getId, adoptIdSet)
                    .list();
            if (CollUtil.isNotEmpty(seatInfoAdoptEntityList)) {
                seatInfoAdoptMap = StreamUtils.toMap(seatInfoAdoptEntityList, SeatInfoAdoptEntity::getId, Function.identity());
            } else {
                seatInfoAdoptMap = null;
            }

            List<AdoptSeatInfoEntity> adoptSeatInfoEntityList = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
                    .in(AdoptSeatInfoEntity::getSeatId, adoptIdSet)
                    .list();
            if (CollUtil.isNotEmpty(adoptSeatInfoEntityList)) {
                adoptMap = StreamUtils.toMap(adoptSeatInfoEntityList, AdoptSeatInfoEntity::getSeatId, Function.identity());
            } else {
                adoptMap = null;
            }
        } else {
            adoptMap = null;
            seatInfoAdoptMap = null;
        }

        List<AdoptSeatInfoVo> list = new ArrayList<>();
        assignEntityList.forEach(assignEntity -> {
            AdoptSeatInfoVo adoptSeatInfoVo = new AdoptSeatInfoVo();
            BeanCopyUtils.copy(assignEntity, adoptSeatInfoVo);
            if (assignEntity.getSeatType() != null && SeatTypeStatus.DONATE.is(assignEntity.getSeatType())) {
                if (MapUtil.isNotEmpty(seatInfoMap) && seatInfoMap.containsKey(assignEntity.getSeatId())) {
                    SeatInfoEntity seatInfoEntity = seatInfoMap.get(assignEntity.getSeatId());
                    if (seatInfoEntity != null) {
                        adoptSeatInfoVo.setSeatNo(seatInfoEntity.getSeatNo());
                        adoptSeatInfoVo.setName(seatInfoEntity.getName());
                        adoptSeatInfoVo.setSize(seatInfoEntity.getSize());
                        adoptSeatInfoVo.setMaterial(seatInfoEntity.getMaterial());
                        adoptSeatInfoVo.setImage(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
                        adoptSeatInfoVo.setIntroduce(seatInfoEntity.getIntroduce());
                    }
                }
                if (MapUtil.isNotEmpty(donateMap) && donateMap.containsKey(assignEntity.getSeatId())) {
                    DonateSeatInfoEntity donateSeatInfoEntity = donateMap.get(assignEntity.getSeatId());
                    if (donateSeatInfoEntity != null) {
                        BeanCopyUtils.copy(donateSeatInfoEntity, adoptSeatInfoVo);
                    }
                    adoptSeatInfoVo.setDonateId(donateSeatInfoEntity.getId());
                }
            } else if (assignEntity.getSeatType() != null && SeatTypeStatus.ADOPT.is(assignEntity.getSeatType())) {
                if (MapUtil.isNotEmpty(seatInfoAdoptMap) && seatInfoAdoptMap.containsKey(assignEntity.getSeatId())) {
                    SeatInfoAdoptEntity seatInfoAdoptEntity = seatInfoAdoptMap.get(assignEntity.getSeatId());
                    if (seatInfoAdoptEntity != null) {
                        adoptSeatInfoVo.setSeatNo(seatInfoAdoptEntity.getSeatNo());
                        adoptSeatInfoVo.setName(seatInfoAdoptEntity.getName());
                        adoptSeatInfoVo.setSize(seatInfoAdoptEntity.getSize());
                        adoptSeatInfoVo.setMaterial(seatInfoAdoptEntity.getMaterial());
                        adoptSeatInfoVo.setImage(fileUploadComponent.getImageUrl(seatInfoAdoptEntity.getImage()));
                        adoptSeatInfoVo.setIntroduce(seatInfoAdoptEntity.getIntroduce());
                    }
                }
                if (MapUtil.isNotEmpty(adoptMap) && adoptMap.containsKey(assignEntity.getSeatId())) {
                    AdoptSeatInfoEntity adoptSeatInfoEntity = adoptMap.get(assignEntity.getSeatId());
                    if (adoptSeatInfoEntity != null) {
                        BeanCopyUtils.copy(adoptSeatInfoEntity, adoptSeatInfoVo);
                    }
                    adoptSeatInfoVo.setAdoptId(adoptSeatInfoEntity.getId());
                }
            }
            adoptSeatInfoVo.setStatus(assignEntity.getStatus());
            adoptSeatInfoVo.setId(assignEntity.getId());
            list.add(adoptSeatInfoVo);
        });
        return DataResponse.success(list);
    }
}
