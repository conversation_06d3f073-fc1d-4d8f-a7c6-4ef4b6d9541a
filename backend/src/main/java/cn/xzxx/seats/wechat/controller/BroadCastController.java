package cn.xzxx.seats.wechat.controller;

import cn.xzxx.seats.code.UpkeepAssignStatus;
import cn.xzxx.seats.common.base.UploadFiles;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.common.response.PulldownItem;
import cn.xzxx.seats.common.utils.DateUtils;
import cn.xzxx.seats.common.utils.StringUtil;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.*;
import cn.xzxx.seats.web.common.CommonService;
import cn.xzxx.seats.web.upkeep.dto.UpkeepForm;
import cn.xzxx.seats.web.upkeep.dto.UpkeepParam;
import cn.xzxx.seats.wechat.service.BroadCastService;
import cn.xzxx.seats.wechat.vo.SeatInfoVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 广播业务
 */
@RestController
@RequestMapping("wechat")
@RequiredArgsConstructor
public class BroadCastController {

    private final BroadCastService broadCastService;
    private final UpkeepAssignMapper upkeepAssignMapper;
    private final SeatInfoMapper seatInfoMapper;
    private final SeatInfoAdoptMapper seatInfoAdoptMapper;
    private final CommonService commonService;
    private final DonateSeatInfoMapper donateSeatInfoMapper;
    private final AdoptSeatInfoMapper adoptSeatInfoMapper;
    private final FileUploadComponent fileUploadComponent;
    private final AdoptUpkeepUserMapper adoptUpkeepUserMapper;

    /**
     * 获取广播列表
     * @return
     */
    @GetMapping("getBroadcastSettings")
    public BaseResponse getBroadcastSettings() {
        return broadCastService.getBroadcastSettings();
    }

    /**
     * 测试
     * @return
     */
    @GetMapping("test1")
    public BaseResponse test(UpkeepParam param) {
        return DataResponse.success();
//        DataResponse<UpkeepForm> result = new DataResponse<>();
//        UpkeepForm upkeepForm = new UpkeepForm();
//        if (param.getInit() != null && param.getInit()) {
//            List<PulldownItem> upkeepUserOptions = new ArrayList<>();
//            List<AdoptUpkeepUserEntity> adoptUpkeepUsers = adoptUpkeepUserMapper.selectList(new LambdaQueryWrapper<>());
//            for (AdoptUpkeepUserEntity s : adoptUpkeepUsers) {
//                upkeepUserOptions.add(new PulldownItem(s.getName(), s.getId()));
//            }
//            upkeepForm.setUpkeepUserOptions(upkeepUserOptions);
//        }
//        UpkeepAssignEntity upkeepAssignEntity = upkeepAssignMapper.selectById(param.getId());
//        BeanUtils.copyProperties(upkeepAssignEntity, upkeepForm);
//        if(upkeepAssignEntity.getSeatType() == 1) {
//            // 捐赠
//            SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(upkeepAssignEntity.getSeatId());
//            if(seatInfoEntity == null) {
//                return MessageResponse.newInstance("数据异常，座椅信息不存在。");
//            }
//            List<DonateSeatInfoEntity> donateInfos = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
//                    .eq(DonateSeatInfoEntity::getSeatId, seatInfoEntity.getId()).list();
//            if(CollectionUtils.isEmpty(donateInfos)) {
//                return MessageResponse.newInstance("数据异常，捐赠信息不存在。");
//            }
//            setUpkeepInitInfo(upkeepForm, upkeepAssignEntity, seatInfoEntity, donateInfos.get(0));
//
//        } else {
//
//            // 认养
//            SeatInfoAdoptEntity seatInfoEntity = seatInfoAdoptMapper.selectById(upkeepAssignEntity.getSeatId());
//            if(seatInfoEntity == null) {
//                return MessageResponse.newInstance("数据异常，座椅信息(认养)不存在。");
//            }
//            List<AdoptSeatInfoEntity> adoptInfos = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
//                    .eq(AdoptSeatInfoEntity::getSeatId, seatInfoEntity.getId()).list();
//            if(CollectionUtils.isEmpty(adoptInfos)) {
//                return MessageResponse.newInstance("数据异常，认养信息不存在。");
//            }
//            setUpkeepInitInfoAdopt(upkeepForm, upkeepAssignEntity, seatInfoEntity, adoptInfos.get(0));
//        }
//        return result.setValue(upkeepForm);
    }

    private void setUpkeepInitInfoAdopt(UpkeepForm upkeepForm, UpkeepAssignEntity upkeepAssignEntity, SeatInfoAdoptEntity seatInfoEntity, AdoptSeatInfoEntity adoptInfo) {
        upkeepForm.setStatusText(UpkeepAssignStatus.statusName(upkeepAssignEntity.getStatus()));
        upkeepForm.setUpkeepDateBegin(DateUtils.localDateFormat(upkeepAssignEntity.getUpkeepDateBegin()));
        upkeepForm.setUpkeepDateEnd(DateUtils.localDateFormat(upkeepAssignEntity.getUpkeepDateEnd()));
        upkeepForm.setSeatNo(seatInfoEntity.getSeatNo());
        upkeepForm.setSeatName(seatInfoEntity.getName());
        upkeepForm.setSeatMaterial(seatInfoEntity.getMaterial());
        upkeepForm.setSeatSize(seatInfoEntity.getSize());
        upkeepForm.setSeatPrice(seatInfoEntity.getPrice());
        upkeepForm.setOwnerUnit(seatInfoEntity.getOwnerUnit());
        upkeepForm.setConstructUnit(seatInfoEntity.getConstructUnit());
        upkeepForm.setSeatIntroduce(seatInfoEntity.getIntroduce());
        upkeepForm.setProvince(adoptInfo.getProvince());
        upkeepForm.setCity(adoptInfo.getCity());
        upkeepForm.setStrict(adoptInfo.getStrict());
        upkeepForm.setStreet(adoptInfo.getStreet());
        upkeepForm.setAdress(adoptInfo.getAdress());
        upkeepForm.setPcdName(commonService.getPcdName(adoptInfo.getProvince(), adoptInfo.getCity(), adoptInfo.getStrict(), adoptInfo.getStreet()));

        if (!StringUtil.isEmpty(seatInfoEntity.getImage())) {
            UploadFiles uploadFiles = new UploadFiles();
            uploadFiles.addFile(seatInfoEntity.getImage(), "", fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
            upkeepForm.setSeatImage(uploadFiles);
        }
    }

    private void setUpkeepInitInfo(UpkeepForm upkeepForm, UpkeepAssignEntity upkeepAssignEntity, SeatInfoEntity seatInfoEntity, DonateSeatInfoEntity donateInfo) {

        upkeepForm.setStatusText(UpkeepAssignStatus.statusName(upkeepAssignEntity.getStatus()));
        upkeepForm.setUpkeepDateBegin(DateUtils.localDateFormat(upkeepAssignEntity.getUpkeepDateBegin()));
        upkeepForm.setUpkeepDateEnd(DateUtils.localDateFormat(upkeepAssignEntity.getUpkeepDateEnd()));
        upkeepForm.setSeatNo(seatInfoEntity.getSeatNo());
        upkeepForm.setSeatName(seatInfoEntity.getName());
        upkeepForm.setSeatMaterial(seatInfoEntity.getMaterial());
        upkeepForm.setSeatSize(seatInfoEntity.getSize());
        upkeepForm.setSeatPrice(seatInfoEntity.getPrice());
        upkeepForm.setOwnerUnit(seatInfoEntity.getOwnerUnit());
        upkeepForm.setConstructUnit(seatInfoEntity.getConstructUnit());
        upkeepForm.setSeatIntroduce(seatInfoEntity.getIntroduce());
        upkeepForm.setProvince(donateInfo.getProvince());
        upkeepForm.setCity(donateInfo.getCity());
        upkeepForm.setStrict(donateInfo.getStrict());
        upkeepForm.setStreet(donateInfo.getStreet());
        upkeepForm.setAdress(donateInfo.getAdress());
        upkeepForm.setPcdName(commonService.getPcdName(donateInfo.getProvince(), donateInfo.getCity(), donateInfo.getStrict(), donateInfo.getStreet()));

        if (!StringUtil.isEmpty(seatInfoEntity.getImage())) {
            UploadFiles uploadFiles = new UploadFiles();
            uploadFiles.addFile(seatInfoEntity.getImage(), "", fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
            upkeepForm.setSeatImage(uploadFiles);
        }
    }

}
