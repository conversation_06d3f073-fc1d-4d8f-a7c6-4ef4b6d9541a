package cn.xzxx.seats.wechat.controller;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.repository.entity.WxUserInfoEntity;
import cn.xzxx.seats.wechat.service.TokenService;
import cn.xzxx.seats.wechat.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 用户业务
 */
@RestController
@RequestMapping("wechat")
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private TokenService tokenService;

    /**
     * 获取openId
     * @param code
     * @return
     */
    @GetMapping("getOpenId")
    public BaseResponse getOpenId(@RequestParam String code) {
        return userService.getOpenId(code);
    }

    /**
     * 新增或者修改用户信息
     * @param wxUserInfoEntity
     * @return
     */
    @PostMapping("addUserInfo")
    public BaseResponse addUserInfo(@RequestBody WxUserInfoEntity wxUserInfoEntity) {
        if (wxUserInfoEntity == null || StringUtils.isBlank(wxUserInfoEntity.getOpenId())) {
            return MessageResponse.newInstance("openId不得为空");
        }
        return userService.addUserInfo(wxUserInfoEntity);
    }

    /**
     * 获取用户信息
     * @param openId
     * @return
     */
    @GetMapping("getUserInfo")
    public BaseResponse getUserInfo(@RequestParam String openId) {
        return userService.getUserInfo(openId);
    }

    /**
     * 创建token
     * @param wxUserInfoEntity
     * @return
     */
    @PostMapping("creatToken")
    public BaseResponse creatToken(@RequestBody WxUserInfoEntity wxUserInfoEntity) {
        if (wxUserInfoEntity == null || StringUtils.isBlank(wxUserInfoEntity.getOpenId())) {
            return MessageResponse.newInstance("入参异常");
        }
        String token = tokenService.creatToken(wxUserInfoEntity.getOpenId());
        return DataResponse.success(token);
    }
}
