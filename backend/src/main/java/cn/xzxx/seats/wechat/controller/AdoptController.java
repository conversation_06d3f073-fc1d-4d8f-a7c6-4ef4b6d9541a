package cn.xzxx.seats.wechat.controller;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.wechat.bo.CheckAdoptUpkeepBo;
import cn.xzxx.seats.wechat.service.AdoptService;
import cn.xzxx.seats.wechat.vo.AdoptDetailVo;
import cn.xzxx.seats.wechat.vo.AdoptSeatApplyVo;
import cn.xzxx.seats.wechat.vo.AdoptUpkeepVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 认养业务
 */
@Slf4j
@RestController
@RequestMapping("/wechat")
public class AdoptController {

    @Resource
    private AdoptService adoptService;

    /**
     * 获取所有认养座椅
     */
    @GetMapping("getAdoptSeatInfo")
    public BaseResponse getAdoptSeatInfo(@RequestParam(required = false) String strict,
                                         @RequestParam(required = false) String openId,
                                         @RequestParam(required = false) Integer specialType) {
        return adoptService.getAdoptSeatInfo(strict, openId, specialType);
    }

    /**
     * 认养座椅详情
     * @param id
     * @return
     */
    @GetMapping("getAdoptSeatApply")
    public BaseResponse getAdoptSeatApply(@RequestParam Integer id) {
       return DataResponse.success(adoptService.getAdoptSeatApply(id));
    }

    /**
     * 认养座椅
     * @param adoptDetailVo
     * @return
     */
    @PostMapping("adoptSeat")
    public BaseResponse adoptSeat(@RequestBody @Validated AdoptDetailVo adoptDetailVo) {
        return adoptService.adoptSeat(adoptDetailVo);
    }

    /**
     * 我的认养
     * @param openId
     * @return
     */
    @GetMapping("getAdoptDetailByOpenId")
    public BaseResponse getAdoptDetailByOpenId(@RequestParam String openId,
                                               @RequestParam(required = false) Boolean passFlag,
                                               @RequestParam(required = false) Integer specialType) {
        return adoptService.getAdoptDetailByOpenId(openId, passFlag, specialType);
    }

    /**
     * 我的养护任务
     * @param openId
     * @return
     */
    @GetMapping("getAdoptUpkeepDetailByOpenId")
    public BaseResponse getAdoptUpkeepDetailByOpenId(@RequestParam String openId,
                                                     @RequestParam(required = false) Integer specialType) {
        return adoptService.getAdoptUpkeepDetailByOpenId(openId, specialType);
    }

    /**
     * 获取维护列表
     * @param openId
     * @return
     */
    @GetMapping("getAdoptUpkeepList")
    public BaseResponse getAdoptUpkeepList(@RequestParam String openId,
                                           @RequestParam(required = false) Integer specialType) {
        return adoptService.getAdoptUpkeepList(openId, specialType);
    }

    /**
     * 维护座椅
     * @param adoptUpkeepVo
     * @return
     */
    @PostMapping("maintainSeat")
    public BaseResponse maintainSeat(@RequestBody @Validated AdoptUpkeepVo adoptUpkeepVo) {
        return adoptService.maintainSeat(adoptUpkeepVo);
    }

    /**
     * 修改认养申请记录
     * @param adoptSeatApplyVo
     * @return
     */
    @PostMapping("modifyAdoptApply")
    public BaseResponse modifyAdoptApply(@RequestBody @Validated AdoptSeatApplyVo adoptSeatApplyVo) {
        return adoptService.modifyAdoptApply(adoptSeatApplyVo);
    }

    /**
     * 根据手机号查询是否是养护人
     * 若是养护人返回养护信息
     *
     * @param tel
     * @param openId
     * @return
     */
    @GetMapping("getUpkeepUserByTel")
    public BaseResponse getUpkeepUserByTel(@RequestParam String tel,
                                           @RequestParam String openId,
                                           @RequestParam(required = false) Integer seatId,
                                           @RequestParam(required = false) Integer specialType) {
        return adoptService.getUpkeepUserByTel(tel, openId, seatId, specialType);
    }

    /**
     * 获取认养座椅及认养人信息
     * @param id
     * @return
     */
    @GetMapping("getAdoptInfoAndAdoptUsers")
    public BaseResponse getAdoptInfoAndAdoptUsers(@RequestParam Integer id,
                                                  @RequestParam(required = false) Integer specialType) {
        return adoptService.getAdoptInfoAndAdoptUsers(id, specialType);
    }

    /**
     * 根据手机号和座椅号判断是否可以养护
     */
    @PostMapping("checkAdoptUpkeepAvailable")
    public BaseResponse checkAdoptUpkeepAvailable(@RequestBody @Validated CheckAdoptUpkeepBo bo) {
        return adoptService.checkAdoptUpkeepAvailable(bo);
    }

    /**
     * 根据座椅id获取养护记录
     * @param seatId 座椅id
     * @return 养护记录
     */
    @GetMapping("getAdoptUpkeepBySeatId")
    public BaseResponse getAdoptUpkeepBySeatId(@RequestParam Integer seatId,
                                               @RequestParam Integer seatType,
                                               @RequestParam(required = false) Integer specialType) {
        return adoptService.getAdoptUpkeepBySeatId(seatId, seatType, specialType);
    }

    /**
     * 查询所有被认养的座椅
     */
    @GetMapping("getAllAdoptSeat")
    public BaseResponse getAllAdoptSeat(@RequestParam String openId, @RequestParam(required = false) Integer specialType) {
        return adoptService.getAllAdoptSeat(openId, specialType);
    }
}
