package cn.xzxx.seats.wechat.controller;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.wechat.bo.InformationDetailsBo;
import cn.xzxx.seats.wechat.service.InformationSettingsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("wechat")
@RequiredArgsConstructor
public class InformationSettingsController {

    private final InformationSettingsService informationSettingsService;

    /**
     * 获取资讯标题设置
     * @return
     */
    @GetMapping("getInformationTitleSettings")
    public BaseResponse getInformationTitleSettings() {
        return informationSettingsService.getInformationTitleSettings();
    }

    /**
     * 获取资讯详情设置
     * @return
     */
    @PostMapping("getInformationDetailsSettings")
    public BaseResponse getInformationDetailsSettings(@RequestBody InformationDetailsBo bo) {
        return informationSettingsService.getInformationDetailsSettings(bo);
    }


}
