package cn.xzxx.seats.wechat.vo;

import lombok.Data;

@Data
public class AdoptSeatInfoVo {

    /**
     * 分配座椅ID
     */
    private Integer id;

    /**
     * 可认养座椅ID
     */
    private Integer adoptId;

    /**
     * 共建座椅ID
     */
    private Integer donateId;

    /**
     * 认养座椅NO
     */
    private String adoptNo;

    /**
     * 捐赠座椅NO
     */
    private String donateNo;

    /**
     * 座椅ID
     */
    private Integer seatId;

    /**
     * 座椅ID
     */
    private Integer seatType;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String name;

    /**
     * 座椅大小
     */
    private String size;

    /**
     * 座椅材质
     */
    private String material;

    /**
     * 座椅主图URL
     */
    private String image;

    /**
     * 座椅介绍
     */
    private String introduce;

    /**
     * 认养金额
     */
    private Integer adoptPrice;

    /**
     * 认养期限 1-一年 2-两年3-三年
     */
    private Integer adoptTerm;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * GPS经度
     */
    private Double gpsLng;

    /**
     * GPS维度
     */
    private Double gpsLat;

    /**
     * GPS经度-baidu
     */
    private Double gpsLngBmap;

    /**
     * GPS维度-baidu
     */
    private Double gpsLatBmap;

    /**
     * 认养状态 1:未发布 2:已发布(待认养) 3:被认养
     */
    private Integer status;
}
