package cn.xzxx.seats.wechat.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSeatCareVo {

    /**
     * 关注ID
     */
    private Integer id;

    /**
     * 微信ID
     */
    private String openId;

    /**
     * 座椅分类 1-共建 2-认养
     */
    private Integer seatType;

    /**
     * 认养座椅ID
     */
    private Integer adoptId;

    /**
     * 共建座椅ID
     */
    private Integer donateId;

    /**
     * 捐赠/认养座椅ID
     */
    private Integer seatId;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String name;

    /**
     * 座椅大小
     */
    private String size;

    /**
     * 座椅材质
     */
    private String material;

    /**
     * 座椅主图URL
     */
    private String image;

    /**
     * 座椅详细图URL
     */
    private List<String> imageDeatil;

    /**
     * 座椅介绍
     */
    private String introduce;

    /**
     * 座椅状态 0:未发布 1:发布 2:下架
     */
    private Integer status;

    /**
     * 座椅价格 单位(分)
     */
    private Integer price;






}
