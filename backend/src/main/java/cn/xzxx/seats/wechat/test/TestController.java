package cn.xzxx.seats.wechat.test;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/wechat")
public class TestController {

  @Autowired
  private TestService testService;

  @GetMapping("/test")
  public BaseResponse test() {

    return new DataResponse<>();
  }
}
