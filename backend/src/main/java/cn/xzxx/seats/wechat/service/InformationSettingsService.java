package cn.xzxx.seats.wechat.service;

import cn.hutool.core.util.StrUtil;
import cn.xzxx.seats.code.ShowStatus;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.utils.StringUtil;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.repository.entity.InformationDetailsSettingsEntity;
import cn.xzxx.seats.repository.entity.InformationTitleSettingsEntity;
import cn.xzxx.seats.repository.mapper.InformationDetailsSettingsMapper;
import cn.xzxx.seats.repository.mapper.InformationTitleSettingsMapper;
import cn.xzxx.seats.wechat.bo.InformationDetailsBo;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class InformationSettingsService {

    private final InformationTitleSettingsMapper informationTitleSettingsMapper;
    private final InformationDetailsSettingsMapper informationDetailsSettingsMapper;
    private final FileUploadComponent fileUploadComponent;

    public BaseResponse getInformationTitleSettings() {
        List<InformationTitleSettingsEntity> list = new LambdaQueryChainWrapper<>(informationTitleSettingsMapper)
                .eq(InformationTitleSettingsEntity::getStatus, ShowStatus.SHOW.status())
                .eq(InformationTitleSettingsEntity::getDeleteFlag, Boolean.FALSE)
                .list();
        list.forEach(item -> {
            if (StrUtil.isNotEmpty(item.getImageUrl())) {
                item.setImageUrl(fileUploadComponent.getImageUrl(item.getImageUrl()));
            }
        });
        return DataResponse.success(list);
    }

    public BaseResponse getInformationDetailsSettings(InformationDetailsBo bo) {
        List<InformationDetailsSettingsEntity> list = new LambdaQueryChainWrapper<>(informationDetailsSettingsMapper)
                .eq(InformationDetailsSettingsEntity::getStatus, ShowStatus.SHOW.status())
                .eq(InformationDetailsSettingsEntity::getDeleteFlag, Boolean.FALSE)
                .eq(bo.getType() != null, InformationDetailsSettingsEntity::getType, bo.getType())
                .eq(bo.getId() != null, InformationDetailsSettingsEntity::getId, bo.getId())
                .list();
        list.forEach(item -> {
            if (StrUtil.isNotEmpty(item.getImageUrl())) {
                item.setImageUrl(fileUploadComponent.getImageUrl(item.getImageUrl()));
            }
        });
        return DataResponse.success(list);
    }
}
