package cn.xzxx.seats.wechat.service;

import cn.xzxx.seats.common.utils.DateTimeUtils;
import cn.xzxx.seats.common.utils.EnDecrypt;
import cn.xzxx.seats.common.utils.SmUtils;
import org.springframework.stereotype.Service;

@Service
public class TokenService {

    String appId="BF1A6E2AF9A54D3CAEAF90033B955619";    //项目编号，用于加密偏移量（阴符）。
    int vTime=7200000 ;    //token内签信息失效时间(秒)
    //上述两参数可放置于项目配置文件中

    //UserID可为微信小程序用户的OpenID、网页用户的用户名等。传参时以密签形式加入（MD5等）更安全。
    //创建token令牌,UserId可为空（非空时，每用户对应不同的加解密密钥）
    public String creatToken(String UserId){
        String issueTime = DateTimeUtils.getDateTime("sss",0,"sss");
        String validTime = DateTimeUtils.getDateTime("sss",vTime,"ss");
        String[] keys = keys(EnDecrypt.MD5_Encrypt(issueTime,UserId));
        String A1 = SmUtils.encryptData_SM4_CBC(keys[1],keys[2],"issueTime:"+issueTime);
        String A2 = SmUtils.encryptData_SM4_CBC(keys[1],keys[2],"validTime:"+validTime);
        String A3 = SmUtils.encryptData_SM3(A1+A2+issueTime,"BASE64");
        return A1+"."+A2+"."+A3+"."+issueTime;
    }

    //判断Token是否有效,UserId可为空（非空时，每用户对应不同的加解密密钥）
    public boolean checkToken(String token){
        String UserId="";
        String[] _token = token.split("\\.");
        int tokenLength=_token.length;
        if(tokenLength==4){
            //取数组最后一个参数作为userId
            UserId=_token[3];

            String[] keys = keys(UserId);
            String A1 = SmUtils.decryptData_SM4_CBC(keys[1],keys[2],_token[0]);
            if(A1==null){
                return false;
            }
            String A2 = SmUtils.decryptData_SM4_CBC(keys[1],keys[2],_token[1]);
            String A3 = SmUtils.encryptData_SM3(_token[0]+_token[1]+A1.split(":")[1].trim(),"BASE64");

            if(A3.equals(_token[2])){
                //有效时间
                assert A2 != null;
                long t1 = Long.parseLong(A2.split(":")[1].trim());
                //当前时间
                long t2 = Long.parseLong(DateTimeUtils.getDateTime("sss",0,"sss"));
                return t1 >= t2;
            }
        }
        return false;
    }

    //验证时的key
    private String[] keys(String tokenId){
        String key = EnDecrypt.MD5_Encrypt(tokenId,appId);
        String[] _key = new String[3];
        _key[0] = key;
        _key[1] = key.substring(0,16);
        _key[2] = key.substring(16);
        return _key;
    }

}
