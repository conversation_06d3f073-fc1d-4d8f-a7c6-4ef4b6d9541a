package cn.xzxx.seats.wechat.service;

import cn.xzxx.seats.common.constants.ICommonConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.common.utils.BeanCopierUtil;
import cn.xzxx.seats.common.utils.DateUtils;
import cn.xzxx.seats.common.utils.StringUtil;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.*;
import cn.xzxx.seats.wechat.vo.*;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@Slf4j
public class VoteService {

    @Resource
    private VoteInfoMapper voteInfoMapper;

    @Resource
    private VoteQuestionMapper voteQuestionMapper;

    @Resource
    private VoteQuestionOptionsMapper voteQuestionOptionsMapper;

    @Resource
    private VoteResponseInfoMapper voteResponseInfoMapper;

    @Resource
    private VoteResponseDetailsMapper voteResponseDeatilsMapper;

    /**
     * 获取投票信息列表
     * @param id 主键id 根据id异步加载数据 每次限制20条
     *           eg: id=20 增加检索条件 id>20
     * @return
     */
    public BaseResponse getVoteInfo(Integer id, String openId) {
        List<VoteInfoEntity> voteInfoList = new LambdaQueryChainWrapper<>(voteInfoMapper)
                // TODO 投票状态 -1:编辑中  0:未发布 1:已发布 2:已结束 9:已删除筛选？
                .in(VoteInfoEntity::getStatus, Stream.of(1,2).collect(Collectors.toList()))
                .gt(id != null, VoteInfoEntity::getId, id)
                .isNotNull(VoteInfoEntity::getStartTime)
                // 投票开始时间倒序
                .orderByDesc(VoteInfoEntity::getStartTime)
                .last("limit "+ICommonConstants.LIMIT_SQL_ROWNUM)
                .list();
        if (CollectionUtils.isEmpty(voteInfoList)) return new BaseResponse();

        // 查询最早的开始时间对应记录 通知前端下拉滚动条结束标识
        VoteInfoListVo voteInfoListVo = new VoteInfoListVo();
        VoteInfoEntity earliestVoteInfo = new LambdaQueryChainWrapper<>(voteInfoMapper)
                .eq(VoteInfoEntity::getStatus, 1)
                .orderByAsc(VoteInfoEntity::getStartTime)
                .last("limit 1")
                .one();
        Integer maxId = voteInfoList.stream().map(VoteInfoEntity::getId).max(Integer::compare).get();
        if (earliestVoteInfo.getId() -  maxId <= Integer.parseInt(ICommonConstants.LIMIT_SQL_ROWNUM)) {
            voteInfoListVo.setIsFinishFlag(true);
        } else {
            voteInfoListVo.setIsFinishFlag(false);
        }

        // 查询投票历史 是否为本人投票
        List<VoteResponseInfoEntity> responseInfoList = new LambdaQueryChainWrapper<>(voteResponseInfoMapper)
                .eq(VoteResponseInfoEntity::getOpenId, openId)
                .le(VoteResponseInfoEntity::getVoteId, maxId)
                .eq(VoteResponseInfoEntity::getValueFlg, 1)
                .list();
        AtomicReference<Map<Integer, VoteResponseInfoEntity>> voteHistoryMapRef = new AtomicReference<>(null);
        if (CollectionUtils.isNotEmpty(responseInfoList)) {
            Map<Integer, VoteResponseInfoEntity> map = responseInfoList.stream().collect(Collectors.toMap(VoteResponseInfoEntity::getVoteId, t -> t));
            voteHistoryMapRef.set(map);
        }

        List<VoteInfoVo> voteInfoVoList = new ArrayList<>();
        voteInfoList.forEach(voteInfoEntity -> {
            VoteInfoVo voteInfoVo = new VoteInfoVo();
            BeanCopierUtil.copy(voteInfoEntity, voteInfoVo);
            voteInfoVo.setStartTime(DateUtils.localDateTimeFormat(voteInfoEntity.getStartTime()));
            voteInfoVo.setEndTime(voteInfoEntity.getEndTime() == null? null:DateUtils.localDateTimeFormat(voteInfoEntity.getEndTime()));
            voteInfoVo.setHistoryVoteFlag(false);
            if (voteHistoryMapRef.get() != null) {
                Map<Integer, VoteResponseInfoEntity> map = voteHistoryMapRef.get();
                // 本人历史投票标识
                if (map.containsKey(voteInfoEntity.getId())) { voteInfoVo.setHistoryVoteFlag(true); }
            }
            voteInfoVoList.add(voteInfoVo);
        });
        voteInfoListVo.setVoteInfoVoList(voteInfoVoList);
        return DataResponse.success(voteInfoListVo);
    }

    /**
     * 获取投票详细信息
     * @param voteId
     * @return
     */
    public BaseResponse getVoteDetails(Integer voteId, String openId) {
        List<VoteQuestionEntity> voteQuestionList = new LambdaQueryChainWrapper<>(voteQuestionMapper)
                .eq(VoteQuestionEntity::getVoteId, voteId)
                .eq(VoteQuestionEntity::getDisplay, 1)
                .orderByAsc(VoteQuestionEntity::getDisplayOrder)
                .list();
        List<VoteQuestionOptionsEntity> voteQuestionOptionsList = new LambdaQueryChainWrapper<>(voteQuestionOptionsMapper)
                .eq(VoteQuestionOptionsEntity::getVoteId, voteId)
                .list();
        if (CollectionUtils.isEmpty(voteQuestionList)||CollectionUtils.isEmpty(voteQuestionOptionsList)) return MessageResponse.newInstance("无问题数据!");

        List<VoteResponseDetailsEntity> voteResponseDeatilsList = new LambdaQueryChainWrapper<>(voteResponseDeatilsMapper)
                .eq(VoteResponseDetailsEntity::getVoteId, voteId)
                .list();
        AtomicReference<Map<Integer, Map<Integer, Long>>> voteNumMapRef = new AtomicReference<>(null);
        if (CollectionUtils.isNotEmpty(voteResponseDeatilsList)) {
            // 按照每个问题每个选项统计投票数目
            Map<Integer, Map<Integer, Long>> voteNumMap = voteResponseDeatilsList.stream().collect(
                    Collectors.groupingBy(VoteResponseDetailsEntity::getQuestionId,
                            Collectors.groupingBy(VoteResponseDetailsEntity::getUserOption, Collectors.counting())));
            voteNumMapRef.set(voteNumMap);
        }

        // 查询response_id 用于匹配是否本人历史投票
        AtomicReference<Map<Integer, Map<Integer, VoteResponseDetailsEntity>>> voteHistoryMapRef = new AtomicReference<>(null);
        List<VoteResponseInfoEntity> voteResponseInfoList = new LambdaQueryChainWrapper<>(voteResponseInfoMapper)
                .eq(VoteResponseInfoEntity::getVoteId, voteId)
                .eq(VoteResponseInfoEntity::getOpenId, openId)
                .list();
        if (CollectionUtils.isNotEmpty(voteResponseInfoList)) {
            Integer id = voteResponseInfoList.get(0).getId();
            List<VoteResponseDetailsEntity> voteResponseDetailsHistoryList = new LambdaQueryChainWrapper<>(voteResponseDeatilsMapper)
                    .eq(VoteResponseDetailsEntity::getResponseId, id)
                    .eq(VoteResponseDetailsEntity::getVoteId, voteId)
                    .list();
            if (CollectionUtils.isNotEmpty(voteResponseDetailsHistoryList)) {
                Map<Integer, Map<Integer, VoteResponseDetailsEntity>> map = voteResponseDetailsHistoryList.stream().collect(Collectors.groupingBy(VoteResponseDetailsEntity::getQuestionId,
                        Collectors.toMap(VoteResponseDetailsEntity::getUserOption, t -> t)));
                voteHistoryMapRef.set(map);
            }
        }

        List<VoteQuestionVo> voteQuestionVoList = new ArrayList<>();
        Map<Integer, List<VoteQuestionOptionsEntity>> voteQuestionOptionsMap = voteQuestionOptionsList.stream().collect(Collectors.groupingBy(VoteQuestionOptionsEntity::getQuestionId));
        voteQuestionList.forEach(voteQuestion -> {
            // 「voteQuestionVo」 单个问题 及 多个可选项信息
            VoteQuestionVo voteQuestionVo = new VoteQuestionVo();
            BeanCopierUtil.copy(voteQuestion, voteQuestionVo);
            List<VoteQuestionOptionsEntity> optionsList = voteQuestionOptionsMap.get(voteQuestion.getId());
            AtomicReference<Map<Integer, VoteResponseDetailsEntity>> innerVoteHistoryMapRef = new AtomicReference<>(null);
            if (voteHistoryMapRef.get() != null) {
                innerVoteHistoryMapRef.set(voteHistoryMapRef.get().get(voteQuestion.getId()));
            }

            // 「voteQuestionOptionsVoList」 单个问题的可选项
            List<VoteQuestionOptionsVo> voteQuestionOptionsVoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(optionsList)) {

                // 统计每个选项票数即是否本人投过票
                countVoteNum(voteNumMapRef, voteQuestion, voteQuestionVo, optionsList, voteQuestionOptionsVoList, innerVoteHistoryMapRef);
            }
            voteQuestionVoList.add(voteQuestionVo);
        });
        return DataResponse.success(voteQuestionVoList);
    }

    /**
     * 提交投票结果
     * @param voteResponseInfoVo
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse commitVote(VoteResponseInfoVo voteResponseInfoVo) {
        List<VoteResponseInfoEntity> list = new LambdaQueryChainWrapper<>(voteResponseInfoMapper)
                .eq(VoteResponseInfoEntity::getVoteId, voteResponseInfoVo.getVoteId())
                .eq(VoteResponseInfoEntity::getOpenId, voteResponseInfoVo.getOpenId())
                .list();
        if (CollectionUtils.isNotEmpty(list)) {
            return MessageResponse.newInstance("您已投过票啦!");
        }

        VoteResponseInfoEntity voteResponseInfoEntity = new VoteResponseInfoEntity();
        BeanCopierUtil.copy(voteResponseInfoVo, voteResponseInfoEntity);
        voteResponseInfoEntity.setStartTime(DateUtils.localDateTimeParse(voteResponseInfoVo.getStartTime()));
        if (!StringUtil.isEmpty(voteResponseInfoVo.getEndTime())) {
            voteResponseInfoEntity.setEndTime(DateUtils.localDateTimeParse(voteResponseInfoVo.getEndTime()));
        }
        voteResponseInfoEntity.setUpdatedBy("System");
        voteResponseInfoMapper.insert(voteResponseInfoEntity);
        Integer id = voteResponseInfoEntity.getId();
        AtomicReference<Integer> responseIdRef = new AtomicReference<>();
        responseIdRef.set(id);

        List<VoteResponseDeatilsVo> deatilsList = voteResponseInfoVo.getDeatilsList();
        deatilsList.forEach( voteResponseDeatilsVo -> {
            VoteResponseDetailsEntity voteResponseDeatilsEntity = new VoteResponseDetailsEntity();
            BeanCopierUtil.copy(voteResponseDeatilsVo, voteResponseDeatilsEntity);
            voteResponseDeatilsEntity.setResponseId(responseIdRef.get());
            voteResponseDeatilsEntity.setUpdatedBy("System");
            voteResponseDeatilsMapper.insert(voteResponseDeatilsEntity);
        });
        return new BaseResponse();
    }

    private void countVoteNum(AtomicReference<Map<Integer, Map<Integer, Long>>> voteNumMapRef, VoteQuestionEntity voteQuestion, VoteQuestionVo voteQuestionVo,
                              List<VoteQuestionOptionsEntity> optionsList, List<VoteQuestionOptionsVo> voteQuestionOptionsVoList,
                              AtomicReference<Map<Integer, VoteResponseDetailsEntity>> innerVoteHistoryMapRef) {
        AtomicReference<Map<Integer, Long>> voteNumMapInnerRef = new AtomicReference<>(null);
        if (voteNumMapRef.get() != null) {
            voteNumMapInnerRef.set(voteNumMapRef.get().get(voteQuestion.getId()));
        }
        optionsList.forEach(voteQuestionOptions -> {
            // 「voteQuestionOptionsVo」 单个问题的单个选项及该选项的票数 票数可为0
            VoteQuestionOptionsVo voteQuestionOptionsVo = new VoteQuestionOptionsVo();
            BeanCopierUtil.copy(voteQuestionOptions,voteQuestionOptionsVo);
            voteQuestionOptionsVo.setTitle(voteQuestionOptions.getText());
            voteQuestionOptionsVo.setTitleImage(voteQuestionOptions.getImage());
            if (voteNumMapInnerRef.get() != null && voteNumMapInnerRef.get().get(voteQuestionOptions.getAnswerId()) != null) {
                voteQuestionOptionsVo.setVoteNum(voteNumMapInnerRef.get().get(voteQuestionOptions.getAnswerId()));
            } else {
                voteQuestionOptionsVo.setVoteNum(0L);
            }
            voteQuestionOptionsVo.setHistoryVoteFlag(false);
            if (innerVoteHistoryMapRef.get() != null) {
                if ( innerVoteHistoryMapRef.get().containsKey(voteQuestionOptionsVo.getAnswerId())) {
                    voteQuestionOptionsVo.setHistoryVoteFlag(true);
                }
            }
            voteQuestionOptionsVoList.add(voteQuestionOptionsVo);
        });
        voteQuestionVo.setOptionsList(voteQuestionOptionsVoList);
    }
}