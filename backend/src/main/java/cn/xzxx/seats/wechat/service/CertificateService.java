package cn.xzxx.seats.wechat.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.xzxx.seats.code.BroadcastStatus;
import cn.xzxx.seats.code.DonateStatus;
import cn.xzxx.seats.code.SeatTypeStatus;
import cn.xzxx.seats.code.ShowStatus;
import cn.xzxx.seats.common.exception.BusinessException;
import cn.xzxx.seats.common.message.MessageConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.utils.BeanCopyUtils;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.config.ProjectConfig;
import cn.xzxx.seats.config.security.ResponseSupport;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.*;
import cn.xzxx.seats.utils.QRCodeUtil;
import cn.xzxx.seats.web.orcode.QrCodeService;
import cn.xzxx.seats.wechat.bo.UserSeatCareBo;
import cn.xzxx.seats.wechat.vo.CertificateSettingsVo;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

@Service
@Slf4j
@RequiredArgsConstructor
public class CertificateService {

    private final CertificateSettingsMapper certificateSettingsMapper;
    private final WxUserInfoMapper wxUserInfoMapper;
    private final DonateSeatApplyMapper donateSeatApplyMapper;
    private final DonateSeatInfoMapper donateSeatInfoMapper;
    private final FileUploadComponent fileUploadComponent;
    private final SeatImageMapper seatImageMapper;
    private final SeatInfoMapper seatInfoMapper;
    private final QrCodeService qrCodeService;
    private final SeatInfoAdoptMapper seatInfoAdoptMapper;;
    private final AdoptSeatInfoMapper adoptSeatInfoMapper;
    private static String DEF_DESCRIBE = "参与共建了0只座椅";

    public BaseResponse getPublicWelfareBusinessCard(String openId) throws Exception {
        List<WxUserInfoEntity> list = new LambdaQueryChainWrapper<>(wxUserInfoMapper)
                .eq(WxUserInfoEntity::getOpenId, openId)
                .list();
        if (CollUtil.isEmpty(list)) return DataResponse.fail("用户不存在");

        List<DonateSeatApplyEntity> donateSeatApplyEntityList = new LambdaQueryChainWrapper<>(donateSeatApplyMapper)
                .eq(DonateSeatApplyEntity::getOpenId, openId)
                .eq(DonateSeatApplyEntity::getDeleteFlag, Boolean.FALSE)
                .notIn(DonateSeatApplyEntity::getStatus, Arrays.asList(DonateStatus.CANCEL.status(),
                        DonateStatus.NO_DISPLAY.status(), DonateStatus.PUBLISH_WAIT.status()))
                .list();

        WxUserInfoEntity wxUserInfoEntity = list.get(0);
        String describe;
        if (CollUtil.isEmpty(donateSeatApplyEntityList)) {
            describe = DEF_DESCRIBE;
            String res = QRCodeUtil.createMaterielWithQrcode(wxUserInfoEntity.getAvatarUrl(), wxUserInfoEntity.getName(), describe, null);
            return DataResponse.success(res);
        } else {
            describe = "参与共建了"+donateSeatApplyEntityList.size()+"只座椅";
        }
        Random random = new Random();
        DonateSeatApplyEntity donateSeatApplyEntity = donateSeatApplyEntityList.get(random.nextInt(donateSeatApplyEntityList.size()));
        Integer donateId = donateSeatApplyEntity.getDonateId();
        DonateSeatInfoEntity donateSeatInfoEntity = donateSeatInfoMapper.selectById(donateId);
        if (donateSeatInfoEntity == null) {
            log.error("异常!无座椅信息,donateId:{}", donateId);
            return DataResponse.fail("异常!无座椅信息");
        }

        SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(donateSeatInfoEntity.getSeatId());
        if (seatInfoEntity == null) {
            log.error("异常!无座椅信息,donateId:{}", donateId);
            return DataResponse.fail("异常!无座椅信息");
        }

//        Integer seatId = donateSeatInfoEntity.getSeatId();
//        List<SeatImageEntity> seatImageEntityList = new LambdaQueryChainWrapper<>(seatImageMapper)
//                .eq(SeatImageEntity::getSeatId, seatId)
//                .list();
//        if (CollUtil.isEmpty(seatImageEntityList)) {
//            log.error("异常!无座椅信息,donateId:{}", donateId);
//            return DataResponse.fail("异常!无座椅信息");
//        }
//
//        SeatImageEntity seatImageEntity = seatImageEntityList.get(0);
        String imageUrl = fileUploadComponent.getImageUrl(seatInfoEntity.getImage());

        String qrcode = QRCodeUtil.createMaterielWithQrcode(wxUserInfoEntity.getAvatarUrl(), wxUserInfoEntity.getName(), describe, imageUrl);
        return DataResponse.success(qrcode);
    }

    public BaseResponse getCertificateSettings(String openId) {
        List<CertificateSettingsEntity> list = new LambdaQueryChainWrapper<>(certificateSettingsMapper)
                .eq(CertificateSettingsEntity::getStatus, BroadcastStatus.Broad_OPEN.status())
                .eq(CertificateSettingsEntity::getDeleteFlag, Boolean.FALSE)
                .eq(CertificateSettingsEntity::getOpenId, openId)
                .eq(CertificateSettingsEntity::getStatus, ShowStatus.SHOW.status())
                .list();
        List<CertificateSettingsVo> voList = CollUtil.newArrayList();
        list.forEach(item -> {
            CertificateSettingsVo vo = new CertificateSettingsVo();
            BeanCopyUtils.copy(item, vo);
            if (StrUtil.isNotEmpty(item.getImageUrl())) {
                vo.setImageUrl(fileUploadComponent.getImageUrl(item.getImageUrl()));
            } else {
                throw new BusinessException("证书图片不存在");
            }
            voList.add(vo);
        });
        return DataResponse.success(voList);
    }

    public void downloadCertificate(Long id, HttpServletResponse response) throws IOException {
        CertificateSettingsEntity certificateSettingsEntity = certificateSettingsMapper.selectById(id);
        if (certificateSettingsEntity == null) {
            throw new BusinessException("证书不存在");
        }
        String imageUrl = certificateSettingsEntity.getImageUrl();
        if (StrUtil.isEmpty(imageUrl)) {
            throw new BusinessException("证书图片不存在");
        }
        InputStream inputStream = null;
        try {
            URL url= new URL(fileUploadComponent.getImageUrl(imageUrl));
            inputStream = url.openStream();
            int available = inputStream.available();
            IoUtil.copy(inputStream, response.getOutputStream(), available);
            response.setContentLength(available);
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
            throw new BusinessException("下载失败,",ExceptionUtil.getMessage(e));
        } finally {
            inputStream.close();
        }
    }

    public BaseResponse creatPoster(UserSeatCareBo bo) {
        try {
            List<WxUserInfoEntity> wxUserInfoEntityList = new LambdaQueryChainWrapper<>(wxUserInfoMapper)
                    .eq(WxUserInfoEntity::getOpenId, bo.getOpenId())
                    .list();
            if (CollUtil.isEmpty(wxUserInfoEntityList)) return DataResponse.fail("用户不存在");
            WxUserInfoEntity wxUserInfoEntity = wxUserInfoEntityList.get(0);
            String describe;
            String seatImage;
            String address;
            if (SeatTypeStatus.DONATE.status() == bo.getSeatType()) {
                SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(bo.getSeatId());
                if (seatInfoEntity == null) {
                    log.error("异常!无捐赠座椅信息,seatId:{}", bo.getSeatId());
                    return DataResponse.fail("异常!无座椅信息");
                }
                seatImage = fileUploadComponent.getImageUrl(seatInfoEntity.getImage());

                DonateSeatInfoEntity donateSeatInfoEntity = donateSeatInfoMapper.selectById(bo.getDonateId());
                if (donateSeatInfoEntity == null) {
                    log.error("异常!无捐赠座椅信息,donateId:{}", bo.getDonateId());
                    return DataResponse.fail("异常!无座椅信息");
                }
                address = donateSeatInfoEntity.getAdress();
                describe = "欢迎打卡我共建的座椅！";
            } else if (SeatTypeStatus.ADOPT.status() == bo.getSeatType()) {
                SeatInfoAdoptEntity seatInfoAdoptEntity = seatInfoAdoptMapper.selectById(bo.getSeatId());
                if (seatInfoAdoptEntity == null) {
                    log.error("异常!无认养座椅信息,seatId:{}", bo.getSeatId());
                    return DataResponse.fail("异常!无座椅信息");
                }
                seatImage = fileUploadComponent.getImageUrl(seatInfoAdoptEntity.getImage());

                AdoptSeatInfoEntity adoptSeatInfoEntity = adoptSeatInfoMapper.selectById(bo.getAdoptId());
                if (adoptSeatInfoEntity == null) {
                    log.error("异常!无认养座椅信息,adoptId:{}", bo.getAdoptId());
                    return DataResponse.fail("异常!无座椅信息");
                }
                address = adoptSeatInfoEntity.getAdress();
                describe = "欢迎打卡我认养的座椅！";
            } else {
                return DataResponse.fail("座椅类型错误");
            }

            //获取接口调用凭证access_token
            String appId = ProjectConfig.APP_ID;//小程序id
            String appKey = ProjectConfig.APP_KEY;//小程序密钥
            String token = qrCodeService.postToken(appId, appKey);

            //生成二维码
            String scene = null;
            if (bo.getDonateId() == null) {
                scene = bo.getSeatType() + bo.getAdoptId().toString();
            } else if (bo.getAdoptId() == null){
                scene = bo.getSeatType() + bo.getDonateId().toString();
            } else {
                throw new BusinessException("donateId或adoptId缺失");
            }
            BufferedInputStream in = qrCodeService.generateQrCode( "pages/chairInformation/chairInformation",  scene, token);
            BufferedImage qrCodeImage = ImageIO.read(in);
            in.close();
            String result = QRCodeUtil.createMaterielWithQrcode(wxUserInfoEntity.getAvatarUrl(), wxUserInfoEntity.getName(), describe, seatImage, address, qrCodeImage);
            return DataResponse.success(result);
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
            return DataResponse.fail(MessageConstants.MESSAGE_E0083);
        }

    }

}
