package cn.xzxx.seats.wechat.controller;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.wechat.bo.DonateCommentBo;
import cn.xzxx.seats.wechat.bo.DonateInfobo;
import cn.xzxx.seats.wechat.service.DonateService;
import cn.xzxx.seats.wechat.vo.DonateSeatApplyVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * 共建业务
 */
@RestController
@RequestMapping("wechat")
public class DonateController {

    @Resource
    private DonateService donateService;

    /**
     * 获取捐赠座椅列表
     * @param bo
     * @return
     */
    @PostMapping("getDonateInfo")
    public BaseResponse getDonateInfo(@RequestBody DonateInfobo bo) {
        return donateService.getDonateSeatInfo(bo);
    }

    /**
     * 获取捐赠座椅及捐赠人信息
     * @param id
     * @return
     */
    @GetMapping("getDonateInfoAndDonateUsers")
    public BaseResponse getDonateInfoAndDonateUsers(@RequestParam Integer id) {
        return donateService.getDonateInfoAndDonateUsers(id);
    }

    /**
     * 获取所有捐赠座椅(按照区划分)
     */
    @GetMapping("getDonateSeatInfo")
    public BaseResponse getDonateSeatInfo(@RequestParam(required = false) String strict,
                                          @RequestParam(required = false) String openId,
                                          @RequestParam(required = false) Integer specialType) {
        return donateService.getDonateSeatInfoByStrict(strict, openId, specialType);
    }

    /**
     * 获取捐赠记录
     * @param openId
     * @return
     */
    @GetMapping("getDonateRecords")
    public BaseResponse getDonateRecords(@RequestParam String openId,
                                         @RequestParam(required = false) Boolean passFlag,
                                         @RequestParam(required = false) Integer specialType) {
        return donateService.getDonateRecords(openId, passFlag, specialType);
    }

    /**
     * 捐赠座椅
     * @param donateSeatApplyVo
     * @return
     */
    @PostMapping("donateSeat")
    public BaseResponse donateSeat(@RequestBody @Validated DonateSeatApplyVo donateSeatApplyVo) {
        return donateService.donateSeat(donateSeatApplyVo);
    }

    /**
     * 修改捐赠记录
     * @param donateSeatApplyVo
     * @return
     */
    @PostMapping("updateDonateApply")
    public BaseResponse updateDonateApply(@RequestBody @Validated DonateSeatApplyVo donateSeatApplyVo) {
        if (donateSeatApplyVo == null || donateSeatApplyVo.getId() == null) {
            return MessageResponse.newInstance("入参异常!");
        }
        return donateService.updateDonateApply(donateSeatApplyVo);
    }


    /**
     * 确认捐赠份额
     * @param donateSeatApplyVo
     * @return
     */
    @PostMapping("confirmDonateShare")
    public BaseResponse confirmDonateShare(@RequestBody DonateSeatApplyVo donateSeatApplyVo) {
        if (donateSeatApplyVo == null || donateSeatApplyVo.getId() == null) {
            return MessageResponse.newInstance("入参异常!");
        }
        return donateService.confirmDonateShare(donateSeatApplyVo);
    }

    /**
     * 评论座椅
     * @param bo
     * @return
     */
    @PostMapping("commentDonateSeat")
    public BaseResponse commentDonateSeat(@RequestBody DonateCommentBo bo) {
        return donateService.commentDonateSeat(bo);
    }

    /**
     * 删除评论
     * @param id
     * @return
     */
    @DeleteMapping("/deleteComment/{id}")
    public BaseResponse deleteComment(@NotNull(message = "评论id不能为空")
            @PathVariable Integer id) {
        return donateService.deleteComment(id);
    }

    /**
     * 根据捐赠id查询评论
     * @param donateId
     * @return
     */
    @GetMapping("/listCommentsByDonateId/{donateId}")
    public BaseResponse listCommentsByDonateId(@NotNull(message = "捐赠id不能为空")
            @PathVariable Integer donateId) {
        return donateService.listCommentsByDonateId(donateId);
    }


}
