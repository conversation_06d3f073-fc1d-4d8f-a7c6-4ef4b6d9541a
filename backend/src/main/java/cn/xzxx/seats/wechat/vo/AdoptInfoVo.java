package cn.xzxx.seats.wechat.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdoptInfoVo {

    /**
     * 可认养座椅ID
     */
    private Integer id;

    /**
     * 座椅ID
     */
    private Integer seatId;

    /**
     * 座椅缩略图
     */
    private String image;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

    /**
     * 座椅大小
     */
    private String size;

    /**
     * 座椅材质
     */
    private String material;

    /**
     * 座椅介绍
     */
    private String introduce;

    /**
     * 座椅价格 单位(元)
     */
    private Integer price;

    /**
     * 认养金额
     */
    private Integer adoptPrice;

    /**
     * 发布开始时间
     */
    private String startTime;

//    /**
//     * 发布结束时间
//     */
//    private LocalDateTime endTime;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 区划代码
     */
    private String code;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * GPS经度
     */
    private Double gpsLng;

    /**
     * GPS维度
     */
    private Double gpsLat;

    /**
     * 捐赠状态 1:未发布 2:已发布(待认养) 3:被认养
     */
    private Integer status;

    /**
     * 图片详细图
     */
    private List<String> imageDetail;

    /**
     * 捐赠人信息
     */
    private List<AdoptUserVo> userVoList;

    /**
     * 养护人信息
     */
    private List<AdoptMaintainerVo> adoptMaintainerVoList;

    /**
     * 建造单位
     */
    private String ownerUnit;

    /**
     * 施工单位
     */
    private String constructUnit;

    /**
     * 是否本人认养
     */
    private Boolean ownerFlag;

    /**
     * 特殊标识 0-全部 1-浦东新区专用
     */
    private Integer specialType;

}
