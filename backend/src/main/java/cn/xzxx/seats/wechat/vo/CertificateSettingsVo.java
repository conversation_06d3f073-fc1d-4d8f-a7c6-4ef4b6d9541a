package cn.xzxx.seats.wechat.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CertificateSettingsVo {

    /**
     * 证书ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 证书编号
     */
    private String certNo;

    /**
     * 证书URL
     */
    private String imageUrl;

    /**
     * 用户open_id
     */
    private String openId;

    /**
     * 座椅分类 1-共建 2-认养
     */
    private Integer seatType;

    /**
     * 捐赠/认养座椅ID
     */
    private Integer applyId;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

}
