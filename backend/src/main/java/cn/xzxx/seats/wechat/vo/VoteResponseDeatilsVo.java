package cn.xzxx.seats.wechat.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoteResponseDeatilsVo {
//    /**
//     * 投票回答详情ID
//     */
//    private Integer id;

    /**
     * 微信ID
     */
    private String openId;

    /**
     * 投票ID
     */
    private Integer voteId;

    /**
     * 投票反馈ID
     */
    private Integer responseId;

    /**
     * 题目ID
     */
    private Integer questionId;

    /**
     * 问题种类
     */
    private String type;

    /**
     * 用户回答_选项
     */
    private Integer userOption;

    /**
     * 实时票数
     */
    private Integer voteNum;
}
