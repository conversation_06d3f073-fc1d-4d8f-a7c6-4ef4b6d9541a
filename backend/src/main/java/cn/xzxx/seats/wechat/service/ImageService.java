package cn.xzxx.seats.wechat.service;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.component.FileUploadComponent;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@Service
public class ImageService {

    @Resource
    private FileUploadComponent fileUploadComponent;

    /**
     * 上传照片
     * @param file
     * @return
     */
    public BaseResponse uploadImage(MultipartFile file) {
        return DataResponse.success(fileUploadComponent.add(file));
    }

    /**
     * 删除照片
     * @param image key
     */
    public void deleteImage(String image) {
        fileUploadComponent.delete(image);
    }
}
