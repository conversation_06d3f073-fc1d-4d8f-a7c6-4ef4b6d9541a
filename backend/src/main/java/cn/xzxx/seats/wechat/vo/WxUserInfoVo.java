package cn.xzxx.seats.wechat.vo;

import cn.xzxx.seats.code.UserIdentityStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxUserInfoVo {

    /**
     * 用户ID
     */
    private Integer id;

    /**
     * 微信ID
     */
    private String openId;

    /**
     * 微信昵名
     */
    private String name;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 微信头像
     */
    private String avatarUrl;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 是否可以推送
     */
    private Integer sendFlg;

    /**
     * 用户状态
     */
    private Integer wxStatus;

    /**
     * 身份 共建者/认养者 游客模式 养护人员
     */
    private UserIdentityStatus identityStatus;
}
