package cn.xzxx.seats.wechat.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.xzxx.seats.code.DonateApplyStatus;
import cn.xzxx.seats.code.DonateCommentStatus;
import cn.xzxx.seats.code.DonateStatus;
import cn.xzxx.seats.code.SeatTypeStatus;
import cn.xzxx.seats.common.constants.ICommonConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.common.utils.BeanCopierUtil;
import cn.xzxx.seats.common.utils.BeanCopyUtils;
import cn.xzxx.seats.common.utils.DateUtils;
import cn.xzxx.seats.common.utils.StreamUtils;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.*;
import cn.xzxx.seats.wechat.bo.DonateCommentBo;
import cn.xzxx.seats.wechat.bo.DonateInfobo;
import cn.xzxx.seats.wechat.vo.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class DonateService {

    private final DonateSeatInfoMapper donateSeatInfoMapper;
    private final DonateSeatApplyMapper donateSeatApplyMapper;
    private final SeatInfoMapper seatInfoMapper;
    private final SeatImageMapper seatImageMapper;
    private final FileUploadComponent fileUploadComponent;
    private final SysAreaMapper sysAreaMapper;
    private final DonateCommentMapper commentMapper;
    private final AdoptUpkeepMapper adoptUpkeepMapper;

    /**
     * 获取捐赠座椅列表
     * @param bo
     * @return
     */
    public BaseResponse getDonateSeatInfo(DonateInfobo bo) {
        Integer count = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
                // TODO 捐赠状态 -1:不显示 0:已取消 1:未发布 2:已发布 3:捐赠中 4:捐赠完成 5:建造中 6:建造完成
                .in(DonateSeatInfoEntity::getStatus, Stream.of(2, 3, 4, 5, 6, 7).collect(Collectors.toList()))
                .gt(bo.getId() != null, DonateSeatInfoEntity::getId, bo.getId())
                .eq(bo.getSpecialType() != null, DonateSeatInfoEntity::getSpecialType, bo.getSpecialType())
                .eq(DonateSeatInfoEntity::getDeleteFlag, Boolean.FALSE)
                .like(StrUtil.isNotBlank(bo.getAdress()), DonateSeatInfoEntity::getAdress, bo.getAdress())
                .eq(StrUtil.isNotBlank(bo.getStrict()), DonateSeatInfoEntity::getStrict, bo.getStrict())
                .orderByDesc(DonateSeatInfoEntity::getValidTime, DonateSeatInfoEntity::getCreatedAt)
                .count();
        DonateInfoListVo donateInfoListVo = new DonateInfoListVo();
        if (count <= Integer.parseInt(ICommonConstants.LIMIT_SQL_ROWNUM)) {
            donateInfoListVo.setIsFinishFlag(true);
        } else {
            donateInfoListVo.setIsFinishFlag(false);
        }
        List<DonateSeatInfoEntity> donateSeatInfoList = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
                // TODO 捐赠状态 -1:不显示 0:已取消 1:未发布 2:已发布 3:捐赠中 4:捐赠完成 5:建造中 6:建造完成
                .in(DonateSeatInfoEntity::getStatus, Stream.of(2,3,4,5,6,7).collect(Collectors.toList()))
                .gt(bo.getId() != null, DonateSeatInfoEntity::getId, bo.getId())
                .eq(bo.getSpecialType() != null, DonateSeatInfoEntity::getSpecialType, bo.getSpecialType())
                .eq(DonateSeatInfoEntity::getDeleteFlag, Boolean.FALSE)
                .eq(StrUtil.isNotBlank(bo.getStrict()), DonateSeatInfoEntity::getStrict, bo.getStrict())
                .like(StrUtil.isNotBlank(bo.getAdress()), DonateSeatInfoEntity::getAdress, bo.getAdress())
                .orderByAsc(DonateSeatInfoEntity::getValidTime, DonateSeatInfoEntity::getCreatedAt)
                .last("limit "+ ICommonConstants.LIMIT_SQL_ROWNUM)
                .list();
        if (CollectionUtils.isEmpty(donateSeatInfoList)) return new BaseResponse();

        // 计算捐赠份额
        AtomicReference<Map<Integer, Integer>> donateSeatApplyMapRef = countDonateShareNum(donateSeatInfoList);

        // 获取座椅缩略图
        List<Integer> seatIdList = donateSeatInfoList.stream().map(DonateSeatInfoEntity::getSeatId).collect(Collectors.toList());
        List<SeatInfoEntity> seatInfoList = new LambdaQueryChainWrapper<>(seatInfoMapper)
                .in(SeatInfoEntity::getId, seatIdList)
                .list();
        Map<Integer, SeatInfoEntity> seatInfoMap = seatInfoList.stream()
                .collect(Collectors.toMap(SeatInfoEntity::getId, Function.identity()));

        // 获取座椅详细图
        List<SeatImageEntity> seatImageEntityList = new LambdaQueryChainWrapper<>(seatImageMapper)
                .in(SeatImageEntity::getSeatId, seatIdList)
                .list();
        Map<Integer, List<SeatImageEntity>> seatImageMap = null;
        if (CollectionUtils.isNotEmpty(seatImageEntityList)) {
            seatImageMap = seatImageEntityList.stream().collect(Collectors.groupingBy(SeatImageEntity::getSeatId));
        }
        Map<Integer, List<SeatImageEntity>> finalSeatImageMap = seatImageMap;
        List<DonateInfoVo> donateInfoVoList = new ArrayList<>();
        donateSeatInfoList.forEach(donateSeatInfo -> {
            DonateInfoVo donateInfoVo = new DonateInfoVo();
            BeanCopierUtil.copy(donateSeatInfo, donateInfoVo);
            SeatInfoEntity seatInfoEntity = seatInfoMap.get(donateSeatInfo.getSeatId());
            if (seatInfoEntity != null) {
                donateInfoVo.setImage(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
                donateInfoVo.setSeatNo(seatInfoEntity.getSeatNo());
                donateInfoVo.setPrice(seatInfoEntity.getPrice());
                donateInfoVo.setValidTime(DateUtils.localDateTimeFormat(donateSeatInfo.getValidTime()));
                donateInfoVo.setSeatName(seatInfoEntity.getName());
                donateInfoVo.setMaterial(seatInfoEntity.getMaterial());
                donateInfoVo.setSize(seatInfoEntity.getSize());
                donateInfoVo.setIntroduce(seatInfoEntity.getIntroduce());
            }
            if (MapUtil.isNotEmpty(finalSeatImageMap)) {
                List<SeatImageEntity> imageEntityList = finalSeatImageMap.get(donateSeatInfo.getSeatId());
                if (CollectionUtils.isNotEmpty(imageEntityList)) {
                    donateInfoVo.setImageDetail(seatImageEntityList.stream().map(t -> fileUploadComponent.getImageUrl(t.getImage())).collect(Collectors.toList()));
                }
            }
            setShareNum(donateSeatApplyMapRef, donateSeatInfo, donateInfoVo);
            donateInfoVoList.add(donateInfoVo);
        });
        donateInfoListVo.setDonateInfoVoList(donateInfoVoList);
        return DataResponse.success(donateInfoListVo);
    }

    /**
     * 获取捐赠座椅及捐赠人信息
     * @param id
     * @return
     */
    public BaseResponse getDonateInfoAndDonateUsers(Integer id) {
        DonateSeatInfoEntity donateSeatInfoEntity = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
                .eq(DonateSeatInfoEntity::getId, id)
                .one();
        if (donateSeatInfoEntity == null) {
            return DataResponse.fail("无捐赠座椅信息");
        }
        SeatInfoEntity seatInfoEntity = new LambdaQueryChainWrapper<>(seatInfoMapper)
                .eq(SeatInfoEntity::getId, donateSeatInfoEntity.getSeatId())
                .one();
        if (seatInfoEntity == null) {
            return DataResponse.fail("无捐赠座椅信息");
        }
        DonateInfoVo donateInfoVo = new DonateInfoVo();
        BeanCopierUtil.copy(donateSeatInfoEntity, donateInfoVo);
        // 填充座椅信息
        donateInfoVo.setSeatNo(seatInfoEntity.getSeatNo());
        donateInfoVo.setSeatName(seatInfoEntity.getName());
        donateInfoVo.setSize(seatInfoEntity.getSize());
        donateInfoVo.setMaterial(seatInfoEntity.getMaterial());
        donateInfoVo.setImage(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
        donateInfoVo.setIntroduce(seatInfoEntity.getIntroduce());
        donateInfoVo.setPrice(seatInfoEntity.getPrice());
        donateInfoVo.setOwnerUnit(seatInfoEntity.getOwnerUnit());
        donateInfoVo.setConstructUnit(seatInfoEntity.getConstructUnit());
        // 填充捐赠人信息
        List<DonateSeatApplyEntity> seatApplyEntityList = new LambdaQueryChainWrapper<>(donateSeatApplyMapper)
                .eq(DonateSeatApplyEntity::getDonateId, id)
                .orderByDesc(DonateSeatApplyEntity::getCreatedAt)
                .list();
        List<DonateUserVo> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(seatApplyEntityList)) {
            seatApplyEntityList.forEach(donateSeatApplyEntity -> {
                DonateUserVo donateUserVo = new DonateUserVo();
                BeanCopierUtil.copy(donateSeatApplyEntity, donateUserVo);
                donateUserVo.setDonateApplyId(donateSeatApplyEntity.getId());
                if (donateUserVo.getDonationWordFlag() != null && donateUserVo.getDonationWordFlag()) {
                    donateUserVo.setDonationWord(donateSeatApplyEntity.getDonationWord());
                } else {
                    donateUserVo.setDonationWord("");
                }
                list.add(donateUserVo);
            });
        }
        donateInfoVo.setUserVoList(list);
        // 填充养护人信息
        List<AdoptMaintainerVo> list1 = new ArrayList<>();
        List<AdoptUpkeepEntity> upkeepEntityList = new LambdaQueryChainWrapper<>(adoptUpkeepMapper)
                .eq(AdoptUpkeepEntity::getDonateId, donateSeatInfoEntity.getId())
                .eq(AdoptUpkeepEntity::getSeatType, SeatTypeStatus.DONATE.status())
                .list();
        if (CollectionUtils.isNotEmpty(upkeepEntityList)) {
            list1 = BeanCopyUtils.copyList(upkeepEntityList, AdoptMaintainerVo.class);
        }
        donateInfoVo.setAdoptMaintainerVoList(list1);
        return DataResponse.success(donateInfoVo);
    }

    /**
     * 获取所有捐赠座椅
     * @return
     */
    public BaseResponse getDonateSeatInfoByStrict(String strict, String openId, Integer specialType) {
        // 根据中文区名称获取区code
//        String idDistrict = null;
//        if (StringUtils.isNotBlank(strict)) {
//            List<SysAreaEntity> list = new LambdaQueryChainWrapper<>(sysAreaMapper)
//                    .eq(SysAreaEntity::getNameDistrict, strict)
//                    .list();
//            if (CollectionUtils.isNotEmpty(list)) { idDistrict = list.get(0).getIdDistrict(); }
//        }
        List<DonateSeatInfoEntity> donateSeatInfoList = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
                .eq(StringUtils.isNotBlank(strict), DonateSeatInfoEntity::getStrict, strict)
                //TODO 捐赠状态 0:已取消 1:未发布 2:已发布 3:捐赠中 4:捐赠完成 5:建造中 6:建造完成
                .in(DonateSeatInfoEntity::getStatus, Stream.of(2,3,4,5,6,7).collect(Collectors.toList()))
                .eq(DonateSeatInfoEntity::getDeleteFlag, Boolean.FALSE)
                .eq(specialType != null, DonateSeatInfoEntity::getSpecialType, specialType)
                .orderByDesc(DonateSeatInfoEntity::getCreatedAt)
                .list();
        if (CollectionUtils.isEmpty(donateSeatInfoList)) return new BaseResponse();

        Map<Integer, DonateSeatApplyEntity> map;
        if (StrUtil.isNotBlank(openId)) {
            List<DonateSeatApplyEntity> donateSeatApplyEntityList = new LambdaQueryChainWrapper<>(donateSeatApplyMapper)
                    .eq(DonateSeatApplyEntity::getOpenId, openId)
                    .eq(DonateSeatApplyEntity::getDeleteFlag, Boolean.FALSE)
                    .list();
            if (CollUtil.isNotEmpty(donateSeatApplyEntityList)) {
                map = StreamUtils.toMap(donateSeatApplyEntityList, DonateSeatApplyEntity::getDonateId, Function.identity());
            } else {
                map = null;
            }
        } else {
            map = null;
        }

        // 计算捐赠份额
        AtomicReference<Map<Integer, Integer>> donateSeatApplyMapRef = countDonateShareNum(donateSeatInfoList);

        List<Integer> seatIdList = donateSeatInfoList.stream().map(DonateSeatInfoEntity::getSeatId).collect(Collectors.toList());
        Map<Integer, SeatInfoEntity> seatInfoEntityMap = new LambdaQueryChainWrapper<>(seatInfoMapper)
                .in(SeatInfoEntity::getId, seatIdList)
                .list()
                .stream()
                .collect(Collectors.toMap(SeatInfoEntity::getId, Function.identity()));
        List<DonateInfoVo> donateInfoVoList = new ArrayList<>();
        donateSeatInfoList.forEach(donateSeatInfoEntity -> {
            DonateInfoVo donateInfoVo = new DonateInfoVo();
            BeanCopierUtil.copy(donateSeatInfoEntity, donateInfoVo);
            donateInfoVo.setValidTime(DateUtils.localDateTimeFormat(donateSeatInfoEntity.getValidTime()));
            SeatInfoEntity seatInfoEntity = seatInfoEntityMap.get(donateSeatInfoEntity.getSeatId());
            setShareNum(donateSeatApplyMapRef, donateSeatInfoEntity, donateInfoVo);
            if (seatInfoEntity != null) {
                donateInfoVo.setSeatNo(seatInfoEntity.getSeatNo());
                donateInfoVo.setImage(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
                donateInfoVo.setSize(seatInfoEntity.getSize());
                donateInfoVo.setMaterial(seatInfoEntity.getMaterial());
                donateInfoVo.setIntroduce(seatInfoEntity.getIntroduce());
                donateInfoVo.setPrice(seatInfoEntity.getPrice());
                donateInfoVo.setSeatName(seatInfoEntity.getName());
                donateInfoVo.setStartTime(DateUtils.localDateTimeFormat(seatInfoEntity.getStartTime()));
            }
            if (map != null) {
                if (MapUtil.isNotEmpty(map) && map.containsKey(donateSeatInfoEntity.getId())) {
                    donateInfoVo.setOwnerFlag(Boolean.TRUE);
                }
            }
            donateInfoVoList.add(donateInfoVo);
        });
        return DataResponse.success(donateInfoVoList);
    }

    /**
     * 我的捐赠
     * @param openId
     */
    public BaseResponse getDonateRecords(String openId, Boolean passFlag, Integer specialType) {
        List<DonateSeatApplyEntity> donateSeatApplyList = new LambdaQueryChainWrapper<>(donateSeatApplyMapper)
                .eq(DonateSeatApplyEntity::getOpenId, openId)
                .eq(DonateSeatApplyEntity::getDeleteFlag, Boolean.FALSE)
                .eq(specialType != null, DonateSeatApplyEntity::getSpecialType, specialType)
                // 区分我的所有记录和审核通过的记录
                .in(passFlag != null && passFlag, DonateSeatApplyEntity::getStatus, Stream.of(DonateApplyStatus.APPLY_PASS.status(), DonateApplyStatus.APPLY_SHARE_WAIT.status(), DonateApplyStatus.APPLY_SHARE_CONFIRM.status())
                        .collect(Collectors.toList()))
                .list();
        if (CollectionUtils.isEmpty(donateSeatApplyList)) return MessageResponse.newInstance("您还没有捐赠的座椅");

        // 获取座椅缩略图和详细图列表关联表过多 不做多个map集匹配
        List<DonateSeatApplyVo> donateSeatApplyVoList = new ArrayList<>();
        donateSeatApplyList.forEach(donateSeatApply -> {
            DonateSeatApplyVo donateSeatApplyVo = new DonateSeatApplyVo();
            BeanCopierUtil.copy(donateSeatApply, donateSeatApplyVo);
            // 申请时间和申请通过时间
            if (donateSeatApply.getApplyTime() != null) donateSeatApplyVo.setApplyTime(DateUtils.localDateTimeFormat(donateSeatApply.getApplyTime()));
            if (donateSeatApply.getApplyPassTime() != null) donateSeatApplyVo.setApplyPassTime(DateUtils.localDateTimeFormat(donateSeatApply.getApplyPassTime()));

            // 身份证明 资质证明图片url
            if (StringUtils.isNotBlank(donateSeatApply.getPersonCertificateImage())) {
                donateSeatApplyVo.setPersonCertificateImage(fileUploadComponent.getImageUrl(donateSeatApply.getPersonCertificateImage()));
            }
            if (StringUtils.isNotBlank(donateSeatApply.getAptitudeCertificateImage())) {
                donateSeatApplyVo.setAptitudeCertificateImage(fileUploadComponent.getImageUrl(donateSeatApply.getAptitudeCertificateImage()));
            }

            DonateSeatInfoEntity donateSeatInfoEntity = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
                    .eq(DonateSeatInfoEntity::getId, donateSeatApply.getDonateId())
                    .one();
            if (donateSeatInfoEntity != null) {
                donateSeatApplyVo.setSeatId(donateSeatInfoEntity.getSeatId());
                donateSeatApplyVo.setDonateStatus(donateSeatInfoEntity.getStatus());
                BeanCopyUtils.copy(donateSeatInfoEntity, donateSeatApplyVo);
                donateSeatApplyVo.setStatus(donateSeatApply.getStatus());
                // 获取座椅缩略图
                SeatInfoEntity seatInfo = new LambdaQueryChainWrapper<>(seatInfoMapper)
                        .in(SeatInfoEntity::getId, donateSeatInfoEntity.getSeatId())
                        .one();
                if (seatInfo != null) {
                    donateSeatApplyVo.setSeatNo(seatInfo.getSeatNo());
                    donateSeatApplyVo.setMaterial(seatInfo.getMaterial());
                    donateSeatApplyVo.setSize(seatInfo.getSize());
                    donateSeatApplyVo.setSeatName(seatInfo.getName());
                    donateSeatApplyVo.setImage(fileUploadComponent.getImageUrl(seatInfo.getImage()));
                    donateSeatApplyVo.setPrice(seatInfo.getPrice());
                    // 获取座椅详细图列表
                    List<SeatImageEntity> seatImageEntityList = new LambdaQueryChainWrapper<>(seatImageMapper)
                            .in(SeatImageEntity::getSeatId, seatInfo.getId())
                            .list();
                    if (CollUtil.isNotEmpty(seatImageEntityList)) {
                        List<String> list = seatImageEntityList.stream()
                                .map(t -> fileUploadComponent.getImageUrl(t.getImage()))
                                .collect(Collectors.toList());
                        list.add(donateSeatApplyVo.getImage());
                        donateSeatApplyVo.setImageDetail(list);
                    }
                }
            }
            donateSeatApplyVo.setId(donateSeatApply.getId());
            donateSeatApplyVoList.add(donateSeatApplyVo);
        });
        return DataResponse.success(donateSeatApplyVoList);
    }

    /**
     * 共建座椅
     * @param donateSeatApplyVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse donateSeat(DonateSeatApplyVo donateSeatApplyVo) {
        DonateSeatInfoEntity donateSeatInfo = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
                .eq(DonateSeatInfoEntity::getId, donateSeatApplyVo.getDonateId())
                .one();
        if (donateSeatInfo == null) return MessageResponse.newInstance("座椅不存在!");
        if (DonateStatus.APPLY_FINISH.status() == donateSeatInfo.getStatus()) {
            return MessageResponse.newInstance("该座椅已经被满额申请了，无法再次提交新的申请!");
        }

        SeatInfoEntity seatInfoEntity = new LambdaQueryChainWrapper<>(seatInfoMapper)
                .eq(SeatInfoEntity::getId, donateSeatInfo.getSeatId())
                .one();
        if (seatInfoEntity == null) return MessageResponse.newInstance("座椅不存在!");

        List<DonateSeatApplyEntity> donateSeatApplyList = new LambdaQueryChainWrapper<>(donateSeatApplyMapper)
                .eq(DonateSeatApplyEntity::getDonateId, donateSeatApplyVo.getDonateId())
//                .eq(DonateSeatApplyEntity::getOpenId, donateSeatApplyVo.getOpenId())
                // 捐赠状态 1:申请中 2:捐赠审核通过 3:不通过 4:无效
//                .eq(DonateSeatApplyEntity::getStatus, DonateApplyStatus.APPLY_PASS.status())
                .eq(DonateSeatApplyEntity::getDeleteFlag, Boolean.FALSE)
                .list();
        // 当前捐赠的总份额
        int shareNum = 0;
        if (CollectionUtils.isNotEmpty(donateSeatApplyList)) {
            shareNum = donateSeatApplyList.stream()
                    .filter(item -> item.getStatus().equals(DonateApplyStatus.APPLY_PASS.status())
                            || item.getStatus().equals(DonateApplyStatus.APPLY_SHARE_WAIT.status())
                            || item.getStatus().equals(DonateApplyStatus.APPLY_SHARE_CONFIRM.status()))
                    .mapToInt(DonateSeatApplyEntity::getShareNum).sum();
        }

        LocalDateTime validTime = donateSeatInfo.getValidTime();
        // 截止时间已过
        if (LocalDateTime.now().isAfter(validTime)) {
            if (shareNum > 100) {
                return MessageResponse.newInstance("份额已满,不能再捐赠!");
            }
        }

        DonateSeatApplyEntity donateSeatApply = new DonateSeatApplyEntity();
        BeanCopierUtil.copy(donateSeatApplyVo, donateSeatApply);
        donateSeatApply.setId(null);
        donateSeatApply.setStatus(DonateApplyStatus.APPLY_WAIT.status());
        donateSeatApply.setApplyTime(LocalDateTime.now());
        donateSeatApply.setUpdatedBy("System");
        donateSeatApplyMapper.insert(donateSeatApply);
        // 如果申请份额超过100份满份额，修改座椅的状态为被满额申请
        if (donateSeatApplyVo.getShareNum() + shareNum >= 100) {
            donateSeatInfo.setStatus(DonateStatus.APPLY_FINISH.status());
            donateSeatInfoMapper.updateById(donateSeatInfo);
        }
        return new BaseResponse();
    }

    /**
     * 更新捐赠信息
     * @param donateSeatApplyVo
     * @return
     */
    public BaseResponse updateDonateApply(DonateSeatApplyVo donateSeatApplyVo) {
        DonateSeatApplyEntity donateSeatApplyEntity = new LambdaQueryChainWrapper<>(donateSeatApplyMapper)
                .eq(DonateSeatApplyEntity::getId, donateSeatApplyVo.getId())
                .one();
        if (donateSeatApplyEntity == null) return MessageResponse.newInstance("该申请记录不存在!");

        // 捐赠状态 1:申请中 2:捐赠审核通过 3:不通过 4:无效
        if (donateSeatApplyEntity.getStatus() == 2) {
            return MessageResponse.newInstance("捐赠审核已通过!无法更改");
        } else {
            DonateSeatApplyEntity donateSeatApply = new DonateSeatApplyEntity();
            BeanCopierUtil.copy(donateSeatApplyVo, donateSeatApply);
            donateSeatApply.setStatus(1);
            donateSeatApply.setApplyTime(LocalDateTime.now());
            donateSeatApply.setUpdatedBy("System");

            // 证件url处理 如果入参为空表示不更新
            if (StringUtils.isBlank(donateSeatApplyVo.getPersonCertificateImage())) {
                donateSeatApply.setPersonCertificateImage(donateSeatApplyEntity.getPersonCertificateImage());
            }
            if (StringUtils.isBlank(donateSeatApplyVo.getAptitudeCertificateImage())) {
                donateSeatApply.setAptitudeCertificateImage(donateSeatApplyEntity.getAptitudeCertificateImage());
            }

            if (donateSeatApplyEntity.getStatus() == 1) {
                donateSeatApplyMapper.updateById(donateSeatApply);
            } else {
                donateSeatApply.setId(null);
                donateSeatApplyMapper.insert(donateSeatApply);
            }
        }
        return new BaseResponse();
    }

    /**
     * 确认捐赠份额
     * @param donateSeatApplyVo
     * @return
     */
    public BaseResponse confirmDonateShare(DonateSeatApplyVo donateSeatApplyVo) {
        DonateSeatApplyEntity donateSeatApplyEntity = new LambdaQueryChainWrapper<>(donateSeatApplyMapper)
                .eq(DonateSeatApplyEntity::getId, donateSeatApplyVo.getId())
                .one();
        if (donateSeatApplyEntity == null) return MessageResponse.newInstance("该申请记录不存在!");

//        if (donateSeatApplyEntity.getStatus() != 5) return MessageResponse.newInstance("该申请不在确认阶段");
        new LambdaUpdateChainWrapper<>(donateSeatApplyMapper)
                .eq(DonateSeatApplyEntity::getId, donateSeatApplyVo.getId())
                .set(DonateSeatApplyEntity::getStatus, DonateApplyStatus.APPLY_SHARE_CONFIRM.status())
                .update();
        return new BaseResponse();
    }

    /**
     * 评论座椅
     * @param bo
     * @return
     */
    public BaseResponse commentDonateSeat(DonateCommentBo bo) {
        DonateCommentEntity add = BeanCopierUtil.toBean(bo, DonateCommentEntity.class);
        add.setUpdatedBy("System");
        boolean flag = commentMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            return DataResponse.success(bo);
        }
        return new BaseResponse(ICommonConstants.RESPONSE_STATUS_CODE_FAILED_BIZ);
    }

    /**
     * 删除评论
     * @param id
     * @return
     */
    public BaseResponse deleteComment(Integer id) {
        boolean flag = commentMapper.deleteById(id) > 0;
        if (flag) {
            return new BaseResponse();
        }
        return new BaseResponse(ICommonConstants.RESPONSE_STATUS_CODE_FAILED_BIZ);
    }

    /**
     * 根据捐赠id查询评论
     * @param donateId
     * @return
     */
    public BaseResponse listCommentsByDonateId(Integer donateId) {
        List<DonateCommentEntity> donateCommentEntityList = commentMapper.selectList(new LambdaQueryWrapper<DonateCommentEntity>()
                .eq(DonateCommentEntity::getDonateId, donateId)
                .eq(DonateCommentEntity::getStatus, DonateCommentStatus.APPLY_PASS.status()));
        List<DonateCommentVo> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(donateCommentEntityList)) {
            list = BeanCopyUtils.copyList(donateCommentEntityList, DonateCommentVo.class);
        }
        return DataResponse.success(list);
    }

    @NotNull
    private AtomicReference<Map<Integer, Integer>> countDonateShareNum(List<DonateSeatInfoEntity> donateSeatInfoList) {
        List<Integer> donateIdList = donateSeatInfoList.stream().map(DonateSeatInfoEntity::getId).collect(Collectors.toList());
        List<DonateSeatApplyEntity> donateSeatApplyList = new LambdaQueryChainWrapper<>(donateSeatApplyMapper)
                .select(DonateSeatApplyEntity::getId, DonateSeatApplyEntity::getDonateId, DonateSeatApplyEntity::getShareNum)
                // 捐赠状态 1:申请中 2:捐赠审核通过 3:不通过 4:无效
                .in(DonateSeatApplyEntity::getStatus, Stream.of(2,5,6).collect(Collectors.toList()))
                .eq(DonateSeatApplyEntity::getDeleteFlag, Boolean.FALSE)
                .in(DonateSeatApplyEntity::getDonateId, donateIdList)
                .list();
        AtomicReference<Map<Integer, Integer>> donateSeatApplyMapRef = new AtomicReference<>();
        if (CollectionUtils.isNotEmpty(donateSeatApplyList)) {
            Map<Integer, Integer> map = donateSeatApplyList.stream()
                    .collect(Collectors.groupingBy(DonateSeatApplyEntity::getDonateId, Collectors.summingInt(DonateSeatApplyEntity::getShareNum)));
            donateSeatApplyMapRef.set(map);
        }
        return donateSeatApplyMapRef;
    }

    private void setShareNum(AtomicReference<Map<Integer, Integer>> donateSeatApplyMapRef, DonateSeatInfoEntity donateSeatInfo, DonateInfoVo donateInfoVo) {
        if (donateSeatApplyMapRef.get() != null) {
            Integer shareNum = donateSeatApplyMapRef.get().get(donateSeatInfo.getId());
            if (shareNum == null) {
                donateInfoVo.setShareNum(0);
            } else if (shareNum <= 100) {
                donateInfoVo.setShareNum(shareNum);
            } else {
                donateInfoVo.setShareNum(100);
            }
        } else {
            donateInfoVo.setShareNum(0);
        }
    }
}
