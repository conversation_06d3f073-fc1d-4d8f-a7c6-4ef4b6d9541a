package cn.xzxx.seats.wechat.controller;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.wechat.service.VoteService;
import cn.xzxx.seats.wechat.vo.VoteResponseInfoVo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 投票业务
 */
@RestController
@RequestMapping("wechat")
public class VoteController {

    @Resource
    private VoteService voteService;

    /**
     * 获取投票信息列表
     * @param id 主键id 根据id异步加载数据 每次限制20条
     *           eg: id=20 增加检索条件 id>20
     * @return
     */
    @GetMapping("getVoteInfo")
    public BaseResponse getVoteInfo(@RequestParam(required = false) Integer id, @RequestParam String openId) {
        return voteService.getVoteInfo(id, openId);
    }

    /**
     * 获取投票详细信息
     * @param voteId
     * @return
     */
    @GetMapping("getVoteDetails")
    public BaseResponse getVoteDetails(@RequestParam Integer voteId, @RequestParam String openId) {
        return voteService.getVoteDetails(voteId, openId);
    }

    /**
     * 提交投票结果
     * @param voteResponseInfoVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("commitVote")
    public BaseResponse commitVote(@RequestBody @Validated VoteResponseInfoVo voteResponseInfoVo) {
        return voteService.commitVote(voteResponseInfoVo);
    }


}
