package cn.xzxx.seats.wechat.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.xzxx.seats.code.AdoptStatus;
import cn.xzxx.seats.code.DonateStatus;
import cn.xzxx.seats.code.SeatTypeStatus;
import cn.xzxx.seats.common.exception.BusinessException;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.utils.BeanCopyUtils;
import cn.xzxx.seats.common.utils.StreamUtils;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.*;
import cn.xzxx.seats.wechat.bo.UserSeatCareBo;
import cn.xzxx.seats.wechat.vo.UserSeatCareVo;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserSeatCareService {

    private final UserSeatCareMapper userSeatCareMapper;
    private final SeatImageMapper seatImageMapper;
    private final SeatImageAdoptMapper seatImageAdoptMapper;
    private final FileUploadComponent fileUploadComponent;
    private final SeatInfoMapper seatInfoMapper;
    private final SeatInfoAdoptMapper seatInfoAdoptMapper;
    private final DonateSeatInfoMapper donateSeatInfoMapper;
    private final AdoptSeatInfoMapper adoptSeatInfoMapper;

    public BaseResponse getOwnerCare(String openId) {
        List<UserSeatCareEntity> list = new LambdaQueryChainWrapper<>(userSeatCareMapper)
                .eq(UserSeatCareEntity::getOpenId, openId)
                .list();
        List<UserSeatCareVo> voList = CollUtil.newArrayList();
        if (CollUtil.isEmpty(list)) return DataResponse.success(voList);

        // 获取共建座椅信息
        DonateResult donateResult = getDonateResult(list);

        // 获取认养座椅信息
        AdoptResult adoptResult = getAdoptResult(list);

        list.forEach(item -> {
            UserSeatCareVo vo = new UserSeatCareVo();
            if (item.getSeatType() != null && SeatTypeStatus.DONATE.status() == item.getSeatType()) {
                if (MapUtil.isNotEmpty(donateResult.donateSeatInfoMap)) {
                    SeatInfoEntity seatInfoEntity = donateResult.donateSeatInfoMap.get(item.getSeatId());
                    BeanCopyUtils.copy(seatInfoEntity, vo);
                }
                vo.setImage(fileUploadComponent.getImageUrl(vo.getImage()));
                if (MapUtil.isNotEmpty(donateResult.donateImageUrlmap)) {
                    vo.setImageDeatil(donateResult.donateImageUrlmap.get(item.getSeatId()));
                }
                if (MapUtil.isNotEmpty(donateResult.donateIdMap)) {
                    DonateSeatInfoEntity donateSeatInfoEntity = donateResult.donateIdMap.get(item.getSeatId());
                    vo.setDonateId(donateSeatInfoEntity.getId());
                    vo.setStatus(donateSeatInfoEntity.getStatus());
                }
            } else if (SeatTypeStatus.ADOPT.status() == item.getSeatType()) {
                if (MapUtil.isNotEmpty(adoptResult.adoptSeatInfoMap)) {
                    SeatInfoAdoptEntity seatInfoAdoptEntity = adoptResult.adoptSeatInfoMap.get(item.getSeatId());
                    BeanCopyUtils.copy(seatInfoAdoptEntity, vo);
                }
                vo.setImage(fileUploadComponent.getImageUrl(vo.getImage()));
                if (MapUtil.isNotEmpty(adoptResult.adoptImageUrlmap)) {
                    vo.setImageDeatil(adoptResult.adoptImageUrlmap.get(item.getSeatId()));
                }
                if (MapUtil.isNotEmpty(adoptResult.adoptIdMap)) {
                    AdoptSeatInfoEntity adoptSeatInfoEntity = adoptResult.adoptIdMap.get(item.getSeatId());
                    vo.setAdoptId(adoptSeatInfoEntity.getId());
                    vo.setStatus(adoptSeatInfoEntity.getStatus());
                }
            } else {
                throw new BusinessException("异常的座椅分类,不是共建也不是认养");
            }
            vo.setId(item.getId());
            vo.setOpenId(item.getOpenId());
            vo.setSeatType(item.getSeatType());
            vo.setSeatId(item.getSeatId());
            voList.add(vo);
        });

        return DataResponse.success(voList);
    }

    public BaseResponse collectSeat(UserSeatCareBo bo) {
        Integer count = new LambdaQueryChainWrapper<>(userSeatCareMapper)
                .eq(UserSeatCareEntity::getOpenId, bo.getOpenId())
                .eq(UserSeatCareEntity::getSeatId, bo.getSeatId())
                .eq(UserSeatCareEntity::getSeatType, bo.getSeatType())
                .count();
        if (count > 0) {
            return DataResponse.fail("已经收藏过了");
        }
        UserSeatCareEntity entity = new UserSeatCareEntity();
        BeanCopyUtils.copy(bo, entity);
        entity.setUpdatedBy("System");
        userSeatCareMapper.insert(entity);
        return DataResponse.success();
    }

    public BaseResponse cancelCollectSeat(UserSeatCareBo bo) {
        boolean delete = new LambdaUpdateChainWrapper<>(userSeatCareMapper)
                .eq(UserSeatCareEntity::getOpenId, bo.getOpenId())
                .eq(UserSeatCareEntity::getSeatId, bo.getSeatId())
                .eq(UserSeatCareEntity::getSeatType, bo.getSeatType())
                .remove();
        if (delete) {
            return DataResponse.success();
        } else {
            return DataResponse.fail("取消收藏失败，没有这条记录");
        }
    }

    public BaseResponse isCollectSeat(UserSeatCareBo bo) {
        Integer count = new LambdaQueryChainWrapper<>(userSeatCareMapper)
                .eq(UserSeatCareEntity::getSeatId, bo.getSeatId())
                .eq(UserSeatCareEntity::getOpenId, bo.getOpenId())
                .eq(UserSeatCareEntity::getSeatType, bo.getSeatType())
                .count();
        if (count > 0) {
            return DataResponse.success(true);
        } else {
            return DataResponse.success(false);
        }
    }

    @NotNull
    private AdoptResult getAdoptResult(List<UserSeatCareEntity> list) {
        // 认养座椅id集合
        Set<Integer> adoptSeatIdList = StreamUtils.toSet(list, t -> {
            if (t.getSeatType() != null && SeatTypeStatus.ADOPT.status() == t.getSeatType()) {
                return t.getSeatId();
            }
            return null;
        });
        Map<Integer, List<String>> adoptImageUrlmap = null;
        Map<Integer, SeatInfoAdoptEntity> adoptSeatInfoMap;
        Map<Integer, AdoptSeatInfoEntity> adoptIdMap = null;
        if (CollUtil.isNotEmpty(adoptSeatIdList)) {
            List<SeatImageAdoptEntity> seatImageEntityList = new LambdaQueryChainWrapper<>(seatImageAdoptMapper)
                    .in(SeatImageAdoptEntity::getSeatId, adoptSeatIdList)
                    .list();

            List<SeatInfoAdoptEntity> seatInfoAdoptEntityList = new LambdaQueryChainWrapper<>(seatInfoAdoptMapper)
                    .in(SeatInfoAdoptEntity::getId, adoptSeatIdList)
                    .list();
            if (CollUtil.isEmpty(seatInfoAdoptEntityList)) {
                throw new BusinessException("认养座椅不存在");
            }
            if (CollUtil.isNotEmpty(seatImageEntityList)) {
                Map<Integer, List<SeatImageAdoptEntity>> listMap = StreamUtils.groupByKey(seatImageEntityList, SeatImageAdoptEntity::getSeatId);
                adoptImageUrlmap=  listMap.entrySet().stream().collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .filter(t -> StrUtil.isNotEmpty(t.getImage()))
                                .map(t->fileUploadComponent.getImageUrl(t.getImage()))
                                .collect(Collectors.toList())
                ));
            }
            adoptSeatInfoMap = StreamUtils.toMap(seatInfoAdoptEntityList, SeatInfoAdoptEntity::getId, Function.identity());

            List<AdoptSeatInfoEntity> adoptIdList = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
                    .in(AdoptSeatInfoEntity::getSeatId, adoptSeatIdList)
                    .eq(AdoptSeatInfoEntity::getDeleteFlag, Boolean.FALSE)
                    .ne(AdoptSeatInfoEntity::getStatus, AdoptStatus.PUBLISH_WAIT.status())
                    .list();
            if (CollUtil.isNotEmpty(adoptIdList)) {
                adoptIdMap = StreamUtils.toMap(adoptIdList, AdoptSeatInfoEntity::getSeatId, Function.identity());
            }
        } else {
            adoptImageUrlmap = null;
            adoptSeatInfoMap = null;
            adoptIdMap = null;
        }
        AdoptResult adoptResult = new AdoptResult(adoptImageUrlmap, adoptSeatInfoMap, adoptIdMap);
        return adoptResult;
    }

    private static class AdoptResult {
        public final Map<Integer, List<String>> adoptImageUrlmap;
        public final Map<Integer, SeatInfoAdoptEntity> adoptSeatInfoMap;

        public final Map<Integer, AdoptSeatInfoEntity> adoptIdMap;

        public AdoptResult(Map<Integer, List<String>> adoptImageUrlmap, Map<Integer, SeatInfoAdoptEntity> adoptSeatInfoMap, Map<Integer, AdoptSeatInfoEntity> adoptIdMap) {
            this.adoptImageUrlmap = adoptImageUrlmap;
            this.adoptSeatInfoMap = adoptSeatInfoMap;
            this.adoptIdMap = adoptIdMap;
        }
    }

    @NotNull
    private DonateResult getDonateResult(List<UserSeatCareEntity> list) {
        // 共建座椅id集合
        Set<Integer> donateSeatIdList = StreamUtils.toSet(list, t -> {
            if (t.getSeatType() != null && SeatTypeStatus.DONATE.status() == t.getSeatType()) {
                return t.getSeatId();
            }
            return null;
        });
        Map<Integer, List<String>> donateImageUrlmap = null;
        Map<Integer, SeatInfoEntity> donateSeatInfoMap;
        Map<Integer, DonateSeatInfoEntity> donateIdMap = null;
        if (CollUtil.isNotEmpty(donateSeatIdList)) {
            List<SeatImageEntity> seatImageEntityList = new LambdaQueryChainWrapper<>(seatImageMapper)
                    .in(SeatImageEntity::getSeatId, donateSeatIdList)
                    .list();

            List<SeatInfoEntity> seatInfoEntityList = new LambdaQueryChainWrapper<>(seatInfoMapper)
                    .in(SeatInfoEntity::getId, donateSeatIdList)
                    .list();
            if (CollUtil.isEmpty(seatInfoEntityList)) {
                throw new BusinessException("共建座椅不存在");
            }
            if (CollUtil.isNotEmpty(seatImageEntityList)) {
                donateImageUrlmap = StreamUtils.groupByKey(seatImageEntityList, SeatImageEntity::getSeatId).entrySet().stream().collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .filter(t -> StrUtil.isNotEmpty(t.getImage()))
                                .map(t->fileUploadComponent.getImageUrl(t.getImage()))
                                .collect(Collectors.toList())
                ));
            }
            donateSeatInfoMap = StreamUtils.toMap(seatInfoEntityList, SeatInfoEntity::getId, Function.identity());

            List<DonateSeatInfoEntity> donateSeatInfoEntityList = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
                    .in(DonateSeatInfoEntity::getSeatId, donateSeatIdList)
                    .eq(DonateSeatInfoEntity::getDeleteFlag, Boolean.FALSE)
                    .notIn(DonateSeatInfoEntity::getStatus, Stream.of(DonateStatus.NO_DISPLAY.status(), DonateStatus.PUBLISH_WAIT.status()).collect(Collectors.toList()))
                    .list();
            if (CollUtil.isNotEmpty(donateSeatInfoEntityList)) {
                donateIdMap = StreamUtils.toMap(donateSeatInfoEntityList, DonateSeatInfoEntity::getSeatId, Function.identity());
            }
        } else {
            donateImageUrlmap = null;
            donateSeatInfoMap = null;
            donateIdMap = null;
        }
        DonateResult donateResult = new DonateResult(donateImageUrlmap, donateSeatInfoMap, donateIdMap);
        return donateResult;
    }

    private static class DonateResult {
        public final Map<Integer, List<String>> donateImageUrlmap;
        public final Map<Integer, SeatInfoEntity> donateSeatInfoMap;

        public final Map<Integer, DonateSeatInfoEntity> donateIdMap;

        public DonateResult(Map<Integer, List<String>> donateImageUrlmap, Map<Integer, SeatInfoEntity> donateSeatInfoMap, Map<Integer, DonateSeatInfoEntity> donateIdMap) {
            this.donateImageUrlmap = donateImageUrlmap;
            this.donateSeatInfoMap = donateSeatInfoMap;
            this.donateIdMap = donateIdMap;
        }
    }
}
