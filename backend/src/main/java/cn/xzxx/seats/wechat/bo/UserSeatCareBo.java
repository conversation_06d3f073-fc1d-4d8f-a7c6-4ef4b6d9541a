package cn.xzxx.seats.wechat.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSeatCareBo {

    /**
     * 微信ID
     */
    @NotEmpty(message = "微信openId不能为空")
    private String openId;

    /**
     * 座椅分类 1-共建 2-认养
     */
    @NotNull(message = "座椅分类不能为空")
    private Integer seatType;

    /**
     * 捐赠/认养座椅ID
     */
    @NotNull(message = "座椅ID不能为空")
    private Integer seatId;

    /**
     * 捐赠座椅ID
     */
    private Integer donateId;

    /**
     * 认养座椅ID
     */
    private Integer adoptId;
}
