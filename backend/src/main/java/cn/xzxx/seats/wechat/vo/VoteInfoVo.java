package cn.xzxx.seats.wechat.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VoteInfoVo {

    /**
     * 投票ID
     */
    private Integer id;

    /**
     * 投票名称
     */
    private String title;

    /**
     * 投票标题图片
     */
    private String titleImage;

    /**
     * 投票状态 -1:编辑中  0:未发布 1:已发布 2:已结束 9:已删除
     */
    private Integer status;

    /**
     * 投票开始时间
     */
    private String startTime;

    /**
     * 投票结束时间
     */
    private String endTime;

    /**
     * 投票介绍
     */
    private String introduce;

//    /**
//     * 问题种类 1-单选 2-多选
//     */
//    private String type;
    /**
     * 是否历史投票
     */
    private Boolean historyVoteFlag;


}
