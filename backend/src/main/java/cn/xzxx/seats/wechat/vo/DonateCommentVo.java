package cn.xzxx.seats.wechat.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class DonateCommentVo {

    /**
     * 评论ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 可捐赠座椅ID
     */
    private Integer donateId;

    /**
     * 评论内容
     */
    private String comment;

    /**
     * 评论人open_id
     */
    private String openId;

    /**
     * 评论人昵称
     */
    private String name;

    /**
     * 审核状态 0-审核中(不展示) 1-审核通过(展示)
     */
    private Integer status;
}
