package cn.xzxx.seats.utils;

import java.awt.*;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

public class Loadfont {

    //第一个参数是外部字体名，第二个是字体大小
    public static Font loadFont(String fontFileName, float fontSize) {
        try {
            File file = new File(fontFileName);
            FileInputStream aixing = new FileInputStream(file);
            Font dynamicFont = Font.createFont(Font.TRUETYPE_FONT, aixing);
            Font dynamicFontPt = dynamicFont.deriveFont(fontSize);
            aixing.close();
            return dynamicFontPt;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Font loadFont(InputStream fontIn, float fontSize) {
        try {
            Font dynamicFont = Font.createFont(Font.TRUETYPE_FONT, fontIn);
            Font dynamicFontPt = dynamicFont.deriveFont(fontSize);
            return dynamicFontPt;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if(fontIn != null) {
                try {
                    fontIn.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }


    public static Font Font() {
        String root = System.getProperty("user.dir");//项目根目录路径
        System.out.println(root);
        Font font = Loadfont.loadFont(root + "/simsun.ttc", 12f);//调用
        return font;//返回字体
    }

    public static Font Font2() {
        String root = System.getProperty("user.dir");//项目根目录路径
        System.out.println(root);
        Font font = Loadfont.loadFont(root + "/simsun.ttc", 12f);
        return font;//返回字体
    }
}