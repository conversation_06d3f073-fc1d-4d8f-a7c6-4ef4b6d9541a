package cn.xzxx.seats.utils;

import cn.hutool.core.util.StrUtil;
import cn.xzxx.seats.config.ProjectConfig;
import cn.xzxx.seats.web.orcode.QrCodeService;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;
import sun.font.FontDesignMetrics;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Ellipse2D;
import java.awt.geom.Rectangle2D;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Hashtable;

public class QRCodeUtil {
    private static final String CHARSET = "utf-8";
    private static final String FORMAT_NAME = "JPG";
    private static final String DEF_BACKGROUND_PATH = "static/background2.png";
    private static final String DEF_AVATOR_PATH = "static/avator.png";
    private static final String DEF_SEAT_PATH = "static/seat.png";
    // 海报默认座椅
    private static final String DEF_POSTER_SEAT_PATH = "static/poster_seat.png";

    // 海报背景
    private static final String DEF_POSTER_PATH = "static/poster_bg.png";
    // 海报矩形框
    private static final String DEF_POSTER_RECT_PATH = "static/rectangle29.png";
    // 文字水印
    private static final String DEF_WATERMARK_CHAR_PATH = "static/watermark_characters.png";
    // 线条水印
    private static final String DEF_WATERMARK_LINE_PATH = "static/watermark_line.png";
    // 江西拙楷字体
    private static final String FONT_JIANGXIZHUOKAI_PATH = "static/jiangxizhuokai.ttf";

    // 公益名片默认宽度
    private static final int CARD_DEF_TOTAL_WIDTH = 1286;
    // 公益名片默认高度
    private static final int CARD_DEF_TOTAL_HEIGHT = 1606;

    // 海报默认宽度
    private static final int POSTER_DEF_TOTAL_WIDTH = 612;

    // 海报默认高度
    private static final int POSTER_DEF_TOTAL_HEIGHT = 1080;

    // 二维码尺寸
    private static final int QRCODE_SIZE = 800;
    // LOGO宽度
    private static final int WIDTH = 120;
    // LOGO高度
    private static final int HEIGHT = 120;

    private static BufferedImage createImage(String content, String imgPath, boolean needCompress) throws Exception {
        Hashtable hints = new Hashtable();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, CHARSET);
        hints.put(EncodeHintType.MARGIN, 2);
        BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, QRCODE_SIZE, QRCODE_SIZE,
                hints);
        int width = bitMatrix.getWidth();
        int height = bitMatrix.getHeight();
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
            }
        }
        if (imgPath == null || "".equals(imgPath)) {
            return image;
        }
        // 插入图片
        QRCodeUtil.insertImage(image, imgPath, needCompress);
        return image;
    }

    private static void insertImage(BufferedImage source, String imgPath, boolean needCompress) throws Exception {
        InputStream in = QRCodeUtil.class.getClassLoader().getResourceAsStream(imgPath);
        //String in = QRCodeUtil.class.getResource(imgPath).getPath();

        if (in == null) {
            System.err.println("" + imgPath + "   该文件不存在！");
            return;
        }
        Image src = ImageIO.read(in);
        //Image src = ImageIO.read(new File(QRCodeUtil.class.getResource(imgPath).getPath()));
        int width = src.getWidth(null) * 2;
        int height = src.getHeight(null) * 2;
        if (needCompress) { // 压缩LOGO
            if (width > WIDTH) {
                width = WIDTH;
            }
            if (height > HEIGHT) {
                height = HEIGHT;
            }
            Image image = src.getScaledInstance(width, height, Image.SCALE_SMOOTH);
            BufferedImage tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics g = tag.getGraphics();
            g.drawImage(image, 0, 0, null); // 绘制缩小后的图
            g.dispose();
            src = image;
        }
        // 插入LOGO
        Graphics2D graph = source.createGraphics();
        int x = (QRCODE_SIZE - width) / 2;
        int y = (QRCODE_SIZE - height) / 2;
        graph.drawImage(src, x, y, width, height, null);
        Shape shape = new RoundRectangle2D.Float(x, y, width, width, 6, 6);
        graph.setStroke(new BasicStroke(3f));
        graph.draw(shape);
        graph.dispose();
    }

    public static String encode(String content, String imgPath, boolean needCompress) throws Exception {
        BufferedImage image = QRCodeUtil.createImage(content, imgPath, needCompress);
        return BufferedImageToBase64(image);
    }


    public static String encode(String content, String imgPath) throws Exception {
        return QRCodeUtil.encode(content, imgPath, false);
    }

    public static String BufferedImageToBase64(BufferedImage bufferedImage) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            ImageIO.write(bufferedImage, "png", baos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                baos.flush();
                baos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        byte[] bytes = baos.toByteArray();
        Base64.Encoder encoder = Base64.getEncoder();
        String png_base64 = encoder.encodeToString(bytes).trim();
        png_base64 = png_base64.replaceAll("[\\s*\t\n\r]", "");
        return png_base64;
    }

    private static BufferedImage base64ToBufferedImage(String base64) {
        Base64.Decoder decoder = Base64.getDecoder();
        try {
            byte[] bytes1 = decoder.decode(base64);
            ByteArrayInputStream bais = new ByteArrayInputStream(bytes1);
            BufferedImage image = ImageIO.read(bais);
            bais.close();
            return image;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * base64转BufferedImage（附带图片压缩）
     *
     * @param base64
     * @param width 压缩后的宽度
     * @param height 压缩后的高度
     * @return
     */
    private static BufferedImage base64ToBufferedImage(String base64, int width, int height) {
        Base64.Decoder decoder = Base64.getDecoder();
        try {
            byte[] bytes1 = decoder.decode(base64);
            ByteArrayInputStream bais = new ByteArrayInputStream(bytes1);
            BufferedImage bufferedImage = Thumbnails.of(bais).size(width, height).asBufferedImage();
            bais.close();
            return bufferedImage;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static String createMateriel(String content, String bgPath, String logoPath) throws Exception {
        InputStream bgInStream = QRCodeUtil.class.getClassLoader().getResourceAsStream(bgPath);
        BufferedImage bg = ImageIO.read(bgInStream);
        //BufferedImage bg = ImageIO.read(new File(QRCodeUtil.class.getResource(bgPath).getPath()));
        BufferedImage qrcode = createImage(content, logoPath, false);
        int width = bg.getWidth();
        int height = bg.getHeight();
        int width1 = qrcode.getWidth();
        int height1 = qrcode.getHeight();
        int x = (width - width1) / 2;
        int y = (height - height1) / 2;
        Graphics2D graphics = bg.createGraphics();
        //绘制二维码图片  定位到背景图的右下角
        graphics.drawImage(qrcode, x, y, width1, height1, null); // 绘制缩小后的图
        //关掉画笔
        graphics.dispose();

        return BufferedImageToBase64(bg);
    }

    public static String createMaterielWithQrcode(String name, Color fontColor, int fontYOffset, String content, String bgPath, String qrcodeBase64, String logoPath) throws Exception {
        return createMaterielWithQrcode(name, fontColor, fontYOffset, content, bgPath, qrcodeBase64, logoPath, 0, 0);
    }

    /**
     * 绘制公益名片
     *
     * @param wechatAvatar 微信头像base64或默认头像url
     * @param nickName 昵称
     * @param describe 昵称下方简单描述
     * @param seatImg 座椅图片base64或座椅图片url
     * @return 公益名片图base64
     */
    public static String createMaterielWithQrcode(String wechatAvatar, String nickName, String describe, String seatImg) throws Exception{
        // 创建一个图片缓冲区
//        BufferedImage bf = new BufferedImage(QRCodeUtil.CARD_DEF_TOTAL_WIDTH, QRCodeUtil.CARD_DEF_TOTAL_HEIGHT, BufferedImage.TYPE_INT_BGR);
//        Graphics2D graphics = bf.createGraphics();

        // 根据默认背景图绘制背景
        BufferedImage bg = ImageIO.read(new ClassPathResource(QRCodeUtil.DEF_BACKGROUND_PATH).getInputStream());
        Graphics2D graphics = bg.createGraphics();
        graphics.setColor(Color.WHITE);
        graphics.drawImage(bg,0,0,null);

        // 消除锯齿
        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        graphics.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL,RenderingHints.VALUE_STROKE_DEFAULT);

        // 绘制微信头像
        if (StrUtil.isBlank(wechatAvatar)) {
            BufferedImage wechatAvatarImage = ImageIO.read(new ClassPathResource(QRCodeUtil.DEF_AVATOR_PATH).getInputStream());
            graphics.drawImage(wechatAvatarImage, 117, 77, 222, 247, null);
        } else if (wechatAvatar.startsWith("http")) {
            BufferedImage bufferedImage = ImageIO.read(new URL(wechatAvatar));
            graphics.drawImage(bufferedImage, 117, 77, 222, 247, null);
        } else {
            BufferedImage bufferedImage = base64ToBufferedImage(wechatAvatar, 222, 247);
            graphics.drawImage(bufferedImage, 117, 77, 222, 247, null);
        }

        // 绘制昵称
        graphics.setColor(Color.BLACK);
        graphics.setFont(new Font(null,Font.BOLD, 55));
        graphics.drawString(nickName,400,165);

        // 绘制昵称下方简单描述
        graphics.setColor(Color.GRAY);
        graphics.setFont(new Font(null,Font.PLAIN, 40));
        graphics.drawString(describe,400,250);

        // 绘制座椅图片
        if (StrUtil.isBlank(seatImg)) {
            BufferedImage seatImage = ImageIO.read(new ClassPathResource(QRCodeUtil.DEF_SEAT_PATH).getInputStream());
            graphics.drawImage(seatImage, 57, 381, 1177, 795, null);
        } else if (seatImg.startsWith("http")) {
            BufferedImage bufferedImage = ImageIO.read(new URL(seatImg));
            graphics.drawImage(bufferedImage, 57, 381, 1177, 795, null);
        } else {
            BufferedImage bufferedImage = base64ToBufferedImage(seatImg, 1177, 795);
            graphics.drawImage(bufferedImage, 57, 381, null);
        }

        // 绘制文字水印
        BufferedImage waterMarkImage = ImageIO.read(new ClassPathResource(QRCodeUtil.DEF_WATERMARK_CHAR_PATH).getInputStream());
        float alpha = 0.80f; // 设置水印透明度
        graphics.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));
        graphics.drawImage(waterMarkImage, 15, 870, 360, 220, null);

        // 绘制线条水印
        BufferedImage waterMarkLineImage = ImageIO.read(new ClassPathResource(QRCodeUtil.DEF_WATERMARK_LINE_PATH).getInputStream());
        graphics.drawImage(waterMarkLineImage, 102, 428, 1090,698,null);

        //关掉画笔
        graphics.dispose();

        return BufferedImageToBase64(bg);

    }

    /**
     * 海报图片生成
     *
     * @param wechatAvatar 微信头像base64或默认头像url
     * @param nickName 昵称
     * @param describe 昵称下方简单描述
     * @param seatImg 座椅图片base64或座椅图片url
     * @param address 地址
     * @param qrCode 二维码base64或二维码url
     * @return 海报名片图base64
     */
    public static String createMaterielWithQrcode(String wechatAvatar, String nickName, String describe, String seatImg, String address, BufferedImage qrCode) throws Exception {
        // 根据默认背景图绘制背景
        BufferedImage bg = ImageIO.read(new ClassPathResource(QRCodeUtil.DEF_POSTER_PATH).getInputStream());
        Graphics2D graphics = bg.createGraphics();
        graphics.drawImage(bg,0,0,null);

        // 消除锯齿
        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        graphics.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL,RenderingHints.VALUE_STROKE_DEFAULT);

        // 裁剪圆角矩形
        RoundRectangle2D rectLeft = new RoundRectangle2D.Double(71, 56, 920, 1054, 234, 234);
        graphics.clip(rectLeft);

        // 绘制座椅图片
        if (StrUtil.isBlank(seatImg)) {
            BufferedImage seatImage = ImageIO.read(new ClassPathResource(QRCodeUtil.DEF_POSTER_SEAT_PATH).getInputStream());
            graphics.drawImage(seatImage, 71, 56, 920, 830, null);
        } else if (seatImg.startsWith("http")) {
            BufferedImage bufferedImage = ImageIO.read(new URL(seatImg));
            graphics.drawImage(bufferedImage, 71, 56, 920, 830, null);
        } else {
            BufferedImage bufferedImage = base64ToBufferedImage(seatImg, 920, 830);
            graphics.drawImage(bufferedImage, 71, 56, null);
        }

        graphics.setClip(null);

        // 绘制矩形框
        BufferedImage posterRectImage = ImageIO.read(new ClassPathResource(QRCodeUtil.DEF_POSTER_RECT_PATH).getInputStream())
                .getSubimage(0,0,920,170);
        graphics.drawImage(posterRectImage, 71, 780,null);

        // 绘制二维码遮蔽层
        BufferedImage coverImage2 = ImageIO.read(new ClassPathResource(QRCodeUtil.DEF_POSTER_RECT_PATH).getInputStream())
                .getSubimage(520,300,400,280);
        graphics.drawImage(coverImage2, 585, 1082,null);

        /* 绘制微信头像 **/
        // 创建一个圆形区域
        Ellipse2D circle = new Ellipse2D.Float(146.5f, 835.5f, 180f, 180f);
        BufferedImage wechatAvatarImage;
        if (StrUtil.isBlank(wechatAvatar)) {
            wechatAvatarImage = ImageIO.read(new ClassPathResource(QRCodeUtil.DEF_AVATOR_PATH).getInputStream());
        } else if (wechatAvatar.startsWith("http")) {
            wechatAvatarImage = ImageIO.read(new URL(wechatAvatar));
        } else {
            wechatAvatarImage = base64ToBufferedImage(wechatAvatar, 180, 180);
        }
        Rectangle2D rectangle = new Rectangle2D.Double(146.5d, 835.5d, 180, 180);
        TexturePaint tp = new TexturePaint(wechatAvatarImage, rectangle);
        graphics.setPaint(tp);
        graphics.fill(circle);

        // 绘制昵称
        graphics.setColor(new Color(38, 38, 44));
        graphics.setFont(new Font("Noto Sans SC",Font.PLAIN, 35));
        graphics.drawString(nickName,364,880);

        // 绘制昵称下方简单描述
        graphics.setColor(new Color(141, 141, 141));
        Font font = Font.createFont(Font.TRUETYPE_FONT, new ClassPathResource(QRCodeUtil.FONT_JIANGXIZHUOKAI_PATH).getInputStream());
        graphics.setFont(font.deriveFont(Font.PLAIN, 47f));
        graphics.drawString(describe,364,949);

        // 绘制地址
        graphics.setColor(new Color(150, 150, 150));
        graphics.setFont(new Font("Noto Sans SC",Font.PLAIN, 26));
        graphics.drawString(address,392,1004);

        /* 绘制二维码 **/
        graphics.drawImage(qrCode, 718, 1111, 190,190,null);

        return BufferedImageToBase64(bg);
    }

    public static String createMaterielWithQrcode(String name, Color fontColor, int fontYOffset, String content, String bgPath, String qrcodeBase64, String logoPath, int qrCodeXOffset, int qrCodeYOffset) throws Exception {
        //BufferedImage bg = ImageIO.read(new File(QRCodeUtil.class.getResource(bgPath).getPath()));
        InputStream bgInStream = QRCodeUtil.class.getClassLoader().getResourceAsStream(bgPath);
        BufferedImage bg = ImageIO.read(bgInStream);
        BufferedImage qrcode = base64ToBufferedImage(qrcodeBase64);
        if (qrcode == null) {
            qrcode = createImage(content, logoPath, false);
        }
        int width = bg.getWidth();
        int height = bg.getHeight();
        int width1 = qrcode.getWidth();
        int height1 = qrcode.getHeight();
        int x = (width - width1) / 2;
        int y = (height - height1) / 2;
        Graphics2D graphics = bg.createGraphics();
        //绘制二维码图片  定位到背景图的右下角
        graphics.drawImage(qrcode, x + qrCodeXOffset, y + qrCodeYOffset, width1, height1, null); // 绘制缩小后的图
        if (StringUtils.isNotBlank(name)) {
            if (name.length() > 15) {
                name = name.substring(0, 15);
            }
            //Font font = Loadfont.loadFont(QRCodeUtil.class.getResource("/font/SourceHanSansCN-Medium.otf").getPath(), 100.0f);
            Font font = Loadfont.loadFont(QRCodeUtil.class.getClassLoader().getResourceAsStream("/font/SourceHanSansCN-Medium.otf"), 100.0f);
            if (font != null) {
                int wordWidth = getWordWidth(font, name);
                //设置颜色
                graphics.setColor(fontColor);
                //设置字体
                graphics.setFont(font);
                //写入
                graphics.drawString(name, (width - wordWidth) / 2, y + fontYOffset);
            }
        }
        //关掉画笔
        graphics.dispose();

        return BufferedImageToBase64(bg);
    }


    public static String createQrcode(String content, String logoPath) throws Exception {
        return encode(content, logoPath);
    }

    public static int getWordWidth(Font font, String content) {
        FontDesignMetrics metrics = FontDesignMetrics.getMetrics(font);
        int width = 0;
        for (int i = 0; i < content.length(); i++) {
            width += metrics.charWidth(content.charAt(i));
        }
        return width;
    }

    public static void main(String[] args) throws Exception {
//        String bg1 = QRCodeUtil.class.getResource("/img/materiel4.png").getPath();
//        BufferedImage qrcode = createImage("https://www.tencent.com/zh-cn/", "/img/logo.png", false);
//        BufferedImage bg = ImageIO.read(new File(bg1));
//        int width = bg.getWidth();
//        int height = bg.getHeight();
//        int width1 = qrcode.getWidth();
//        int height1 = qrcode.getHeight();
//        int x = (width - width1) / 2;
//        int y = (height - height1) / 2;
//        Graphics2D g = bg.createGraphics();
//
//        // 绘制背景图片
//        //g.drawImage(bg.getScaledInstance(width, height, Image.SCALE_DEFAULT), -3, -3, null); // 绘制缩小后的图
//        //绘制二维码图片  定位到背景图的右下角
//        //g.drawImage(qrcode, x + 10, y - 70, width1, height1, null); // 绘制缩小后的图
//        g.drawImage(qrcode, x, y, width1, height1, null); // 绘制缩小后的图
//
//        //Font font = new Font("微软雅黑", Font.PLAIN, 100);
//        Font font = Loadfont.loadFont(QRCodeUtil.class.getResource("/font/SourceHanSansCN-Medium.otf").getPath(), 100.0f);
//        //g.setColor(Color.BLACK);
//        int wordWidth = getWordWidth(font, "微信名称");
//        //设置颜色
//        //g.setColor(new Color(17,132,154));
//        //g.setColor(new Color(0, 0, 0));
//        g.setColor(new Color(255, 255, 255));
//        //设置字体
//        g.setFont(font);
//        //写入
//        g.drawString("微信名称", (width - wordWidth) / 2, y + 1080);
//        g.dispose();
//        //关掉画笔
//
//        ImageIO.write(bg, "png", new File("/Users/<USER>/4444.png"));
          String result = createMaterielWithQrcode(null, "微信昵称", "欢迎打卡我共建的座椅！", null, "上海市闵行区浦江镇联航路1688弄", null);
          System.out.println(result);


    }
}
