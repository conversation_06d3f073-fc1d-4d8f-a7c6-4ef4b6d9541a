package cn.xzxx.seats.common.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

public class OprType {

  private final String opr;

  public OprType(String opr) {
    this.opr = opr;
  }

  public static OprType add() {
    return new OprType("add");
  }

  public static OprType edit() {
    return new OprType("edit");
  }

  public static OprType show() {
    return new OprType("show");
  }

  public static OprType delete() {
    return new OprType("delete");
  }

  public static OprType normal() {
    return new OprType("normal");
  }

  @JsonIgnore
  public boolean isAdd() {
    return this.opr.startsWith("add");
  }

  @JsonIgnore
  public boolean isEdit() {
    return this.opr.startsWith("edit");
  }

  @JsonIgnore
  public boolean isShow() {
    return "show".equals(this.opr);
  }

  @JsonIgnore
  public boolean isDelete() {
    return this.opr.startsWith("delete");
  }

  @Override
  @JsonProperty("value")
  public String toString() {
    return this.opr;
  }
}
