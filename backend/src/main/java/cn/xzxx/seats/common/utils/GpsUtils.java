package cn.xzxx.seats.common.utils;

import lombok.Getter;

public class GpsUtils {

  public static GPS bMap2qqMap(double lat, double lng) {
    double txLat;
    double txLng;
    double xPi = 3.14159265358979324;
    double x = lng - 0.0065, y = lat - 0.006;
    double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * xPi);
    double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * xPi);
    txLng = z * Math.cos(theta);
    txLat = z * Math.sin(theta);
    return new GPS(txLat, txLng);
  }

  public static GPS qqMap2BMap(double lat, double lng) {
    double bdLat;
    double bdLng;
    double xPi = 3.14159265358979324;
    double x = lng, y = lat;
    double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * xPi);
    double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * xPi);
    bdLng = z * Math.cos(theta) + 0.0065;
    bdLat = z * Math.sin(theta) + 0.006;
    return new GPS(bdLat, bdLng);
  }

  /**
   * 坐标转换，腾讯地图转换成百度地图坐标
   * @param lat1 腾讯纬度
   * @param lon1 腾讯经度
   * @return 返回结果：经度,纬度
   */
  public static GPS map_tx2bd(String lat1, String lon1) {
    double lat=Double.parseDouble(lat1) ;
    double lon=Double.parseDouble(lon1) ;
    double bd_lat;
    double bd_lon;
    double x_pi = 3.14159265358979324;
    double x = lon, y = lat;
    double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
    double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
    bd_lon = z * Math.cos(theta) + 0.0065;
    bd_lat = z * Math.sin(theta) + 0.006;
    return new GPS(bd_lat, bd_lon);
  }

  /**
   *坐标转换：百度地图转为腾讯地图坐标
   * @param lat1:百度纬度
   * @param lon1：百度经度
   * @return
   */
  public static GPS BdMapToTxMap(String lat1, String lon1) {
    double lat=Double.parseDouble(lat1) ;
    double lon=Double.parseDouble(lon1) ;
    double tx_lat;
    double tx_lon;
    double x_pi = (3.14159265358979324 * 3000.0) / 180.0;
    double x = lon - 0.0065;
    double y = lat - 0.006;
    double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
    double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
    tx_lon = z * Math.cos(theta);
    tx_lat = z * Math.sin(theta);
    return new GPS(tx_lat, tx_lon);
  }

  public static void main(String[] args) {
    System.out.println(GpsUtils.bMap2qqMap(31.19144402392000000000, 121.61918424485000000000));
    System.out.println(GpsUtils.BdMapToTxMap("31.19144402392000000000", "121.61918424485000000000"));

  }

  @Getter
  public static class GPS {

    private final double lat;
    private final double lng;

    public GPS(double lat, double lng) {
      this.lat = lat;
      this.lng = lng;
    }

    @Override
    public String toString() {
      return lng + "," + lat;
    }
  }
}
