package cn.xzxx.seats.common.base;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import lombok.Data;

import java.util.List;

@Data
public class BaseCondition {

  /**
   * 是否是初始操作
   */
  private boolean init;

  /**
   * 页号
   */
  private int pageNumber;

  /**
   * 分页条数
   */
  private int pageSize = 10;

  /**
   * 排序列
   */
  private List<OrderItem> sorts;

  private OrderItem defaultSort;

  public BaseCondition() {
  }
}
