package cn.xzxx.seats.common.security;

import cn.xzxx.seats.config.security.model.LoginUser;
import org.jetbrains.annotations.Nullable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Objects;

/**
 * <pre>
 * SecuritySupport class
 * </pre>
 */
public class SecuritySupport {

  private SecuritySupport() {
  }

  @Nullable
  public static String getLoginUserId() {
    LoginUser loginUser = getLoginUser();
    if (loginUser == null) {
      return null;
    }
    return loginUser.getUserId();
  }

  public static String getLoginUserEmail() {
    return Objects.requireNonNull(getLoginUser()).getUserEmail();
  }

  public static String getLoginUserName() {
    return Objects.requireNonNull(getLoginUser()).getUserName();
  }

  @Nullable
  private static LoginUser getLoginUser() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication == null) {
      return null;
    }
    Object principal = authentication.getPrincipal();
    if (principal instanceof LoginUser) {
      return (LoginUser) principal;
    } else {
      return null;
    }
  }

  public static boolean hasAuthority(String authority) {

    LoginUser loginUser = getLoginUser();

    return loginUser != null && loginUser.getAuthorities().stream()
        .map(GrantedAuthority::getAuthority).anyMatch(a -> a.equalsIgnoreCase(authority));
  }

}
