package cn.xzxx.seats.common.base;

import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.common.utils.ListUtils;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface IBasePagingMapper {

  default <E, M> List<M> selectWithPage(BaseCondition condition, ListResponse<M> response, Function<E, M> convert,
    Function<Page<E>, Page<E>> select) {

    Page<E> page = new Page<>();
    page.setCurrent(condition.getPageNumber());
    page.setSize(condition.getPageSize());
    List<OrderItem> sorts = condition.getSorts();
    if (ListUtils.isNotEmpty(sorts)) {
      page.setOrders(sorts);
    } else {
      OrderItem orderItem = condition.getDefaultSort();
      if (orderItem != null) {
        page.setOrders(Collections.singletonList(orderItem));
      }
    }

    Page<E> dataPage = select.apply(page);
    if (dataPage.getTotal() > 0 && dataPage.getRecords().isEmpty()) {
      long mode = dataPage.getTotal() % dataPage.getSize();
      page.setCurrent(mode == 0 ? mode : mode + 1);
      dataPage = select.apply(page);
    }

    List<M> collect = dataPage.getRecords().stream().map(convert).collect(Collectors.toList());
    response.setValue(collect);

    response.createPagination2(dataPage);

    return collect;
  }
}
