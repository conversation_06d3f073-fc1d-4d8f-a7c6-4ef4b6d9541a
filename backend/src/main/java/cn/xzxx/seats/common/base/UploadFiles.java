package cn.xzxx.seats.common.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

@Data
public class UploadFiles {

  /**
   * タイプ
   */
  @Getter
  private static final String type = "_file_";

  /**
   * アップロードファイル
   */
  @Getter
  private final List<RawFile> files = new ArrayList<>();

  /**
   * ファイルを追加.
   *
   * @param fileId ファイルID
   * @param name   ファイル名
   * @return ファイルのアップロード
   */
  public UploadFiles addFile(String fileId, String name, String url) {
    files.add(new RawFile(fileId, name, url));
    return this;
  }

  /**
   * ファイルを削除します.
   *
   * @return アップロードファイル
   */
  public List<RawFile> deletedFiles() {
    return files.stream().filter(e -> "remove".equals(e.getStatus())).collect(Collectors.toList());
  }

  /**
   * 複数のファイルを追加.
   *
   * @return アップロードファイル
   */
  public List<RawFile> addedFiles() {
    return files.stream().filter(e -> "ready".equals(e.getStatus())).collect(Collectors.toList());
  }

  public boolean isEmpty() {
    return files.isEmpty() || files.stream().allMatch(e -> StringUtils.equals("remove", e.getStatus()));
  }

  /**
   * ファイルクラス.
   */
  @Data
  public static class RawFile {

    /**
     * ファイルID
     */
    private String fileId;

    /**
     * ファイル名
     */
    private String name;

    /**
     * ファイルスターテス
     */
    private String status;

    /**
     * マルチパートファイル
     */
    @JsonIgnore
    private MultipartFile raw;

    /**
     * ファイルサイズ
     */
    @JsonInclude(Include.NON_NULL)
    private Long size;

    private String url;

    /**
     * ファイルConstruction method。
     */
    public RawFile() {
    }

    /**
     * ファイルConstruction method。
     *
     * @param fileId ファイルID
     * @param name   ファイル名
     */
    private RawFile(String fileId, String name, String url) {
      this.fileId = fileId;
      this.name = name;
      this.url = url;
      this.status = "success";
    }

    /**
     * ファイル名を取得。
     *
     * @return ファイル名
     */
    @JsonIgnore
    public String getFileName() {
      return this.name;
    }
  }
}
