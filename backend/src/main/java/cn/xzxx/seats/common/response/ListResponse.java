package cn.xzxx.seats.common.response;

import cn.xzxx.seats.common.base.BaseRecordEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
public class ListResponse<T> extends BaseResponse {

  private Pagination pagination = new Pagination();

  private List<T> value;

  @JsonProperty("data")
  private Map<String, Object> dataMap = new HashMap<>();

  public ListResponse() {
    super();
  }

  public ListResponse(String statusCode) {
    super(statusCode);
  }

  public ListResponse<T> putData(String key, Object data) {
    this.dataMap.put(key, data);
    return this;
  }

  public <E extends BaseRecordEntity> void createPagination(Page<E> dataPage) {
    pagination.setTotalCount(dataPage.getTotal());
    pagination.setPageNumber((int) dataPage.getCurrent());
    pagination.setPageSize((int) dataPage.getSize());
  }

  public <E> void createPagination2(IPage<E> dataPage) {
    pagination.setTotalCount(dataPage.getTotal());
    pagination.setPageNumber((int) dataPage.getCurrent());
    pagination.setPageSize((int) dataPage.getSize());
  }

  @Data
  public static class Pagination {
    private long totalCount;
    private int pageNumber;
    private int pageSize;
  }
}
