package cn.xzxx.seats.common.utils;

import cn.xzxx.seats.common.constants.Constant;
import org.bouncycastle.util.encoders.Hex;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SmUtils {

    private final static Pattern P = Pattern.compile("\\s*|\t|\r|\n");
    private final static String STR01 ="BASE64";

    public SmUtils(){}

    /**
     * SM3加密
     *
     * @param msg 		待加密字符串
     * @param model 	模式(BASE64 / Hex)
     * @return String	加密后字符串
     */
    public static String encryptData_SM3(String msg,String model) {
        byte[] md3 = new byte[32];
        byte[] msg3 = msg.getBytes(Constant.SysCharSet);
        Sm3Digest sm3 = new Sm3Digest();
        sm3.update(msg3, 0, msg3.length);
        sm3.doFinal(md3, 0);
        String s3;
        if(model.equals(STR01)) {
            s3 = new BASE64Encoder().encode(md3);
        }else{
            s3 = new String(Hex.encode(md3));
        }
        return s3;
    }

    /**
     * SM4-ECB加密
     * @param	secretKey	密钥(16)
     * @param	plainText	明文
     * @return	String		密文
     * */
    public static String encryptData_SM4_ECB(String secretKey, String plainText) {
        try
        {
            Sm4Context ctx = new Sm4Context();
            ctx.isPadding = true;
            ctx.mode = Sm4.SM4_ENCRYPT;
            byte[] keyBytes;
            keyBytes = secretKey.getBytes(Constant.SysCharSet);
            Sm4 sm4 = new Sm4();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_ecb(ctx, plainText.getBytes(Constant.SysCharSet));
            String cipherText = new BASE64Encoder().encode(encrypted);
            if (cipherText != null && cipherText.trim().length() > 0)
            {
                Matcher m = P.matcher(cipherText);
                cipherText = m.replaceAll("");
            }
            return cipherText;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * SM4-ECB解密
     * @param	secretKey	密钥(16)
     * @param	cipherText	密文
     * @return	String		明文
     * */
    public static String decryptData_SM4_ECB(String secretKey, String cipherText) {
        try
        {
            Sm4Context ctx = new Sm4Context();
            ctx.isPadding = true;
            ctx.mode = Sm4.SM4_DECRYPT;
            byte[] keyBytes;
            keyBytes = secretKey.getBytes(Constant.SysCharSet);
            Sm4 sm4 = new Sm4();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_ecb(ctx, new BASE64Decoder().decodeBuffer(cipherText));
            return new String(decrypted, Constant.SysCharSet);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * SM4-CBC加密
     * @param	secretKey	密钥(16)
     * @param	iv			偏移量(16)
     * @param	plainText	明文
     * @return	String		密文
     * */
    public static String encryptData_SM4_CBC(String secretKey, String iv, String plainText) {
        try{
            Sm4Context ctx = new Sm4Context();
            ctx.isPadding = true;
            ctx.mode = Sm4.SM4_ENCRYPT;
            byte[] keyBytes;
            byte[] ivBytes;
            keyBytes = secretKey.getBytes();
            ivBytes = iv.getBytes();
            Sm4 sm4 = new Sm4();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, plainText.getBytes(Constant.SysCharSet));
            String cipherText = new BASE64Encoder().encode(encrypted);
            if (cipherText != null && cipherText.trim().length() > 0)
            {
                Matcher m = P.matcher(cipherText);
                cipherText = m.replaceAll("");
            }
            return cipherText;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * SM4-CBC解密
     * @param	secretKey	密钥(16)
     * @param	iv			偏移量(16)
     * @param	cipherText	密文
     * @return	String		明文
     * */
    public static String decryptData_SM4_CBC(String secretKey, String iv, String cipherText) {
        try
        {
            Sm4Context ctx = new Sm4Context();
            ctx.isPadding = true;
            ctx.mode = Sm4.SM4_DECRYPT;
            byte[] keyBytes;
            byte[] ivBytes;
            keyBytes = secretKey.getBytes();
            ivBytes = iv.getBytes();
            Sm4 sm4 = new Sm4();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, new BASE64Decoder().decodeBuffer(cipherText));
            return new String(decrypted, Constant.SysCharSet);
        }catch (Exception ignored){
            return null;
        }
    }

    /**
     * SM4-CBC解密
     * @param	secretKey	密钥(16)
     * @param	iv			偏移量(16)
     * @param	hexString	密钥IV16进制模式(true / false)
     * @param	cipherText	密文
     * @return	String		明文
     * */
    public static String decryptData_SM4_CBC(String secretKey, String iv, boolean hexString, String cipherText) {
        try
        {
            Sm4Context ctx = new Sm4Context();
            ctx.isPadding = true;
            ctx.mode = Sm4.SM4_DECRYPT;
            byte[] keyBytes;
            byte[] ivBytes;
            if (hexString)
            {
                keyBytes = SmSecurityUtils.hexStringToBytes(secretKey);
                ivBytes = SmSecurityUtils.hexStringToBytes(iv);
            }else{
                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();
            }

            Sm4 sm4 = new Sm4();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, new BASE64Decoder().decodeBuffer(cipherText));
            return new String(decrypted, Constant.SysCharSet);
        }catch (Exception ignored){
            return null;
        }
    }

    /**
     * 生成随机秘钥对
     * @return 	Map<String,String>	密钥对
     * */
    public static Map<String,String> generateKeyPair(){
        return Sm2Utils.generateKeyPair();
    }

    /**
     * SM2加密
     * @param 	publicKey	公钥
     * @param 	plainText	明文内容
     * @param 	charset 	字符编码
     * @return 	String		加密字符串
     * */
    public static String encryptData_SM2(String publicKey, String plainText, Charset charset) {
        try {
            return Sm2Utils.encrypt(SmSecurityUtils.hexStringToBytes(publicKey),plainText.getBytes(charset));
        } catch (IOException e) {
            return e.toString();
        }
    }

    /**
     * SM2解密
     * @param	privateKey	私钥
     * @param	encString	密文
     * @param	charset		字符编码
     * @return	String		明文内容
     * */
    public static String decryptData_SM2(String privateKey, String encString, Charset charset) {
        try {
            return new String(Sm2Utils.decrypt(SmSecurityUtils.hexStringToBytes(privateKey), SmSecurityUtils.hexStringToBytes(encString)),charset);
        } catch (IOException e) {
            return e.toString();
        }
    }

}
