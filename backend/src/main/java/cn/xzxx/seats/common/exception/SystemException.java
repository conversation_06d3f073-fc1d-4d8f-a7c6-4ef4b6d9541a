package cn.xzxx.seats.common.exception;

import lombok.Getter;

@Getter
public class SystemException extends RuntimeException {

  private final Throwable exception;

  private final String message;

  private final Object[] messageParam;

  public SystemException(Throwable cause, String message, String... messageParam) {
    super(message, cause);
    this.exception = cause;
    this.message = message;
    this.messageParam = messageParam;
  }
}
