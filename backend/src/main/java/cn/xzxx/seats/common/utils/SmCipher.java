package cn.xzxx.seats.common.utils;

import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.math.ec.ECPoint;

import java.math.BigInteger;

public class SmCipher {
    private int ct;
    private ECPoint p2;
    private Sm3Digest sm3KeyBase;
    private Sm3Digest sm3c3;
    private final byte[] key;
    private byte keyOff;

    public SmCipher() {
        this.ct = 1;
        this.key = new byte[32];
        this.keyOff = 0;
    }

    private void Reset() {
        this.sm3KeyBase = new Sm3Digest();
        this.sm3c3 = new Sm3Digest();

        byte[] p = SmSecurityUtils.byteConvert32Bytes(p2.getX().toBigInteger());
        this.sm3KeyBase.update(p, 0, p.length);
        this.sm3c3.update(p, 0, p.length);

        p = SmSecurityUtils.byteConvert32Bytes(p2.getY().toBigInteger());
        this.sm3KeyBase.update(p, 0, p.length);
        this.ct = 1;
        nextKey();
    }

    private void nextKey() {
        Sm3Digest sm3KeyCur = new Sm3Digest(this.sm3KeyBase);
        sm3KeyCur.update((byte) (ct >> 24 & 0xff));
        sm3KeyCur.update((byte) (ct >> 16 & 0xff));
        sm3KeyCur.update((byte) (ct >> 8 & 0xff));
        sm3KeyCur.update((byte) (ct & 0xff));
        sm3KeyCur.doFinal(key, 0);
        this.keyOff = 0;
        this.ct++;
    }

    public ECPoint initEnc(Sm2 sm2, ECPoint userKey) {
        AsymmetricCipherKeyPair key = sm2.ecc_key_pair_generator.generateKeyPair();
        ECPrivateKeyParameters ecPriKey = (ECPrivateKeyParameters) key.getPrivate();
        ECPublicKeyParameters ecPubKey = (ECPublicKeyParameters) key.getPublic();
        BigInteger k = ecPriKey.getD();
        ECPoint c1 = ecPubKey.getQ();
        this.p2 = userKey.multiply(k);
        Reset();
        return c1;
    }

    public void encrypt(byte[] data) {
        this.sm3c3.update(data, 0, data.length);
        for (int i = 0; i < data.length; i++)
        {
            if (keyOff == key.length)
            {
                nextKey();
            }
            data[i] ^= key[keyOff++];
        }
    }

    public void initDec(BigInteger userDi, ECPoint c1) {
        this.p2 = c1.multiply(userDi);
        Reset();
    }

    public void decrypt(byte[] data) {
        for (int i = 0; i < data.length; i++)
        {
            if (keyOff == key.length)
            {
                nextKey();
            }
            data[i] ^= key[keyOff++];
        }

        this.sm3c3.update(data, 0, data.length);
    }

    public void doFinal(byte[] c3) {
        byte[] p = SmSecurityUtils.byteConvert32Bytes(p2.getY().toBigInteger());
        this.sm3c3.update(p, 0, p.length);
        this.sm3c3.doFinal(c3, 0);
        Reset();
    }
}
