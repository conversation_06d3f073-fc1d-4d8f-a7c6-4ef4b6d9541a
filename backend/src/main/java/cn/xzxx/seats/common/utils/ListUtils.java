package cn.xzxx.seats.common.utils;

import java.util.List;

public final class ListUtils {

    private ListUtils() {
    }

    public static boolean isEmpty(List list) {
        return list == null || list.isEmpty();
    }

    public static boolean isNotEmpty(List list) {
        return !isEmpty(list);
    }

    public static boolean size(List list, int size) {
        return isNotEmpty(list) && list.size() >= size;
    }

    public static <T> T orElse(List<T> list, int size, T or) {
        if (size(list, size + 1)) {
            return list.get(size);
        } else {
            return or;
        }
    }

    public static <T> T orElseNull(List<T> list, int size) {
        return orElse(list, size, null);
    }
}
