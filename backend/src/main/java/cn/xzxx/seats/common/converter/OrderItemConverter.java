package cn.xzxx.seats.common.converter;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.converter.Converter;

import java.util.ArrayList;
import java.util.List;

public class OrderItemConverter implements Converter<String, List<OrderItem>> {

  @Override
  public List<OrderItem> convert(String order) {
    if (StringUtils.isEmpty(order) || !order.contains(":")) {
      return null;
    }
    String[] orderItem = order.split(":");
    String items = orderItem[0];
    boolean asc = "ascending".equals(orderItem[1]);

    List<OrderItem> orderList = new ArrayList<>();
    if (items.contains("/")) {
      for (String item : items.split("/")) {
        orderList.add(asc ? OrderItem.asc(item) : OrderItem.desc(item));
      }
    } else {
      orderList.add(asc ? OrderItem.asc(items) : OrderItem.desc(items));
    }

    return orderList;
  }
}
