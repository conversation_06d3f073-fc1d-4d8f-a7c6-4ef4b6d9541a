package cn.xzxx.seats.common.utils;

import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.math.ec.ECPoint;

import java.io.IOException;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

public class Sm2Utils {
    private static final boolean bl=false;

    /**
     * 生成随机秘钥对
     * @return 	Map<String,String>	密钥对
     * */
    public static Map<String,String> generateKeyPair(){
        Sm2 sm2 = Sm2.Instance();
        AsymmetricCipherKeyPair key = sm2.ecc_key_pair_generator.generateKeyPair();
        ECPrivateKeyParameters ecPriKey = (ECPrivateKeyParameters) key.getPrivate();
        ECPublicKeyParameters ecPubKey = (ECPublicKeyParameters) key.getPublic();
        //解密密钥
        BigInteger privateKey = ecPriKey.getD();
        //加密密钥
        ECPoint publicKey = ecPubKey.getQ();
        Map<String,String> resultMap = new HashMap<String,String>();
        resultMap.put("privateKey", SmSecurityUtils.byteToHex(privateKey.toByteArray()));
        //resultMap.put("publicKey",SmSecurityUtils.byteToHex(publicKey.getEncoded()));  //废弃 2021.06.20变更
        // true  代表压缩密钥，以02、03开头
        // false 代表未压缩，以04开头
        //resultMap.put("publicKey",SmSecurityUtils.byteToHex(publicKey.getEncoded(bl)));
        resultMap.put("publicKey",SmSecurityUtils.byteToHex(publicKey.getEncoded(bl)));
        return resultMap;

        /*Map<String,String> resultMap = new HashMap<String,String>();
        return resultMap;*/
    }

    /**
     * 数据加密
     * @param publicKey 公钥
     * @param data      内容
     * @return
     * @throws IOException
     */
    public static String encrypt(byte[] publicKey, byte[] data) throws IOException
    {
        if (publicKey == null || publicKey.length == 0)
        {
            return null;
        }

        if (data == null || data.length == 0)
        {
            return null;
        }

        byte[] source = new byte[data.length];
        System.arraycopy(data, 0, source, 0, data.length);

        SmCipher cipher = new SmCipher();
        Sm2 sm2 = Sm2.Instance();
        ECPoint userKey = sm2.ecc_curve.decodePoint(publicKey);

        ECPoint c1 = cipher.initEnc(sm2, userKey);
        cipher.encrypt(source);
        byte[] c3 = new byte[32];
        cipher.doFinal(c3);

        //C1 C2 C3拼装成加密字串
        //return SmSecurityUtils.byteToHex(c1.getEncoded()) + SmSecurityUtils.byteToHex(source) + SmSecurityUtils.byteToHex(c3);
        return SmSecurityUtils.byteToHex(c1.getEncoded(bl)) + SmSecurityUtils.byteToHex(source) + SmSecurityUtils.byteToHex(c3);
    }

    /**
     * 数据解密
     * @param privateKey
     * @param encryptedData
     * @return
     * @throws IOException
     */
    public static byte[] decrypt(byte[] privateKey, byte[] encryptedData) throws IOException
    {
        if (privateKey == null || privateKey.length == 0)
        {
            return null;
        }

        if (encryptedData == null || encryptedData.length == 0)
        {
            return null;
        }
        //加密字节数组转换为十六进制的字符串 长度变为encryptedData.length * 2
        String data = SmSecurityUtils.byteToHex(encryptedData);

        /**分解加密字串
         ** （C1 = C1标志位2位 + C1实体部分128位 = 130）
         ** （C3 = C3实体部分64位  = 64）
         ** （C2 = encryptedData.length * 2 - C1长度  - C2长度）
         */
        byte[] c1Bytes = SmSecurityUtils.hexToByte(data.substring(0,130));
        int c2Len = encryptedData.length - 97;
        byte[] c2 = SmSecurityUtils.hexToByte(data.substring(130,130 + 2 * c2Len));
        byte[] c3 = SmSecurityUtils.hexToByte(data.substring(130 + 2 * c2Len,194 + 2 * c2Len));

        Sm2 sm2 = Sm2.Instance();
        BigInteger userDi = new BigInteger(1, privateKey);

        //通过C1实体字节来生成ECPoint
        ECPoint c1 = sm2.ecc_curve.decodePoint(c1Bytes);
        SmCipher cipher = new SmCipher();
        cipher.initDec(userDi, c1);
        cipher.decrypt(c2);
        cipher.doFinal(c3);

        //返回解密结果
        return c2;
    }

}
