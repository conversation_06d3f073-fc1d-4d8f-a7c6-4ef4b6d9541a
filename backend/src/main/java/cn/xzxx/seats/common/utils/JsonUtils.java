package cn.xzxx.seats.common.utils;

import cn.xzxx.seats.config.SpringContext;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

public class JsonUtils {

  public static byte[] toBytes(Object jsonObject) {
    try {
      ObjectMapper objectMapper = SpringContext.getBean(ObjectMapper.class);
      return objectMapper.writeValueAsBytes(jsonObject);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }
}
