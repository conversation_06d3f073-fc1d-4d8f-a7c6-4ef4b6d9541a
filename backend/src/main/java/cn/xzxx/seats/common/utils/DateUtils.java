package cn.xzxx.seats.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;

/**
 * <pre>
 * 日期操作工具类
 * </pre>
 */
public class DateUtils {

  public static final String TIME_FORMAT_CAL = "yyyy-MM-dd HH:mm:ss";
  public static final String TIME_FORMAT_CAL_UTC = "yyyy-MM-dd'T'HH:mm:ssXXX";

  private DateUtils() {
  }

  public static String convertUtcTimeToTime(String timeStr) {

    Timestamp t = convertStringToDate(timeStr, TIME_FORMAT_CAL_UTC, true);
    return convertOnlyDateTimeCalendar(t, TIME_FORMAT_CAL);
  }

  /**
   * timestamp型を「yyyy-MM-dd'T'HH:mm」にString型で変換.
   *
   * @return String yyyy-MM-dd HH:mm
   */
  public static String convertDateTimeZoneCalendar(Timestamp ts) {
    return convertOnlyDateTimeCalendar(ts, TIME_FORMAT_CAL);
  }

  /**
   * timestamp型をパラメータ指定のフォーマットにString型で変換.
   *
   * @param ts     timestamp型日付
   * @param format フォーマット
   * @return フォーマット済み日付
   */
  public static String convertOnlyDateTimeCalendar(Timestamp ts, String format) {
    if (ts == null) {
      return "";
    }
    SimpleDateFormat sdf = new SimpleDateFormat(format);
    sdf.setLenient(false);
    sdf.setTimeZone(TimeZone.getTimeZone(ZoneId.of("Asia/Tokyo")));
    return sdf.format(ts);
  }

  /**
   * String型の「yyyy-MM-dd HH:mm」をDate型で変換.
   *
   * @return Date yyyy-MM-dd HH:mm
   * @throws ParseException 変換時例外
   */
  public static Timestamp convertStringToDate(String st) {
    return convertStringToDate(st, TIME_FORMAT_CAL);
  }

  /**
   * <pre>
   * String型をパラメータ指定のフォーマットにtimestamp型で変換.
   * 変換元文字列がフォーマットより長い場合、超過分を除外して変換
   * </pre>
   *
   * @param st     String型日付
   * @param format フォーマット
   * @return フォーマット済み日付
   */
  public static Timestamp convertStringToDate(String st, String format) {

    return convertStringToDate(st, format, false);
  }

  /**
   * <pre>
   * String型をパラメータ指定のフォーマットにtimestamp型で変換.
   * 変換元文字列がフォーマットより長い場合、超過分を除外して変換
   * </pre>
   *
   * @param st     String型日付
   * @param format フォーマット
   * @param isUtc  UTCであるか
   * @return フォーマット済み日付
   */
  public static Timestamp convertStringToDate(String st, String format, boolean isUtc) {
    if (StringUtils.isEmpty(st)) {
      return null;
    }

    int formatLen = format.replaceAll("'", "").length();
    if (st.length() > formatLen) {
      st = st.substring(0, formatLen);
    }
    SimpleDateFormat sdf = new SimpleDateFormat(format);
    sdf.setLenient(false);
    if (isUtc) {
      sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
    }
    Timestamp retTime = null;
    try {
      retTime = new Timestamp(sdf.parse(st).getTime());
    } catch (ParseException e) {
      return null;
    }
    return retTime;
  }

  /**
   * ナノ秒をX.XXXXXXと秒を基準とした文字列に変換します。 除算を行わずに実施し、高速化を行っています。
   *
   * @param elapsedTime ナノ秒
   * @return 秒単位に整形された文字列
   */
  public static String formattingElapsedTime(long elapsedTime) {
    StringBuilder builder = new StringBuilder();
    builder.append(elapsedTime);
    if (builder.length() < 10) {
      for (int j = builder.length(); j < 10; j++) {
        if (j == 9) {
          builder.insert(0, '.');
        }
        builder.insert(0, '0');
      }
    } else {
      builder.insert(builder.length() - 9, '.');
    }
    return builder.toString();
  }

  public static String localDateTimeFormat(LocalDateTime time) {
    return localDateTimeFormat(time, "yyyy-MM-dd HH:mm:ss");
  }

  public static String localDateTimeFormat(LocalDateTime time, String patten) {
    if (time == null) {
      return "";
    }
    return time.format(DateTimeFormatter.ofPattern(patten));
  }

  public static LocalDateTime localDateTimeParse(String text) {
    return LocalDateTime.parse(text, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
  }

  public static LocalDate localDateParse(String text) {
    return LocalDate.parse(text, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
  }

  public static String localDateFormat(LocalDate date) {
    return localDateFormat(date, "yyyy-MM-dd");
  }

  public static String localDateFormat(LocalDate date, String patten) {
    if (date == null) {
      return "";
    }
    return date.format(DateTimeFormatter.ofPattern(patten));
  }

  public static LocalDate parseLocalDate(String date, String patten) {
    return LocalDate.parse(date, DateTimeFormatter.ofPattern(patten));
  }

  public static LocalDateTime parseLocalDateTime(String date, String patten) {
    return LocalDateTime.parse(date, DateTimeFormatter.ofPattern(patten));
  }
}
