package cn.xzxx.seats.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 一覧画面のレコードの@key注釈付きフィールドを暗号化します。
 *
 * <AUTHOR>
 */
public final class EncryptUtils {

  // d6ef6c84720047e9b6f09d8683446cbe
  private EncryptUtils() {
  }

  /**
   * Mapオブジェクト暗号化処理を行います。
   *
   * @param mapObject 　Map対象
   * @return T
   */
  public static String encrypt(Map<String, String> mapObject) {
    return DesEncrypt.encrypt(JSON.toJSONString(mapObject));
  }

  public static void main(String[] args) {
    Map<String, String> paramMap = new HashMap<>();
    paramMap.put("wjId", "4");
    paramMap.put("openId", "gaoym==11");
    paramMap.put("answerNo", "CG01");
    System.out.println(encrypt(paramMap));
  }

  public static String decrypt(String cipherText, String key) {
    Map<String, String> decrypt = decrypt(cipherText);
    return decrypt.get(key);
  }

  public static Map<String, String> decrypt(String cipherText) {
    String decrypt = DesEncrypt.decrypt(cipherText);
    if (decrypt == null) {
      return new HashMap<>();
    }
    JSONObject jsonObject = JSON.parseObject(decrypt);
    Map<String, String> map = new HashMap<>();
    jsonObject.forEach((key, value) -> map.put(key, String.valueOf(value)));
    return map;
  }

  /**
   * 文字列をMD５に変換します。
   *
   * @param plainText 平文
   * @return MD5文字列
   */
  public static String md5(String plainText) {
    if (!StringUtil.isEmpty(plainText)) {
      return DigestUtils.md5Hex(plainText);
    } else {
      return null;
    }
  }
}
