package cn.xzxx.seats.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.security.SecureRandom;

/**
 * Des暗号化用ツールクラスです。
 *
 * <AUTHOR>
 */
public final class DesEncrypt {

  /**
   * ロガー
   */
  private static final Logger LOGGER = LoggerFactory.getLogger(DesEncrypt.class);
  /**
   * セキュリティキー：DESです。
   */
  private static final String DES = "DES";
  /**
   * セキュリティキー
   */
  private static final String SECRET_KEY = "JQ2o0Tr*$";
  /**
   * 暗号化
   */
  private static final int ENCRYPT = 1;
  /**
   * 復号化
   */
  private static final int DECRYPT = 2;

  private DesEncrypt() {
  }

  /**
   * DES暗号化処理を行います。
   *
   * @param plainText 　平文
   * @return 密文
   */
  public static String encrypt(String plainText) {
    if (plainText == null) {
      return null;
    } else {
      try {
        byte[] input = encrypt(plainText.getBytes(), SECRET_KEY.getBytes());
        return byteArrayToHex(input);
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    }
  }

  /**
   * DES復号化処理を行います。
   *
   * @param cipherText 　密文
   * @return 平文
   */
  public static String decrypt(String cipherText) {
    if (cipherText == null) {
      return null;
    } else {
      try {
        byte[] input = hexToByteArray(cipherText);
        return new String(decrypt(input, SECRET_KEY.getBytes()));
      } catch (Exception e) {
        return null;
      }
    }
  }

  private static byte[] encrypt(byte[] string, byte[] key) {
    return parseData(string, key, ENCRYPT);
  }

  private static byte[] decrypt(byte[] string, byte[] key) {
    return parseData(string, key, DECRYPT);
  }

  private static byte[] parseData(byte[] string, byte[] key, int operation) {

    try {
      SecureRandom random = new SecureRandom();
      DESKeySpec spec = new DESKeySpec(key);
      SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance(DES);
      SecretKey secretKey = secretKeyFactory.generateSecret(spec);
      Cipher cipher = Cipher.getInstance(DES);
      cipher.init(operation, secretKey, random);
      return cipher.doFinal(string);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private static byte[] hexToByteArray(String hexString) {
    byte[] result = new byte[hexString.length() / 2];
    for (int len = hexString.length(), index = 0; index <= len - 1; index += 2) {
      String subString = hexString.substring(index, index + 2);
      int intValue = Integer.parseInt(subString, 16);
      result[index / 2] = (byte) intValue;
    }
    return result;
  }

  private static String byteArrayToHex(byte[] bytes) {
    StringBuilder result = new StringBuilder();
    for (int index = 0, len = bytes.length; index <= len - 1; index += 1) {
      String up = Integer.toHexString((bytes[index] >> 4) & 0xF);
      String down = Integer.toHexString(bytes[index] & 0xF);
      result.append(up);
      result.append(down);
    }
    return result.toString();
  }
}
