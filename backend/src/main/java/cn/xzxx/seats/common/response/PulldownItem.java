package cn.xzxx.seats.common.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PulldownItem {

  private Object value;
  private String label;
  private Object pvalue;
  private List<PulldownItem> children;
  private String type;
  private Object data;

  public PulldownItem(){

  }

  public PulldownItem(String label, Object value, Object pvalue) {
    this.label = label;
    this.value = value;
    this.pvalue = pvalue;
  }

  public PulldownItem(String label, Object value) {
    this.label = label;
    this.value = value;
  }

  @JsonIgnore
  public List<PulldownItem> getOrCreateChildren() {
    if (this.children == null) {
      this.children = new ArrayList<>();
    }
    return this.children;
  }
}
