package cn.xzxx.seats.common.response;

import cn.xzxx.seats.common.constants.ICommonConstants;

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

public class DataResponse<T> extends BaseResponse {

  private T data;

  @Getter
  private Map<String, Object> map = new HashMap<>();

  public DataResponse() {
    super();
  }

  public DataResponse(String statusCode) {
    super(statusCode);
  }

  public DataResponse(String statusCode, String statusMsg) {
    super(statusCode, statusMsg);
  }

  public DataResponse(T data) {
    super(ICommonConstants.RESPONSE_STATUS_CODE_SUCCESS);
    this.data = data;
  }

  public static <T> DataResponse<T> success(){
    return new DataResponse<>();
  }
  public static <T> DataResponse<T> success(T data){
    return new DataResponse<>(data);
  }

  public static <T> DataResponse<T> fail(String statusMsg){
    return new DataResponse(ICommonConstants.RESPONSE_STATUS_CODE_FAILED_BIZ, statusMsg);
  }

  public T getValue() {
    return data;
  }

  public DataResponse<T> setValue(T data) {
    this.data = data;
    return this;
  }

  public static DataResponse of(Map data) {
    DataResponse dataResponse = new DataResponse<>();
    dataResponse.setValue(data);
    return dataResponse;
  }

  public static DataResponse of(Object... kvs) {
    DataResponse dataResponse = new DataResponse<>();

    Map<Object, Object> data = new HashMap<>();
    for (int i = 0; i < kvs.length; i += 2) {
      data.put(kvs[i], kvs[i + 1]);
    }
    dataResponse.setValue(data);
    return dataResponse;
  }

  public DataResponse<T> putData(String key, Object value) {
    this.map.put(key, value);
    return this;
  }
}
