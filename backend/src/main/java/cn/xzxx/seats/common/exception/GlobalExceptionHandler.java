package cn.xzxx.seats.common.exception;

import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.config.ControllerInterceptor;
import cn.xzxx.seats.common.message.MessageConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;
import java.util.stream.Collectors;

@ControllerAdvice
public class GlobalExceptionHandler {

  private static final Logger logger = LoggerFactory
      .getLogger(ControllerInterceptor.class.getName());

  @ExceptionHandler(value = Exception.class)
  @ResponseBody
  public BaseResponse defaultErrorHandler(Exception e) {

    logger.error(e.getMessage(), e);

    MessageResponse response = MessageResponse.newInstance();
    response.addMessage(MessageConstants.MESSAGE_E0099);
    return response;
  }

//  /**
//   * BindException 针对表单校验异常
//   *
//   * @param e
//   * @return
//   */
//  @ResponseStatus(HttpStatus.BAD_REQUEST)
//  @ExceptionHandler({BindException.class})
//  @ResponseBody
//  public BaseResponse otherExceptionHandler(BindException e) {
//    logger.error(ExceptionUtils.getStackTrace(e));
//    // 获取所有异常
//    List<String> errors = e.getBindingResult()
//            .getFieldErrors()
//            .stream()
//            .map(DefaultMessageSourceResolvable::getDefaultMessage)
//            .collect(Collectors.toList());
//    return DataResponse.fail(String.join(";", errors));
//  }

//  /**
//   * MethodArgumentNotValidException异常针对json格式异常
//   * 设置响应状态码为400
//   * @param ex
//   * @return
//   */
//  @ExceptionHandler(value = MethodArgumentNotValidException.class)
//  @ResponseStatus(HttpStatus.BAD_REQUEST)
//  @ResponseBody
//  public BaseResponse handleBindGetException(MethodArgumentNotValidException ex) {
//    logger.error(ExceptionUtils.getStackTrace(ex));
//    logger.error("111111111111111111");
//    logger.error(ex.getMessage());
//    // 获取所有异常
//    List<String> errors = ex.getBindingResult()
//            .getFieldErrors()
//            .stream()
//            .map(DefaultMessageSourceResolvable::getDefaultMessage)
//            .collect(Collectors.toList());
//    return DataResponse.fail(String.join(",", errors));
//  }

  /**
   * TokenInvalidException 针对token校验异常
   *
   * @param e
   * @return
   */
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  @ExceptionHandler({TokenInvalidException.class})
  @ResponseBody
  public BaseResponse tokenExceptionHandler(TokenInvalidException e) {
    logger.error(ExceptionUtils.getStackTrace(e));
    return DataResponse.fail(MessageConstants.MESSAGE_E0080);
  }
}
