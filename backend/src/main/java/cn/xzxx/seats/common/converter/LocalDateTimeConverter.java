package cn.xzxx.seats.common.converter;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class LocalDateTimeConverter implements Converter<String, LocalDateTime> {

  @Override
  public LocalDateTime convert(String s) {
    DateTimeFormatter simpleDateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    if (!StringUtils.isBlank(s)) {
      try {
        return LocalDateTime.parse(s, simpleDateFormat);
      } catch (DateTimeParseException e) {
        throw new RuntimeException(e);
      }
    } else {
      return null;
    }
  }
}
