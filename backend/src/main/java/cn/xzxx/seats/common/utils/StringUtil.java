package cn.xzxx.seats.common.utils;

import java.util.UUID;
import org.apache.commons.lang3.StringUtils;

import javax.sql.rowset.serial.SerialBlob;
import java.nio.charset.StandardCharsets;
import java.sql.Blob;
import java.sql.SQLException;

public class StringUtil {

  private StringUtil() {
  }

  public static Blob stringToBlob(String data) {
    try {
      if (StringUtils.isEmpty(data)) {
        return new SerialBlob(" ".getBytes());
      }
      return new SerialBlob(data.getBytes(StandardCharsets.UTF_8));
    } catch (SQLException e) {
      try {
        return new SerialBlob(" ".getBytes());
      } catch (SQLException e1) {
        return null;
      }
    }
  }

  public static String blobToString(Blob data) {
    try {
      if (data == null || data.length() == 0) {
        return "";
      }
      return new String(data.getBytes(1, (int) data.length()), StandardCharsets.UTF_8).trim();
    } catch (SQLException e) {
      return "";
    }
  }

  public static boolean isEmpty(String text) {
    return text == null || text.trim().length() == 0;
  }

  public static boolean isNumber(String text) {
    try {
      Integer.parseInt(text);
      return true;
    } catch (Exception e) {
      return false;
    }
  }

  public static String getId() {
    return UUID.randomUUID().toString().replaceAll("[-]", "");
  }

  public static String concatUrl(String basePath, String subPath) {
    String path = basePath.replaceAll("\\\\", "/");
    if (path.endsWith("/") && subPath.startsWith("/")) {
      path += subPath.substring(1);
    } else if (!path.endsWith("/") && !subPath.startsWith("/")) {
      path += "/" + subPath;
    } else {
      path += subPath;
    }
    return path;
  }
}
