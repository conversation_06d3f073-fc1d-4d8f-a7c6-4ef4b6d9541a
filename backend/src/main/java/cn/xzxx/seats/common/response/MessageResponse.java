package cn.xzxx.seats.common.response;

import cn.xzxx.seats.common.constants.ICommonConstants;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

public class MessageResponse extends BaseResponse {

  @Getter
  private final List<MessageEx> messages = new ArrayList<>();

  @Getter
  private String type;

  @Setter
  @Getter
  private boolean custom;

  @Setter
  @Getter
  private Object value;

  private MessageResponse() {
    super(ICommonConstants.RESPONSE_STATUS_CODE_FAILED_BIZ);
    custom = false;
  }

  public static MessageResponse newInstance() {
    return new MessageResponse();
  }

  public static MessageResponse newInstance(String message, Object... args) {
    MessageResponse messageResponse = new MessageResponse();
    messageResponse.addMessage("E", message, args);
    return messageResponse;
  }

  public MessageResponse addSuccessMessage(String message, Object... args) {
    return addMessage("I", message, args);
  }

  public MessageResponse addMessage(String message, Object... args) {
    return addMessage("E", message, args);
  }

  public MessageResponse addMessage(String type, String message, Object... args) {
    String msg = message;
    if (args != null) {
      for (int i = 0; i < args.length; i++) {
        msg = msg.replaceAll("\\{" + i + "}", String.valueOf(args[i]));
      }
    }
    addMessageInner(type, "", msg);
    updateType();
    return this;
  }

  private void updateType() {
    if (messages.stream().anyMatch(m -> "E".equals(m.getType()))) {
      type = "E";
    } else if (messages.stream().anyMatch(m -> "W".equals(m.getType()))) {
      type = "W";
    } else {
      type = "I";
    }
  }

  public boolean hasError() {
    return !messages.isEmpty();
  }

  protected void addMessageInner(String type, String messageId, String message) {
    messages.add(new MessageEx(type, messageId, message));
  }

  @Data
  public static class MessageEx {

    private String type;
    private String messageId;
    private String message;

    public MessageEx() {
    }

    public MessageEx(String type, String messageId, String message) {
      this.type = type;
      this.messageId = messageId;
      this.message = message;
    }
  }
}

