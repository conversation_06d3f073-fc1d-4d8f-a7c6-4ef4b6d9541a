package cn.xzxx.seats.common.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateTimeUtils {

    /**返回时间节点前后时间
     * @param	timeType	时间类型[Y年、M月、D日、H时、mm分、ss秒、sss毫秒]
     * @param	timeInterval	时间间隔(往前为负数、往后为正数、0为当前时间)
     * @param	timeIntervalUnit	时间间隔单位[Y年、M月、D日、H时、mm分、ss秒、sss毫秒]
     * */
    public static String getDateTime(String timeType,int timeInterval,String timeIntervalUnit) {
        SimpleDateFormat format;
        switch (timeType) {
            case "Y":
                format = new SimpleDateFormat("yyyy");
                break;
            case "M":
                format = new SimpleDateFormat("yyyyMM");
                break;
            case "D":
                format = new SimpleDateFormat("yyyyMMdd");
                break;
            case "H":
                format = new SimpleDateFormat("yyyyMMddHH");
                break;
            case "mm":
                format = new SimpleDateFormat("yyyyMMddHHmm");
                break;
            case "sss":
                format = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                break;
            case "ss|- :":
                format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                break;
            default :
                format = new SimpleDateFormat("yyyyMMddHHmmss");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        switch(timeIntervalUnit) {
            case "Y":
                c.add(Calendar.YEAR, timeInterval);
                break;
            case "M":
                c.add(Calendar.MONTH, timeInterval);
                break;
            case "D":
                c.add(Calendar.DATE, timeInterval);
                break;
            case "H":
                c.add(Calendar.HOUR, timeInterval);
                break;
            case "mm":
                c.add(Calendar.MINUTE, timeInterval);
                break;
            case "sss":
                c.add(Calendar.MILLISECOND, timeInterval);
                break;
            default :
                c.add(Calendar.SECOND, timeInterval);
        }
        Date now = c.getTime();
        return format.format(now);
    }


    /**返回两个日期之间的差值（返回差值以毫秒为单位）
     * @param dt01  时间1
     * @param dt02  时间2
     * @param dateFormat   时间格式（例："YYYYMMDDHHmmssSSS"）
     * @return  时间差
     */
    public static long dateTimeDiff(String dt01,String dt02,String dateFormat){
        try {
            SimpleDateFormat format;
            format = new SimpleDateFormat(dateFormat);
            Date date01=format.parse(dt01);
            Date date02=format.parse(dt02);
            return date01.getTime()-date02.getTime();
        } catch (Exception e) {
            //e.printStackTrace();
            return -99999999999999000L;
        }
    }
}
