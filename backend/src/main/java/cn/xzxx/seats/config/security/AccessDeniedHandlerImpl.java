package cn.xzxx.seats.config.security;

import cn.xzxx.seats.common.message.MessageConstants;
import cn.xzxx.seats.common.response.MessageResponse;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class AccessDeniedHandlerImpl implements AccessDeniedHandler {

  @Override
  public void handle(HttpServletRequest request, HttpServletResponse response,
      AccessDeniedException accessDeniedException) throws IOException, ServletException {
    if (!response.isCommitted()) {
      // Set the 403 status code.
      MessageResponse data = MessageResponse.newInstance();
      data.addMessage(MessageConstants.MESSAGE_E0080);
      ResponseSupport.jsonResponse(response, data);
    }
  }
}
