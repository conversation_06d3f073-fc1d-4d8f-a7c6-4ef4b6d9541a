package cn.xzxx.seats.config.filter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.CorsUtils;
import org.springframework.web.cors.DefaultCorsProcessor;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Order(1)
@WebFilter(urlPatterns = "/*")
public class CorsFilter implements Filter {

  @Autowired
  private CorsConfigurationSource corsConfigurationSource;

  @Override
  public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse,
      <PERSON><PERSON><PERSON>hain filterChain) throws IOException, ServletException {

    HttpServletRequest request = (HttpServletRequest) servletRequest;
    HttpServletResponse response = (HttpServletResponse) servletResponse;

    if (CorsUtils.isCorsRequest(request)) {
      DefaultCorsProcessor corsProcessor = new DefaultCorsProcessor();
      corsProcessor
          .processRequest(corsConfigurationSource.getCorsConfiguration(request), request, response);
    }
    if (HttpMethod.OPTIONS.matches(request.getMethod())) {
      return;
    }

    filterChain.doFilter(servletRequest, servletResponse);
  }
}
