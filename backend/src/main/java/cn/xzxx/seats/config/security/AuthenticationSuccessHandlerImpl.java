package cn.xzxx.seats.config.security;

import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.config.security.model.LoginUser;
import cn.xzxx.seats.web.auth.LoginAuthService;
import cn.xzxx.seats.web.auth.model.UserModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class AuthenticationSuccessHandlerImpl implements AuthenticationSuccessHandler {

  private static final Logger LOGGER = LoggerFactory
      .getLogger(AuthenticationSuccessHandlerImpl.class);

  private final LoginAuthService loginAuthService;

  public AuthenticationSuccessHandlerImpl(LoginAuthService loginAuthService) {
    this.loginAuthService = loginAuthService;
  }

  @Override
  public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
      Authentication authentication) throws IOException {
    LoginUser loginUser = (LoginUser) authentication.getPrincipal();
    // loginAuthService.updateWrongCount(loginUser.getUserId(), true);
    UserModel user = new UserModel(request.getSession().getId(), loginUser);
    DataResponse<UserModel> data = new DataResponse<>();
    data.setValue(user);
    LOGGER.info("[{}] - login success!", user.getUserEmail());
    ResponseSupport.jsonResponse(response, data);
  }
}
