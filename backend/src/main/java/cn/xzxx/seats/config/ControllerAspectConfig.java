package cn.xzxx.seats.config;

import cn.xzxx.seats.common.exception.SystemException;
import cn.xzxx.seats.common.exception.BusinessException;
import cn.xzxx.seats.common.message.MessageConstants;
import cn.xzxx.seats.common.response.MessageResponse;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@Configuration
@EnableAspectJAutoProxy
public class ControllerAspectConfig {

  private static final Logger LOGGER = LoggerFactory.getLogger("ACS");

  @Bean
  ComponentAspect componentAspect() {
    return new ComponentAspect();
  }

  @Aspect
  public class ComponentAspect {

    @Around("execution(public * cn.xzxx.seats.we*..*Controller.*(..))")
    public Object invokeWeb(ProceedingJoinPoint pjp) {
      try {
        return pjp.proceed();
      } catch (SystemException e) {
        MessageResponse response = MessageResponse.newInstance();
        LOGGER.error(String.format(e.getMessage(), e.getMessageParam()), e.getException());
        response.addMessage(e.getMessage(), e.getMessageParam());
        return response;
      } catch (BusinessException e) {
        MessageResponse response = MessageResponse.newInstance();
        LOGGER.error(String.format(e.getMessage(), e.getMessageParam()));
        response.addMessage(e.getMessage(), e.getMessageParam());
        return response;
      } catch (Throwable e) {
        MessageResponse response = MessageResponse.newInstance();
        LOGGER.error(MessageConstants.MESSAGE_E0099, e);
        response.addMessage(MessageConstants.MESSAGE_E0099);
        return response;
      }
    }
  }
}
