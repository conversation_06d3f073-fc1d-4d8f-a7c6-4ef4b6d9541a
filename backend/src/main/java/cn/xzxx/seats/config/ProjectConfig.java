package cn.xzxx.seats.config;

public class ProjectConfig {

    //授权（必填）
    public static final String GRANT_TYPE = "authorization_code";
    //openID请求URL
    public static final String OPENID_URL = "https://api.weixin.qq.com/sns/jscode2session";
    //token请求URL
    public static final String TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";
    // 密钥
    public final static String SECRETKEY = "Y6DL9IT2Z073CCTW8RGLYKKN";
    // 偏移量
    public final static String IV = "RC666DCH";

    // 微信二维码URL
    public static final String WX_REQUEST_CODE_URL = "https://open.weixin.qq.com/connect/oauth2/authorize?";
    // 获取微信信息URL
    public static final String REDIRECT_SEAT_INTRODUCE_URL = "https://seat.xxinxi.com/seats/introduce";
    // H5页面URL
    public static final String REDIRECT_H5_URL = "https://www.sunrise-market-research.com/wenjuan-h5/#/index/";
    // 获取openid失败后跳转页面
    public final static String WX_FAILED_URL = "https://www.sunrise-market-research.com/wenjuan-h5/#/index/";
    // 二维码excel读取路径根目录
    public static final String QR_CODE_IN_PATH = "G:/wenjuan/in/";
    // 二维码生成路径根目录
    public static final String QR_CODE_OUT_PATH = "G:/wenjuan/out/";

    // 微信授权登录
    public static final String WX_OAUTH2 = "https://api.weixin.qq.com/sns/oauth2/access_token?";

    // 微信获取用户信息
    public static final String WX_USER_INFO = "https://api.weixin.qq.com/sns/userinfo?";

    public static final String APP_ID = "wxe86b432af07e2c94";

    public static final String APP_KEY = "d6e3483e4385e061c2d2d33317231350";

    public static final String WX_GRANT_TYPE = "authorization_code";

    // 微信公众号验证token
    public static final String WX_TOKEN = "xz_bady";

    // 微信公众号验证密钥
    public static final String WX_ENCODING_AES_KEY = "kTvBCaoPrcTZsIb69Td0VlSDGsRdPo85omfB0diTpar";
}
