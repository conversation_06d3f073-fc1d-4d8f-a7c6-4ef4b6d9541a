package cn.xzxx.seats.config.security;

import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.web.auth.LoginAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class AuthenticationFailureHandlerImpl implements AuthenticationFailureHandler {

  private static final Logger LOGGER = LoggerFactory
      .getLogger(AuthenticationFailureHandlerImpl.class);

  private final LoginAuthService loginAuthService;

  public AuthenticationFailureHandlerImpl(LoginAuthService loginAuthService) {
    this.loginAuthService = loginAuthService;
  }

  @Override
  public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
      AuthenticationException exception) throws IOException {
    String errorMessage;
    MessageResponse data = MessageResponse.newInstance();
    if (exception instanceof UsernameNotFoundException) {
      errorMessage = "用户名或者密码错误。";
    } else if (exception instanceof BadCredentialsException) {

      // loginAuthService.updateWrongCount(request.getParameter("username"), false);
      errorMessage = "用户名或者密码错误。";
    } else if(exception instanceof LockedException) {
      errorMessage = "该用户被锁定，请重新设置密码。";
    } else if(exception instanceof DisabledException) {
      errorMessage = "请修改初始密码。";
    } else {
      errorMessage = "登录发生异常。";
    }

    String username = request.getParameter("username");
    LOGGER.warn("[{}] - login failure!", username);
    data.addMessage(errorMessage);
    ResponseSupport.jsonResponse(response, data);
  }
}
