package cn.xzxx.seats.config;

import cn.xzxx.seats.config.security.*;
import cn.xzxx.seats.web.auth.LoginAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.ObjectPostProcessor;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.authentication.configuration.GlobalAuthenticationConfigurerAdapter;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.session.SessionManagementFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Collections;

/**
 * <pre>
 * Security config
 * </pre>
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

  @Autowired
  private LoginAuthService loginAuthService;

  @Override
  public void configure(WebSecurity web) throws Exception {
    web.ignoring().antMatchers(//
        "/static/**", "/index.html",//
        "/*.js", "/*.css", "/webjars/**",//
        "/wechat/**","/qrCode/**",
        "/webh5/**",//
        "/v2/api-docs/**",
        "/v3/api-docs/**",
        "/swagger-resources/**", "/swagger-ui.html", "/*.html", "/error", // swagger
        "/api/v1/partners/**", // expose partners
        "/password/sendMail", "/password/change", // password change
        "/");
  }

  @Override
  protected void configure(HttpSecurity http) throws Exception {
    http.authorizeRequests()
        // swagger start
        .antMatchers("/swagger-ui.html").permitAll() //
        .antMatchers("/swagger-resources/**").permitAll() //
        .antMatchers("/images/**").permitAll() //
        .antMatchers("/webjars/**").permitAll() //
        .antMatchers("/v2/api-docs/**").permitAll() //
        .antMatchers("/v3/api-docs/**").permitAll() //
        .antMatchers("/api/v2/**").permitAll() //
        .antMatchers("/health/check").permitAll() //
        .antMatchers("/configuration/ui").permitAll() //
        .antMatchers("/configuration/security").permitAll() //
        .antMatchers("/v3/api-docs/**").permitAll() //
        // swagger end
        .anyRequest().authenticated();

    // CSRF disable
    http.csrf().disable();

    // swagger start
    http.formLogin().loginPage("/") //
        .loginProcessingUrl("/auth/login") //
        .successHandler(new AuthenticationSuccessHandlerImpl(loginAuthService)) //
        .failureHandler(new AuthenticationFailureHandlerImpl(loginAuthService)) //
        .and(); //

    http.addFilterBefore(new SessionExpiredFilter(), SessionManagementFilter.class);

    // Content-Security-Policy: default-src 'self'
    http.headers().contentSecurityPolicy("script-src 'self' 'unsafe-inline' 'unsafe-eval' " //
        + "https://www.googletagmanager.com " //
        + "https://www.google-analytics.com;").and().httpStrictTransportSecurity()
        .includeSubDomains(true).maxAgeInSeconds(31536000);

    http.cors().configurationSource(corsConfigurationSource());

    http.logout() //
        .logoutRequestMatcher(new AntPathRequestMatcher("/auth/logout**")) //
        .logoutSuccessHandler(new LogoutSuccessHandlerImpl()) //
        .invalidateHttpSession(true); //

    http.exceptionHandling().accessDeniedHandler(new AccessDeniedHandlerImpl());
  }

  /**
   * CorsConfigurationSource
   *
   * @return CorsConfigurationSource
   */
  @Bean
  CorsConfigurationSource corsConfigurationSource() {
    CorsConfiguration configuration = new CorsConfiguration();
    configuration.setAllowedOrigins(Collections.singletonList("*"));
    configuration.setAllowedHeaders(Collections.singletonList("*"));
    configuration.setAllowedMethods(Collections.singletonList("*"));
    configuration.setAllowCredentials(true);
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", configuration);
    return source;
  }

//  @Bean
//  public CookieSerializer httpSessionIdResolver() {
//    DefaultCookieSerializer cookieSerializer = new DefaultCookieSerializer();
//    cookieSerializer.setSameSite("None");
//    return cookieSerializer;
//  }

//  @Bean
//  public PasswordEncoder passwordEncoder() {
//    return new BCryptPasswordEncoder();
//  }

  @Configuration
  protected static class AuthenticationConfiguration extends GlobalAuthenticationConfigurerAdapter {

    @Autowired
    private LoginAuthService loginAuthService;

//    @Autowired
//    private PasswordEncoder passwordEncoder;

    @Override
    public void init(AuthenticationManagerBuilder auth) throws Exception {
      auth.userDetailsService(loginAuthService).passwordEncoder(new BCryptPasswordEncoder());

      auth.objectPostProcessor(new ObjectPostProcessor<Object>() {
        @Override
        public Object postProcess(Object object) {
          if (object instanceof ProviderManager) {
            ((ProviderManager) object).getProviders().forEach(e -> {
              if (e instanceof DaoAuthenticationProvider) {
                ((DaoAuthenticationProvider) e).setHideUserNotFoundExceptions(false);
              }
            });
          }
          return object;
        }
      });
    }
  }
}
