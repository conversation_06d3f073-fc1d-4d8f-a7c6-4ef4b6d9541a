package cn.xzxx.seats.config.security.model;

import lombok.Data;
import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;
import java.util.Date;

public class LoginUser extends User {

  private static final long serialVersionUID = 5366890930439174556L;

  @Getter
  private final String userId;

  @Getter
  private final String userName;

  @Getter
  private final String userEmail;

  @Getter
  private final String[] roleIds;

  @Getter
  private final long loginTime;

  public LoginUser(UserParam userParam) {
    super(userParam.getUserId(), userParam.getPassword(), userParam.isAccountEnabled(), true, true, !userParam.isAccountLocked(), userParam.getAuthorities());
    this.userId = userParam.getUserId();
    this.userName = userParam.getUserName();
    this.userEmail = userParam.getUserEmail();
    this.roleIds = userParam.getRoleIds();
    this.loginTime = new Date().getTime();
  }

  @Override
  public boolean equals(Object rhs) {
    return super.equals(rhs);
  }

  @Override
  public int hashCode() {
    return super.hashCode();
  }

  @Data
  public static class UserParam {
    String userId;
    String userName;
    String userEmail;
    String password;
    String[] roleIds;
    boolean accountLocked;
    boolean accountEnabled;
    Collection<? extends GrantedAuthority> authorities;
  }
}
