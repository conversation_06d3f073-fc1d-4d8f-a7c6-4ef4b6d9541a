package cn.xzxx.seats.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger.web.*;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Springfox Config
 */
@Configuration
@EnableSwagger2
public class SpringFoxConfig {

  @Value("${spring.fox.enable: false}")
  private boolean isSpringFoxEnable;

  @Bean
  public Docket document() {

    ParameterBuilder ticketPar = new ParameterBuilder();
    List<Parameter> pars = new ArrayList<>();
    ticketPar.name("X-SEATS-KEY").description("Seats")
            .modelRef(new ModelRef("string")).parameterType("header")
            .required(true).build();
    pars.add(ticketPar.build());

    return new Docket(DocumentationType.SWAGGER_2).select()
        .apis(RequestHandlerSelectors.basePackage("cn.xzxx.seats.web"))
            .paths(PathSelectors.any()).build().globalOperationParameters(pars).apiInfo(apiInfo()).enable(isSpringFoxEnable);
  }

  /**
   * @return UiConfiguration
   */
  @Bean
  public UiConfiguration uiConfig() {
    return UiConfigurationBuilder.builder().deepLinking(true).displayOperationId(false)
        // .defaultModelsExpandDepth(1)
        // .defaultModelExpandDepth(1)
        .defaultModelRendering(ModelRendering.EXAMPLE).displayRequestDuration(false)
        .docExpansion(DocExpansion.NONE).filter(false).maxDisplayedTags(null)
        .operationsSorter(OperationsSorter.ALPHA)
        // .showExtensions(false)
        .showExtensions(true).tagsSorter(TagsSorter.ALPHA)
        .supportedSubmitMethods(UiConfiguration.Constants.DEFAULT_SUBMIT_METHODS).validatorUrl(null)
        .build();
  }

  /**
   * @return ApiInfo
   */
  private ApiInfo apiInfo() {
    return new ApiInfo("Seats-Shared web rest API", "共享座椅用Rest API列表", "V1", "Terms of service",//
        new Contact("Xuzhong Group Corp.", "www.xuzhong.com", "<EMAIL>"), "License of API", "API license URL", //
        Collections.emptyList());
  }
}
