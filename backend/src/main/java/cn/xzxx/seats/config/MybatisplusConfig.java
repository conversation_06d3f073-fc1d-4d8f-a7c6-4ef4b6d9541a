package cn.xzxx.seats.config;

import cn.xzxx.seats.common.security.SecuritySupport;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Configuration
@MapperScan({ "cn.xzxx.seats.repository", "cn.xzxx.seats.web", "cn.xzxx.seats.wechat" })
public class MybatisplusConfig {

  /**
   * 分页插件
   */
  @Bean
  public PaginationInterceptor paginationInterceptor() {
    return new PaginationInterceptor();
  }

  @Bean
  public GlobalConfig globalConfig() {
    GlobalConfig globalConfig = new GlobalConfig();
    globalConfig.setMetaObjectHandler(new MetaFillHandler());
    return globalConfig;
  }
}

@Component
class MetaFillHandler implements MetaObjectHandler {

  /**
   * 新增数据执行
   *
   * @param metaObject MetaObject
   */
  @Override
  public void insertFill(MetaObject metaObject) {
    this.setFieldValByName("createdAt", LocalDateTime.now(), metaObject);
    this.setFieldValByName("updatedAt", LocalDateTime.now(), metaObject);
    this.setFieldValByName("updatedBy", SecuritySupport.getLoginUserId(), metaObject);
  }

  /**
   * 更新数据执行
   *
   * @param metaObject MetaObject
   */
  @Override
  public void updateFill(MetaObject metaObject) {
    this.setFieldValByName("updatedAt", LocalDateTime.now(), metaObject);
    this.setFieldValByName("updatedBy", SecuritySupport.getLoginUserId(), metaObject);
  }
}
