package cn.xzxx.seats.config.security;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.config.security.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {

  private static final Logger LOGGER = LoggerFactory.getLogger(LogoutSuccessHandlerImpl.class);

  @Override
  public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response,
      Authentication authentication) throws IOException {
    if (authentication != null && authentication.getPrincipal() != null) {
      LoginUser user = (LoginUser) authentication.getPrincipal();
      LOGGER.info("{} logout success!", user.getUserId());
    }

    ResponseSupport.jsonResponse(response, new BaseResponse());
  }
}
