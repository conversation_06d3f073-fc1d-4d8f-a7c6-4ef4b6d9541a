package cn.xzxx.seats.config;

import cn.xzxx.seats.common.security.SecuritySupport;
import cn.xzxx.seats.common.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class ControllerInterceptor extends HandlerInterceptorAdapter {

  private static final String SEATS_START_CONTROLLER_NANO_TIME = "seats_start_elapsed_nano_time";

  private static final Logger LOGGER = LoggerFactory.getLogger(ControllerInterceptor.class);

  private static final String LOG_FMT = "url:{}, method:{}, status:{}, elapsed:{}";

  /*
   * (non-Javadoc)
   *
   * @see org.springframework.web.servlet.handler.HandlerInterceptorAdapter#preHandle(
   * javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse,
   * java.lang.Object)
   */
  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
      throws Exception {
    if (!(handler instanceof HandlerMethod)) {
      return true;
    }

    request.setAttribute(SEATS_START_CONTROLLER_NANO_TIME, System.nanoTime());

    MDC.put("user_agent", request.getHeader("User-Agent"));
    MDC.put("remote_ip", getIpAddress(request));
    MDC.put("session_id", request.getSession().getId());
    String userId = "user_id";
    if (SecuritySupport.getLoginUserId() != null) {
      MDC.put(userId, SecuritySupport.getLoginUserEmail());
    } else {
      MDC.put(userId, request.getParameter("username"));
    }

    return true;
  }

  /*
   * (non-Javadoc)
   *
   * @see org.springframework.web.servlet.handler.HandlerInterceptorAdapter#
   * afterCompletion(javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse,
   * java.lang.Object, java.lang.Exception)
   */
  @Override
  public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
      Object handler, Exception ex) throws Exception {
    Long startNanoTime = (Long) request.getAttribute(SEATS_START_CONTROLLER_NANO_TIME);
    String elapsedTme = "";
    String xRequestId = request.getHeader("X-REQUEST-ID");
    if (!StringUtils.isEmpty(xRequestId)) {
      response.setHeader("X-REQUEST-ID", xRequestId);
    }
    if (startNanoTime != null) {
      long endNanoTime = System.nanoTime();
      elapsedTme = DateUtils.formattingElapsedTime(endNanoTime - startNanoTime);
    }

    if (!request.getServletPath().endsWith("/health/check") //
        && !request.getServletPath().endsWith("/session/check") //
        && !request.getServletPath().equals("/")) {
      LOGGER.info(LOG_FMT, request.getRequestURI(), request.getMethod(), response.getStatus(),
          elapsedTme);
    }

    MDC.remove("user_agent");
    MDC.remove("remote_ip");
    MDC.remove("user_id");
    MDC.remove("session_id");
  }

  private static String getIpAddress(HttpServletRequest request) {
    String ip = request.getHeader("x-forwarded-for");
    String unknown = "unknown";
    if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip)) {
      ip = request.getHeader("Proxy-Client-IP");
    }
    if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip)) {
      ip = request.getHeader("WL-Proxy-Client-IP");
    }
    if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip)) {
      ip = request.getRemoteAddr();
    }
    return ip;
  }
}
