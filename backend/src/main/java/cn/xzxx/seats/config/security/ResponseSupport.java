package cn.xzxx.seats.config.security;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.common.utils.JsonUtils;
import net.sf.json.JSONObject;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class ResponseSupport {

  private ResponseSupport() {
  }

  public static void jsonResponse(HttpServletResponse response, BaseResponse data) throws IOException {
    response.setCharacterEncoding("UTF-8");
    response.setContentType("application/json; charset=utf-8");
    response.getWriter().write(JSONObject.fromObject(data).toString());
  }

  public static ResponseEntity<byte[]> messageResponseEntity(String messageId, String... args) {
    MessageResponse response = MessageResponse.newInstance();
    response.addMessage(messageId, args);
    return messageResponseEntity(response);
  }

  public static ResponseEntity<byte[]> messageResponseEntity(MessageResponse response) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    byte[] bytes = JsonUtils.toBytes(response);
    return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
  }
}
