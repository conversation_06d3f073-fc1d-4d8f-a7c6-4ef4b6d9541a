package cn.xzxx.seats.aspect;

import cn.xzxx.seats.common.exception.TokenInvalidException;
import cn.xzxx.seats.wechat.service.TokenService;
import com.alibaba.fastjson2.JSON;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;


@Aspect
@Component
/**
 * 核验token切面处理
 */
public class TokenAspect {
    private static final Logger logger      = LoggerFactory.getLogger(TokenAspect.class);

    public static final String DEF_TOKEN = "KYlnKUgHwL7S7DuVN3/hdVOAa/DVc9Tg+TUOQTd+Tec=.NdOZKmHjBrTdktZcHPfomZpH0A5o3YWeH3qxrxC7TuY=.iKrSWxEOEVoYvmeZrFP9yGRH/zz0Z8vAgEN8zkqwYO4=.953AEABECACE31B52613473234B92B37";

    @Resource
    private TokenService tokenService;

    @Pointcut("execution(* cn.xzxx.seats.wechat.controller.*.*(..)) " +
            "&& !execution(* cn.xzxx.seats.wechat.controller.UserController.getOpenId())" +
            "&& !execution(* cn.xzxx.seats.wechat.controller.UserController.creatToken())")
    public void aopLogAspect() {
    }

    @Before("aopLogAspect()")
    public void around(JoinPoint point) {
        logger.info("请求开始 : ====================================================================================");

        // 接收到请求，记录请求内容
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String token = request.getHeader("token");
        if (StringUtils.isEmpty(token) || !tokenService.checkToken(token)) {
            throw new TokenInvalidException("token核验不通过");
        }
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();

        // 记录下请求内容
//        logger.info("请求类型 :" + request.getMethod() + "  " + "请求URL : " + request.getRequestURL());
//        logger.info("请求IP  : " + request.getRemoteAddr());
//        logger.info("请求方法 : " + point.getSignature().getDeclaringTypeName() + "." + point.getSignature().getName());

        // 只记录post方法 传json格式的数据
        if ("POST".equals(request.getMethod())) {
            LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
            String[] paramNames = u.getParameterNames(method);
            //方法 1 请求的方法参数值 JSON 格式 null不显示
            if (point.getArgs().length > 0) {
                Object[] args = point.getArgs();
                for (int i = 0; i < args.length; i++) {
                    //请求参数类型判断过滤，防止JSON转换报错
                    if (args[i] instanceof HttpServletRequest || args[i] instanceof HttpServletResponse || args[i] instanceof MultipartFile) {
                        continue;
                    }
                    logger.info("请求参数名称 :" + paramNames[i] + ", 内容 :" + JSON.toJSONString(args[i]));
                }
            }
        } else {
            //请求的方法参数值 兼容fromDate格式和JSON格式
            Object[] args = point.getArgs();
            // 请求的方法参数名称 显示所有字段 值为null也显示该字段
            LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
            String[] paramNames = u.getParameterNames(method);
            if (args != null && paramNames != null) {
                StringBuilder params = new StringBuilder();
                for (int i = 0; i < args.length; i++) {
                    //请求参数类型判断过滤，
                    if (args[i] instanceof HttpServletRequest || args[i] instanceof HttpServletResponse || args[i] instanceof MultipartFile) {
                        continue;
                    }
                    params.append(" ").append(paramNames[i]).append(": ").append(args[i]).append(",");
                }
                logger.info("请求参数 :" + params);
            }
        }
    }

    @AfterReturning(returning = "ret", pointcut = "aopLogAspect()")
    public void doAfterReturning(Object ret) throws Throwable {
        // 处理完请求，返回内容
//        logger.info("返回内容 : " + JSON.toJSONString(ret));
        logger.info("请求结束 :====================================================================================");
    }
}
