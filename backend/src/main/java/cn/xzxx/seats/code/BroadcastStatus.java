package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 广播状态 0:关闭 1:开启 2:暂停
 */
public enum BroadcastStatus {

  Broad_CLOSE(0, "关闭"),
  Broad_OPEN(1, "开启"),
  Broad_STOP(2, "暂停");

  private final int status;
  private final String statusName;

  BroadcastStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (BroadcastStatus value : BroadcastStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
