package cn.xzxx.seats.code;

public enum ResponseStatus {

  NOT_BEGIN("0", "无效"),
  COMPLETED("1", "有效");

  private final String status;
  private final String label;

  ResponseStatus(String status, String label) {
    this.status = status;
    this.label = label;
  }

  public boolean is(String status) {
    return this.status.equals(status);
  }

  public boolean is(Integer status) {
    return this.status.equals(String.valueOf(status));
  }

  public static String label(Integer status) {
    for (ResponseStatus value : ResponseStatus.values()) {
      if (value.is(status)) {
        return value.label;
      }
    }
    return "";
  }

  public int status() {
    return Integer.parseInt(this.status);
  }

  public String label() {
    return this.label;
  }
}
