package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 座椅状态
 */
public enum SexStatus {

  MALE(1, "男"),
  FEMALE(0, "女");

  private final int status;
  private final String statusName;

  SexStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (SexStatus value : SexStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
