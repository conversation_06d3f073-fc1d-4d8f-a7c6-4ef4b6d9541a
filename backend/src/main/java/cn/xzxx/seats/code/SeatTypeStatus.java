package cn.xzxx.seats.code;

import java.util.Objects;

public enum SeatTypeStatus {
    /**
     * 共建
     */
    DONATE(1, "共建"),

    /**
     * 发布
     */
    ADOPT(2, "认养");

    private final int status;
    private final String statusName;

    SeatTypeStatus(int status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    public int status() {
        return this.status;
    }

    public String statusName() {
        return this.statusName;
    }

    public boolean is(Integer status) {
        return Objects.equals(status, this.status);
    }

    public static String statusName(Integer status) {
        for (SeatTypeStatus value : SeatTypeStatus.values()) {
            if (value.is(status)) {
                return value.statusName;
            }
        }
        return String.valueOf(status);
    }
}
