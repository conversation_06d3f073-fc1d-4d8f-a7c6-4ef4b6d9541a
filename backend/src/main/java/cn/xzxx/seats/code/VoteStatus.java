package cn.xzxx.seats.code;

import java.util.Objects;

// 投票状态 -1:编辑中  0:未发布 1:已发布 2:已结束 9:已删除
public enum VoteStatus {
  /**
   * 编辑中
   */
  EDIT_ING(-1, "编辑中"),
  /**
   * 未发布
   */
  NO_PUBLISH(0, "未发布"),

  /**
   * 发布
   */
  PUBLISH(1, "已发布"),

  /**
   * 已结束
   */
  FINISHED(2, "已结束"),

  /**
   * 已删除
   */
  DELETED(9, "已删除");

  private final int status;
  private final String statusName;

  VoteStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (VoteStatus value : VoteStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
