package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 统计排序
 */
public enum SortTypeAdoptStatus {

  SEAT_COUNT(1, "座椅数量"),
  ADOPT_COUNT(2, "认养数量");

  private final int status;
  private final String statusName;

  SortTypeAdoptStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (SortTypeAdoptStatus value : SortTypeAdoptStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
