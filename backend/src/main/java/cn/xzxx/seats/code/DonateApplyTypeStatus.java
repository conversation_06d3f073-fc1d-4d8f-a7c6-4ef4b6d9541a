package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 捐赠申请类型
 */
public enum DonateApplyTypeStatus {

  STREET(1, "街道"),
  PERSON(2, "个人"),
  GOVERNMENT(3, "政府单位"),
  ENTERPRISE(4, "企事业单位"),
  SOCIETY(5, "社会组织");

  private final int status;
  private final String statusName;

  DonateApplyTypeStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (DonateApplyTypeStatus value : DonateApplyTypeStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
