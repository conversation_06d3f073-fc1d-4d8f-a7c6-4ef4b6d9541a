package cn.xzxx.seats.code;

import java.util.Objects;

public enum ShowStatus {
    SHOW(1, "显示"),
    HIDDEN(0, "不显示");

    private final int status;
    private final String statusName;

    ShowStatus(int status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    public int status() {
        return this.status;
    }

    public String statusName() {
        return this.statusName;
    }

    public boolean is(Integer status) {
        return Objects.equals(status, this.status);
    }

    public static String statusName(Integer status) {
        for (ShowStatus value : ShowStatus.values()) {
            if (value.is(status)) {
                return value.statusName;
            }
        }
        return String.valueOf(status);
    }
}
