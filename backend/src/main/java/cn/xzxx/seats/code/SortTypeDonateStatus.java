package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 统计排序
 */
public enum SortTypeDonateStatus {

  SEAT_COUNT(1, "座椅数量"),
  DONATE_COUNT(2, "捐赠数量"),
  DONATE_AMOUNT(3, "捐赠金额");

  private final int status;
  private final String statusName;

  SortTypeDonateStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (SortTypeDonateStatus value : SortTypeDonateStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
