package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 座椅状态
 */
public enum AdoptStatus {

  PUBLISH_WAIT(1, "未发布"),
  PUBLISH_FINISH(2, "已发布"),
  ADOPT_FINISH(3, "被认养"),
  ADOPT_ING(4, "认养中");

  private final int status;
  private final String statusName;

  AdoptStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (AdoptStatus value : AdoptStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
