package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 捐赠申请状态
 */
public enum DonateApplyStatus {

  APPLY_NO_DISPLAY(-1, "不显示"),
  APPLY_WAIT(1, "审核申请中"),
  APPLY_PASS(2, "审核通过"),
  APPLY_REFUSE(3, "审核不通过"),
  APPLY_INEFFECTIVE(4, "无效"),
  APPLY_SHARE_WAIT(5, "份额确认中"),
  APPLY_SHARE_CONFIRM(6, "份额已确认");

  private final int status;
  private final String statusName;

  DonateApplyStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (DonateApplyStatus value : DonateApplyStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
