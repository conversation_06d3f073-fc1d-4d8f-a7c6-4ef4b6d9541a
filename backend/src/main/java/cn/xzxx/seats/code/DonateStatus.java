package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 座椅状态
 */
public enum DonateStatus {

  NO_DISPLAY(-1, "不显示"),
  CANCEL(0, "已取消"),
  PUBLISH_WAIT(1, "未发布"),
  PUBLISH_FINISH(2, "已发布"),
  DONATE_WAIT(3, "捐赠中"),
  DONATE_FINISH(4, "捐赠完成"),
  BUILD_WAIT(5, "建造中"),
  BUILD_FINISH(6, "建造完成"),
  APPLY_FINISH(7, "被满额申请");

  private final int status;
  private final String statusName;

  DonateStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public String statusName() {
    return this.statusName;
  }

  public int status() {
    return this.status;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (DonateStatus value : DonateStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
