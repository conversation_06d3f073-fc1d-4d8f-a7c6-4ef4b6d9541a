package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 统计类型
 */
public enum AreaTypeStatus {

  PROVINCE(1, "按省份"),
  CITY(2, "按城市"),
  STRICT(3, "按区县"),
  STREET(4, "按街道");

  private final int status;
  private final String statusName;

  AreaTypeStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (AreaTypeStatus value : AreaTypeStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
