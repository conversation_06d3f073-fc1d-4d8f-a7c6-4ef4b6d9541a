package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 座椅状态
 */
public enum InfoDetailsStatus {

  INFO_NEWS(1, "新闻"),
  INFO_ADS(2, "公告"),
  INFO_ACTIVES(3, "活动");

  private final int status;
  private final String statusName;

  InfoDetailsStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (InfoDetailsStatus value : InfoDetailsStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
