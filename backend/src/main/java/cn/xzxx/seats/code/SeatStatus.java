package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 座椅状态
 */
public enum SeatStatus {
  /**
   * 未发布
   */
  NO_PUBLISH(0, "未发布"),

  /**
   * 发布
   */
  PUBLISH(1, "发布"),

  /**
   * 下架
   */
  TAKE_DOWN(2, "下架");

  private final int status;
  private final String statusName;

  SeatStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (SeatStatus value : SeatStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
