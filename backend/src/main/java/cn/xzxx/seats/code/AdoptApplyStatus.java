package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 认养状态
 */
public enum AdoptApplyStatus {
  /**
   * 1:申请中
   */
  APPLYING(1, "申请中"),

  /**
   * 审核通过
   */
  APPROVED(2, "审核通过"),

  /**
   * 审核不通过
   */
  EXPIRED(3, "审核不通过"),

  /**
   * 无效
   */
  INVALID(4, "无效");

  private final int status;
  private final String statusName;

  AdoptApplyStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (AdoptApplyStatus value : AdoptApplyStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
