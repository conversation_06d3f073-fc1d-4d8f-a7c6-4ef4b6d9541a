package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 捐赠申请状态
 */
public enum DonateCommentStatus {

  APPLY_WAIT(0, "未审核"),
  APPLY_PASS(1, "审核通过");

  private final int status;
  private final String statusName;

  DonateCommentStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (DonateCommentStatus value : DonateCommentStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
