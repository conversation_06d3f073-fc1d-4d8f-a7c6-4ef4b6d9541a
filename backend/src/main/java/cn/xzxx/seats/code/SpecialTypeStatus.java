package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 特殊标识
 */
public enum SpecialTypeStatus {

    WHOLE(0, "全部"),

    FOR_PUDONG_NEW_DISTRICT(1, "浦东新区专用");

    private final int status;
    private final String statusName;

    SpecialTypeStatus(int status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    public int status() {
        return this.status;
    }

    public String statusName() {
        return this.statusName;
    }

    public boolean is(Integer status) {
        return Objects.equals(status, this.status);
    }

    public static String statusName(Integer status) {
        for (SpecialTypeStatus value : SpecialTypeStatus.values()) {
            if (value.is(status)) {
                return value.statusName;
            }
        }
        return String.valueOf(status);
    }
}
