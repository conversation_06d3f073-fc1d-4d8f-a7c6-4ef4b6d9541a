package cn.xzxx.seats.code;

import java.util.Objects;

/**
 * 养护状态
 */
public enum UpkeepAssignStatus {
  /**
   * 1:待养护 2:养护超时 3:已养护
   */
  UPKEEP_WAIT(1, "待养护"),

  /**
   * 审核通过
   */
  UPKEEP_PASS(2, "养护超时"),

  /**
   * 审核不通过
   */
  UPKEEP_FAIL(3, "已养护");

  private final int status;
  private final String statusName;

  UpkeepAssignStatus(int status, String statusName) {
    this.status = status;
    this.statusName = statusName;
  }

  public int status() {
    return this.status;
  }

  public String statusName() {
    return this.statusName;
  }

  public boolean is(Integer status) {
    return Objects.equals(status, this.status);
  }

  public static String statusName(Integer status) {
    for (UpkeepAssignStatus value : UpkeepAssignStatus.values()) {
      if (value.is(status)) {
        return value.statusName;
      }
    }
    return String.valueOf(status);
  }
}
