package cn.xzxx.seats.code;

import lombok.Data;

import java.util.Objects;

public enum UserIdentityStatus {
    /**
     * 共建者/认养者
     */
    DONATE_OR_ADOPTER("DONATE_OR_ADOPTER", "共建者/认养者"),
    /**
     * 游客模式（初次登录/未有任何认养和共建行为的用户）
     */
    TOURIST("TOURIST", "游客模式"),

    /**
     * 养护人员(后台已登记记录)
     */
    MAINTENANCE("MAINTENANCE", "养护人员");


    private final String status;
    private final String statusName;

    UserIdentityStatus(String status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    public String status() {
        return this.status;
    }

    public String statusName() {
        return this.statusName;
    }

    public boolean is(String status) {
        return Objects.equals(status, this.status);
    }

    public static String statusName(String status) {
        for (UserIdentityStatus value : UserIdentityStatus.values()) {
            if (value.is(status)) {
                return value.statusName;
            }
        }
        return String.valueOf(status);
    }



}
