package cn.xzxx.seats.repository.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("view_sys_use_auth")
public class ViewSysUseAuthEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private String userId;

    private String userName;

    private String password;

    private String roleName;

    private String authCode;


}
