package cn.xzxx.seats.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 座椅认养维护表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("adopt_upkeep")
public class AdoptUpkeepEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 维护记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 养护分配ID
     */
    private Integer upkeepId;

    /**
     * 认养座椅ID
     */
    private Integer adoptId;

    /**
     * 共建座椅ID
     */
    private Integer donateId;

    /**
     * 座椅类型 1-共建 2-认养
     */
    private Integer seatType;

    /**
     * 座椅ID
     */
    private Integer seatId;

    /**
     * 认养人open_id
     */
    private String openId;

    /**
     * 养护人open_id
     */
    private String upkeepOpenId;

    /**
     * 养护人姓名
     */
    private String upkeepName;

    /**
     * 养护单位
     */
    private String upkeepCompany;

    /**
     * 维护时间
     */
    private LocalDateTime upkeepTime;

    /**
     * 维护前图片
     */
    private String upkeepImageBefore;

    /**
     * 维护后图片
     */
    private String upkeepImageAfter;

    /**
     * 维护备注
     */
    private String memo;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * GPS经度
     */
    private Double gpsLng;

    /**
     * GPS维度
     */
    private Double gpsLat;

    /**
     * GPS经度-baidu
     */
    private Double gpsLngBmap;

    /**
     * GPS维度-baidu
     */
    private Double gpsLatBmap;

    /**
     * 特殊标识 0-全部 1-浦东新区专用
     */
    private Integer specialType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;


}
