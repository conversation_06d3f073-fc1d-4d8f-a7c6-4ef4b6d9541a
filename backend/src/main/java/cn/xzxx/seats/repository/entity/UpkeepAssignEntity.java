package cn.xzxx.seats.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 座椅认养维护表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("upkeep_assign")
public class UpkeepAssignEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 养护分配ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 认养座椅NO
     */
    private String adoptNo;

    /**
     * 捐赠座椅NO
     */
    private String donateNo;

    /**
     * 座椅类型 1-共建 2-认养
     */
    private Integer seatType;

    /**
     * 座椅ID
     */
    private Integer seatId;

    /**
     * 养护人id
     */
    private Integer upkeepUserId;

    /**
     * 维护开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate upkeepDateBegin;

    /**
     * 维护结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate upkeepDateEnd;

    /**
     * 养护状态 1:待养护 2:养护超时 3:已养护
     */
    private Integer status;

    /**
     * 特殊标识 0-全部 1-浦东新区专用
     */
    private Integer specialType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;


}
