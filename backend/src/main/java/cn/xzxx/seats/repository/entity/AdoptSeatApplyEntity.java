package cn.xzxx.seats.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 座椅认养反馈表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("adopt_seat_apply")
public class AdoptSeatApplyEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 认养ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 认养座椅ID
     */
    private Integer adoptId;

    /**
     * 可认养座椅ID
     */
    private Integer seatId;

    /**
     * 认养人open_id
     */
    private String openId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 区划代码
     */
    private String code;

    /**
     * 认养公共座椅原因
     */
    private String reason;

    /**
     * 了解渠道 1:
     */
    private String channel;

    /**
     * 认养寄语
     */
    private String donationWord;

    /**
     * 寄语是否显示 0-否 1-是
     */
    private Boolean donationWordFlag;

    /**
     * 认养有效期（默认一年）
     */
    private LocalDateTime validTime;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 申请通过时间
     */
    private LocalDateTime applyPassTime;

    /**
     * 认养状态 1:申请中 2:审核通过 3:审核不通过 4:无效
     */
    private Integer status;

    /**
     * 特殊标识 0-全部 1-浦东新区专用
     */
    private Integer specialType;

    /**
     * 养护访问密码（审核通过时设置）
     */
    private String upkeepPass;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 删除flag 0-否 1-是
     */
    private Boolean deleteFlag;


}
