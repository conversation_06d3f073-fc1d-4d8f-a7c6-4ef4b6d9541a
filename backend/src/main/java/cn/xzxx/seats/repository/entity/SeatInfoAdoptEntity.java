package cn.xzxx.seats.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 座椅信息表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("seat_info_adopt")
public class SeatInfoAdoptEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 座椅ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String name;

    /**
     * 座椅大小
     */
    private String size;

    /**
     * 座椅材质
     */
    private String material;

    /**
     * 座椅主图URL
     */
    private String image;

    /**
     * 座椅介绍
     */
    private String introduce;

    /**
     * 座椅状态 0:未发布 1:发布 2:下架
     */
    private Integer status;

    /**
     * 座椅价格 单位(分)
     */
    private Integer price;

    /**
     * 建造单位
     */
    private String ownerUnit;

    /**
     * 施工单位
     */
    private String constructUnit;

    /**
     * 特殊标识 0-全部 1-浦东新区专用
     */
    private Integer specialType;

    /**
     * 发布开始时间
     */
    private LocalDateTime startTime;

    /**
     * 发布结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;


}
