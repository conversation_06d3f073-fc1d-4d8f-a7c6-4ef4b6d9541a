package cn.xzxx.seats.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 座椅捐赠信息表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("donate_seat_info")
public class DonateSeatInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 可捐赠座椅ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 捐赠/认养座椅NO
     */
    private String donateNo;

    /**
     * 座椅ID
     */
    private Integer seatId;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * GPS经度
     */
    private Double gpsLng;

    /**
     * GPS维度
     */
    private Double gpsLat;

    /**
     * GPS经度-baidu
     */
    private Double gpsLngBmap;

    /**
     * GPS维度-baidu
     */
    private Double gpsLatBmap;

    /**
     * 认捐截止时间（到达截止时间 且 认捐份额>=100 ）
     */
    private LocalDateTime validTime;

    /**
     * 捐赠状态 -1:不显示(自行创建) 0:已取消 1:未发布 2:已发布 3:捐赠中 4:捐赠完成 5:建造中 6:建造完成
     */
    private Integer status;

    /**
     * 特殊标识 0-全部 1-浦东新区专用
     */
    private Integer specialType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 删除flag 0-否 1-是
     */
    private Boolean deleteFlag;


}
