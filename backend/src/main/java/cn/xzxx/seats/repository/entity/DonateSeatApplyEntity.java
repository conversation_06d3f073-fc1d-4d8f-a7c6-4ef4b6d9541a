package cn.xzxx.seats.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 座椅捐赠反馈表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("donate_seat_apply")
public class DonateSeatApplyEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 捐赠ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 可捐赠座椅ID
     */
    private Integer donateId;

    /**
     * 捐赠人open_id
     */
    private String openId;

    /**
     * 捐赠份额（1-100）
     */
    private Integer shareNum;

    /**
     * 实际份额（1-100）
     */
    private Integer realNum;

    /**
     * 实际应付金额
     */
    private Integer payedPrice;

    /**
     * 预估金额 用于记录用户填写数据
     */
    private String predictPrice;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 性别(1-男 2-女)
     */
    private Integer sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 捐赠属性 1:街道 2:个人
     */
    private Integer type;

    /**
     * 身份证明图片URL
     */
    private String personCertificateImage;

    /**
     * 资质证明图片URL
     */
    private String aptitudeCertificateImage;

    /**
     * 了解渠道 1:
     */
    private String channel;

    /**
     * 捐赠公共座椅原因
     */
    private String reason;

    /**
     * 寄语
     */
    private String donationWord;

    /**
     * 寄语是否显示 0-否 1-是
     */
    private Boolean donationWordFlag;

    /**
     * 捐赠状态 -1:不显示 1:申请中 2:捐赠审核通过 3:不通过 4:无效 5:份额确认中 6:份额已确认
     */
    private Integer status;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 申请通过时间
     */
    private LocalDateTime applyPassTime;

    /**
     * 特殊标识 0-全部 1-浦东新区专用
     */
    private Integer specialType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 区划代码
     */
    private String code;

    /**
     * 删除flag 0-否 1-是
     */
    private Boolean deleteFlag;


}
