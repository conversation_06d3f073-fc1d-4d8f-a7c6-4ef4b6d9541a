package cn.xzxx.seats.repository.entity;

import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 权限表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_auth")
public class SysAuthEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @com.baomidou.mybatisplus.annotation.TableId("id")
    private String id;

    /**
     * 权限编码
     */
    private String authCode;

    /**
     * 权限名称
     */
    private String authName;

    /**
     * 父级权限主键
     */
    private String parentId;

    /**
     * 显示顺序
     */
    private Integer showSequence;

    /**
     * GROUP用
     */
    private Boolean groupKey;

    /**
     * 插入时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;


}
