package cn.xzxx.seats.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 投票问题
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("vote_question")
public class VoteQuestionEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投票问题ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 投票ID
     */
    private Integer voteId;

    /**
     * 投票编号(表示用)
     */
    private String questionNo;

    /**
     * 唯一标识UUID
     */
    private String uuid;

    /**
     * 问题种类 1-单选 2-多选
     */
    private String type;

    /**
     * 投票问题名称
     */
    private String title;

    /**
     * 问题样式
     */
    private String titleCss;

    /**
     * 问题图片
     */
    private String titleImage;

    /**
     * 问题图片2
     */
    private String titleImage2;

    /**
     * 问题图片3
     */
    private String titleImage3;

    /**
     * 描述
     */
    private String details;

    /**
     * 题目表示顺序
     */
    private Integer displayOrder;

    /**
     * 是否表示 0-非表示 1-表示
     */
    private Integer display;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;


}
