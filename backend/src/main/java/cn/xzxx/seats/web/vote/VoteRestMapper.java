package cn.xzxx.seats.web.vote;

import cn.xzxx.seats.common.base.IBasePagingMapper;
import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.web.vote.dto.FeedbackCondition;
import cn.xzxx.seats.web.vote.dto.FeedbackRecord;
import cn.xzxx.seats.web.vote.dto.VoteRecord;
import cn.xzxx.seats.web.vote.dto.VoteSearchCondition;
import cn.xzxx.seats.web.vote.entity.VoteFeedbackEntity;
import cn.xzxx.seats.web.vote.entity.VoteRecordEntity;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.function.Function;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface VoteRestMapper extends IBasePagingMapper {

  default List<VoteRecord> selectDataList(VoteSearchCondition condition, ListResponse<VoteRecord> listResponse,
    Function<VoteRecordEntity, VoteRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectList(page, condition));
  }

  Page<VoteRecordEntity> selectList(Page<VoteRecordEntity> page, @Param(value = "param") VoteSearchCondition condition);

  default List<FeedbackRecord> selectFeedbackList(FeedbackCondition condition,
    ListResponse<FeedbackRecord> listResponse, Function<VoteFeedbackEntity, FeedbackRecord> convert) {
    return selectWithPage(condition, listResponse, convert, page -> selectFeedbackList(page, condition));
  }

  Page<VoteFeedbackEntity> selectFeedbackList(Page<VoteFeedbackEntity> page,
    @Param(value = "param") FeedbackCondition condition);
}
