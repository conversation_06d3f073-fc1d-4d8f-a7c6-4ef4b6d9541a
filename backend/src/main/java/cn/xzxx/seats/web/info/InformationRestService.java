package cn.xzxx.seats.web.info;

import cn.xzxx.seats.code.InfoDetailsStatus;
import cn.xzxx.seats.common.base.UploadFiles;
import cn.xzxx.seats.common.response.*;
import cn.xzxx.seats.common.utils.DateUtils;
import cn.xzxx.seats.common.utils.ListUtils;
import cn.xzxx.seats.common.utils.StringUtil;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.component.PulldownComponent;
import cn.xzxx.seats.repository.entity.CertificateSettingsEntity;
import cn.xzxx.seats.repository.entity.InformationDetailsSettingsEntity;
import cn.xzxx.seats.repository.entity.InformationTitleSettingsEntity;
import cn.xzxx.seats.repository.mapper.InformationDetailsSettingsMapper;
import cn.xzxx.seats.repository.mapper.InformationTitleSettingsMapper;
import cn.xzxx.seats.web.info.dto.InformationDetailsForm;
import cn.xzxx.seats.web.info.dto.InformationForm;
import cn.xzxx.seats.web.info.dto.InformationSearchCondition;
import cn.xzxx.seats.web.info.dto.entity.InformationDetailsRecord;
import cn.xzxx.seats.web.info.dto.entity.InformationRecord;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class InformationRestService {

  @Autowired
  private InformationTitleSettingsMapper settingsMapper;

  @Autowired
  private InformationDetailsSettingsMapper settingsDetailsMapper;

  @Autowired
  private PulldownComponent pulldownComponent;

  @Autowired
  private FileUploadComponent fileUploadComponent;

  public BaseResponse listInit(InformationSearchCondition condition) {

    ListResponse<InformationRecord> listResponse = new ListResponse<>();
    List<InformationTitleSettingsEntity> entities = settingsMapper.selectList(new LambdaQueryWrapper<InformationTitleSettingsEntity>()
            .eq(condition.getStatus() !=null, InformationTitleSettingsEntity::getStatus, condition.getStatus()));
    List<InformationRecord> records = new ArrayList<>();
    entities.forEach(x->{
      InformationRecord record = new InformationRecord();
      record.setId(x.getId());
      record.setImageUrl(x.getImageUrl());
      record.setLinkUrl(x.getLinkUrl());
      record.setStatus(x.getStatus());
      record.setCreatedAt(DateUtils.localDateTimeFormat(x.getCreatedAt()));
      records.add(record);
    });
    listResponse.setValue(records);
    return listResponse;
  }

  public BaseResponse init(Integer id) {

    InformationTitleSettingsEntity entity = settingsMapper.selectById(id);
    if(entity == null) {
      return MessageResponse.newInstance("信息不存在，请返回列表刷新后再操作。");
    }
    InformationForm form = new InformationForm();
    form.setId(entity.getId());

    if (!StringUtil.isEmpty(entity.getImageUrl())) {
      UploadFiles personFiles = new UploadFiles();
      personFiles.addFile(entity.getImageUrl(), "", fileUploadComponent.getImageUrl(entity.getImageUrl()));
      form.setImage(personFiles);
    }
    form.setImageUrl(entity.getImageUrl());

    form.setLinkUrl(entity.getLinkUrl());
    DataResponse<InformationForm> result = new DataResponse<>();
    return result.setValue(form);

  }

  @Transactional
  public BaseResponse edit(boolean add, InformationForm form) {
    InformationTitleSettingsEntity entity;

    UploadFiles image = form.getImage();
    if(image == null) {
      return MessageResponse.newInstance("保存失败，请添加资讯图片后再进行保存！");
    }
    List<UploadFiles.RawFile> addedFiles = image.addedFiles();
    List<UploadFiles.RawFile> deleteFiles = image.deletedFiles();

    if (add) {
      entity = new InformationTitleSettingsEntity();
      if (ListUtils.isNotEmpty(addedFiles)) {
        UploadFiles.RawFile rawFile = addedFiles.get(0);
        String fileUrl = fileUploadComponent.add(rawFile);
        entity.setImageUrl(fileUrl);
      }
      entity.setLinkUrl(form.getLinkUrl());
      entity.setStatus(form.getStatus());
      entity.setCreatedAt(LocalDateTime.now());
      settingsMapper.insert(entity);
    } else {
      entity = settingsMapper.selectById(form.getId());
      if (entity == null) {
        return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
      }
      if (ListUtils.isNotEmpty(addedFiles)) {
        UploadFiles.RawFile rawFile = addedFiles.get(0);
        String fileUrl = fileUploadComponent.add(rawFile);
        entity.setImageUrl(fileUrl);
      }
      entity.setStatus(form.getStatus());
      entity.setLinkUrl(form.getLinkUrl());
      settingsMapper.updateById(entity);
    }

    // 座椅图片处理
    if (ListUtils.isNotEmpty(deleteFiles)) {
      fileUploadComponent.delete(deleteFiles.get(0));
    }
    return new BaseResponse();
  }

  @Transactional
  public BaseResponse delete(Integer id) {

    InformationTitleSettingsEntity entity = settingsMapper.selectById(id);
    if (entity == null) {
      return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
    }

    fileUploadComponent.delete(entity.getImageUrl());

    settingsMapper.deleteById(id);
    return new BaseResponse();
  }

  public BaseResponse listInitDetails(InformationSearchCondition condition) {

    ListResponse<InformationDetailsRecord> listResponse = new ListResponse<>();
    List<InformationDetailsSettingsEntity> entities = settingsDetailsMapper.selectList(new LambdaQueryWrapper<InformationDetailsSettingsEntity>()
            .eq(condition.getType() != null, InformationDetailsSettingsEntity::getType, condition.getType())
            .eq(condition.getStatus() !=null, InformationDetailsSettingsEntity::getStatus, condition.getStatus()));
    List<InformationDetailsRecord> records = new ArrayList<>();
    entities.forEach(x->{
      InformationDetailsRecord record = new InformationDetailsRecord();
      record.setId(x.getId());
      record.setType(x.getType());
      record.setTypeName(InfoDetailsStatus.statusName(x.getType()));
      record.setTitle(x.getTitle());
      record.setContent(x.getContent());
      record.setImageUrl(x.getImageUrl());
      record.setLinkUrl(x.getLinkUrl());
      record.setStatus(x.getStatus());
      record.setCreatedAt(DateUtils.localDateTimeFormat(x.getCreatedAt()));
      records.add(record);
    });
    listResponse.setValue(records);

    if (condition.isInit()) {
      List<PulldownItem> statusOptions = pulldownComponent.getBroadcastStatusPulldown();
      listResponse.putData("typeOptions", statusOptions);
    }
    return listResponse;
  }

  public BaseResponse initDetails(Integer id) {

    InformationDetailsSettingsEntity entity = settingsDetailsMapper.selectById(id);
    if(entity == null) {
      return MessageResponse.newInstance("信息不存在，请返回列表刷新后再操作。");
    }
    InformationDetailsForm form = new InformationDetailsForm();
    form.setId(entity.getId());
    form.setType(entity.getType());
    form.setTitle(entity.getTitle());
    form.setContent(entity.getContent());
    form.setImageUrl(entity.getImageUrl());
    if (!StringUtil.isEmpty(entity.getImageUrl())) {
      UploadFiles personFiles = new UploadFiles();
      personFiles.addFile(entity.getImageUrl(), "", fileUploadComponent.getImageUrl(entity.getImageUrl()));
      form.setImage(personFiles);
    }
    form.setLinkUrl(entity.getLinkUrl());
    DataResponse<InformationDetailsForm> result = new DataResponse<>();
    return result.setValue(form);

  }

  @Transactional
  public BaseResponse editDetails(boolean add, InformationDetailsForm form) {
    InformationDetailsSettingsEntity entity;
    UploadFiles image = form.getImage();
    if(image == null) {
      return MessageResponse.newInstance("保存失败，请添加证书图片后再进行保存！");
    }
    List<UploadFiles.RawFile> addedFiles = image.addedFiles();
    List<UploadFiles.RawFile> deleteFiles = image.deletedFiles();

    if (add) {
      entity = new InformationDetailsSettingsEntity();
      entity.setType(form.getType());
      entity.setTitle(form.getTitle());
      entity.setContent(form.getContent());
      if (ListUtils.isNotEmpty(addedFiles)) {
        UploadFiles.RawFile rawFile = addedFiles.get(0);
        String fileUrl = fileUploadComponent.add(rawFile);
        entity.setImageUrl(fileUrl);
      }
      entity.setLinkUrl(form.getLinkUrl());
      entity.setStatus(form.getStatus());
      entity.setCreatedAt(LocalDateTime.now());
      settingsDetailsMapper.insert(entity);
    } else {
      entity = settingsDetailsMapper.selectById(form.getId());
      if (entity == null) {
        return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
      }
      entity.setType(form.getType());
      entity.setTitle(form.getTitle());
      entity.setContent(form.getContent());
      if (ListUtils.isNotEmpty(addedFiles)) {
        UploadFiles.RawFile rawFile = addedFiles.get(0);
        String fileUrl = fileUploadComponent.add(rawFile);
        entity.setImageUrl(fileUrl);
      }
      entity.setStatus(form.getStatus());
      entity.setLinkUrl(form.getLinkUrl());
      settingsDetailsMapper.updateById(entity);
    }
    // 座椅图片处理
    if (ListUtils.isNotEmpty(deleteFiles)) {
      fileUploadComponent.delete(deleteFiles.get(0));
    }
    return new BaseResponse();
  }

  @Transactional
  public BaseResponse deleteDetails(Integer id) {

    InformationDetailsSettingsEntity entity = settingsDetailsMapper.selectById(id);
    if (entity == null) {
      return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
    }
    settingsDetailsMapper.deleteById(id);

    fileUploadComponent.delete(entity.getImageUrl());

    return new BaseResponse();
  }
}
