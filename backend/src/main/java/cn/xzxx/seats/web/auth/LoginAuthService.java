package cn.xzxx.seats.web.auth;

import cn.xzxx.seats.config.security.model.LoginUser;
import cn.xzxx.seats.repository.entity.ViewSysUseAuthEntity;
import cn.xzxx.seats.repository.mapper.ViewSysUseAuthMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LoginAuthService implements UserDetailsService {

  @Value("${pwd.wrong.times.account.locked:10}")
  private int wrongTimesForLock;

  @Autowired
  private ViewSysUseAuthMapper sysUseAuthMapper;

  /*
   * (non-Javadoc)
   *
   * @see org.springframework.security.core.userdetails.UserDetailsService#
   * loadUserByUsername(java.lang. String)
   */
  @Override
  public UserDetails loadUserByUsername(String username) {
    if (username == null || "".equals(username.trim())) {
      throw new UsernameNotFoundException("用户ID不能为空。");
    }

    ViewSysUseAuthEntity userEntity = sysUseAuthMapper
        .selectOne(new QueryWrapper<ViewSysUseAuthEntity>().eq("user_id", username));

    if (userEntity == null) {
      throw new UsernameNotFoundException("用户ID不存在。");
    }
    LoginUser.UserParam param = new LoginUser.UserParam();
    param.setUserId(username);
    param.setUserName(userEntity.getUserName());
    param.setPassword(userEntity.getPassword());
    param.setRoleIds(userEntity.getRoleName().split(","));
    param.setAccountLocked(false);
    param.setAccountEnabled(true);

    List<Authority> authList = Arrays.stream("R_ADMIN".split(","))
        .map(Authority::createAuthority).collect(Collectors.toList());
    param.setAuthorities(authList);
    return new LoginUser(param);
  }

  static class Authority implements GrantedAuthority {
    private static final long serialVersionUID = 1L;

    static HashMap<String, Authority> authorities = new HashMap<>();

    private String value;

    private Authority() {
    }

    private Authority(String authority) {
      this.value = authority;
    }

    static Authority createAuthority(String authority) {

      return authorities.computeIfAbsent(authority, Authority::new);
    }

    @Override
    public String getAuthority() {
      return value;
    }

    @Override
    public String toString() {
      return getAuthority();
    }
  }

  static class Role extends Authority {

    private static final long serialVersionUID = 1L;

    private Role() {
    }

    private Role(String role) {
      super(role);
    }

    @Override
    public String getAuthority() {
      return "ROLE_" + super.getAuthority();
    }

    public static Authority createRole(String authority) {
      return authorities.computeIfAbsent(authority, Role::new);
    }

  }
}
