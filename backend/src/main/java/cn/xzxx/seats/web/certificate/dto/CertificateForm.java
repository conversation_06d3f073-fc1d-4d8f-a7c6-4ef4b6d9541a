package cn.xzxx.seats.web.certificate.dto;

import cn.xzxx.seats.common.base.UploadFiles;
import lombok.Data;

@Data
public class CertificateForm {

  /**
   * 证书ID
   */
  private Integer id;

  /**
   * 用户open_id
   */
  private String openId;

  /**
   * 座椅分类 1-共建 2-认养
   */
  private Integer seatType;

  /**
   * 捐赠/认养座椅ID
   */
  private Integer applyId;

  /**
   * 座椅编号
   */
  private String seatNo;

  /**
   * 座椅名称
   */
  private String seatName;

  /**
   * 证书URL
   */
  private String imageUrl;

  private UploadFiles image;

  /**
   * 显示
   */
  private Integer status;

}