package cn.xzxx.seats.web.donate;

import cn.xzxx.seats.web.donate.dto.DonateExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ReadDataListener extends AnalysisEventListener<DonateExcel> {

  private DonateRestService donateRestService;

  public static final Logger logger = LoggerFactory.getLogger(ReadDataListener.class);

  public ReadDataListener(DonateRestService donateRestService) {
      this.donateRestService = donateRestService;
  }

  @Override
  public void invoke(DonateExcel donateExcel, AnalysisContext analysisContext) {

      // 导入DB
    donateRestService.insDonateInfo(donateExcel);

  }

  @Override
  public void doAfterAllAnalysed(AnalysisContext analysisContext) {
  }
}
