package cn.xzxx.seats.web.adopt;

import cn.xzxx.seats.web.adopt.dto.AdoptExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ReadDataListener extends AnalysisEventListener<AdoptExcel> {

  private AdoptRestService adoptRestService;

  public static final Logger logger = LoggerFactory.getLogger(ReadDataListener.class);

  public ReadDataListener(AdoptRestService adoptRestService) {
      this.adoptRestService = adoptRestService;
  }

  @Override
  public void invoke(AdoptExcel adoptExcel, AnalysisContext analysisContext) {

      // 导入DB
    adoptRestService.insAdoptInfo(adoptExcel);

  }

  @Override
  public void doAfterAllAnalysed(AnalysisContext analysisContext) {
  }
}
