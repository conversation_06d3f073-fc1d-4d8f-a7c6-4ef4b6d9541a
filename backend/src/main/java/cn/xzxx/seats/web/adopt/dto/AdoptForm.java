package cn.xzxx.seats.web.adopt.dto;

import cn.xzxx.seats.common.base.UploadFiles;
import lombok.Data;

@Data
public class AdoptForm {

  /**
   * 认养ID
   */
  private Integer id;

  /**
   * 认养NO
   */
  private String adoptNo;

  /**
   * 捐赠NO
   */
  private String donateNo;

  /**
   * 座椅ID
   */
  private Integer seatId;

  /**
   * 座椅编号
   */
  private String seatNo;

  /**
   * 座椅名称
   */
  private String seatName;

  /**
   * 座椅大小
   */
  private String seatSize;

  /**
   * 座椅材质
   */
  private String seatMaterial;

  /**
   * 座椅图片
   */
  private UploadFiles seatImage;
  private UploadFiles seatImageList;

  /**
   * 座椅介绍
   */
  private String seatIntroduce;

  /**
   * 座椅价格
   */
  private Integer seatPrice;

  /**
   * 所在省
   */
  private String province;

  /**
   * 所在城市
   */
  private String city;

  /**
   * 所在区
   */
  private String strict;

  /**
   * 所在街道
   */
  private String street;

  /**
   * 详细地址
   */
  private String adress;

  /**
   * GPS经度
   */
  private Double gpsLng;

  /**
   * GPS维度
   */
  private Double gpsLat;

  /**
   * 认养状态 1:未发布 2:已发布(待认养) 3:被认养
   */
  private Integer status;

  /**
   * 省市县街道
   */
  private String pcdName;

  /**
   * 认养价格
   */
  private Integer adoptPrice;

  /**
   * 认养期限
   */
  private Integer adoptTerm;

  /**
   * 捐赠状态名称
   */
  private String statusText;

  /**
   * 建造单位
   */
  private String ownerUnit;

  /**
   * 施工单位
   */
  private String constructUnit;
}