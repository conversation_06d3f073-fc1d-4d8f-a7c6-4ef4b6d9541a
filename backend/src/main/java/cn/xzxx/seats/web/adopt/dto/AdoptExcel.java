package cn.xzxx.seats.web.adopt.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER,verticalAlignment = VerticalAlignment.CENTER)//标题样式,垂直水平居中
@HeadFontStyle(fontName = "微软雅黑",fontHeightInPoints = 11,bold = false)//表头字体样式
@HeadRowHeight(value = 25)//表头行高
@ContentFontStyle(fontName = "微软雅黑",fontHeightInPoints = 11)//内容字体样式
@ContentRowHeight(value = 20)//内容行高
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER,verticalAlignment = VerticalAlignment.CENTER)//内容样式,垂直水平居中
public class AdoptExcel {

    /**
     * 认养编号
     */
    @ExcelProperty(value = "认养编号",index = 0)
    private String adoptNo;

    /**
     * 捐赠编号
     */
    @ExcelProperty(value = "捐赠编号",index = 1)
    private String donateNo;

    /**
     * 座椅编号
     */
    @ExcelProperty(value = "座椅编号",index = 2)
    private String seatNo;

    /**
     * 座椅名称
     */
    @ExcelProperty(value = "座椅名称",index = 3)
    private String seatName;

    /**
     * 座椅大小
     */
    @ExcelProperty(value = "座椅大小",index = 4)
    private String seatSize;

    /**
     * 座椅材质
     */
    @ExcelProperty(value = "座椅材质",index = 5)
    private String seatMaterial;

    /**
     * 座椅介绍
     */
    @ExcelProperty(value = "座椅介绍",index = 6)
    private String seatIntroduce;

    /**
     * 座椅价格
     */
    @ExcelProperty(value = "座椅价格",index = 7)
    private Integer seatPrice;

    /**
     * 所在省
     */
    @ExcelProperty(value = "所在省",index = 8)
    private String province;

    /**
     * 所在城市
     */
    @ExcelProperty(value = "所在城市",index = 9)
    private String city;

    /**
     * 所在区
     */
    @ExcelProperty(value = "所在区",index = 10)
    private String strict;

    /**
     * 所在街道
     */
    @ExcelProperty(value = "所在街道",index = 11)
    private String street;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址",index = 12)
    private String adress;

    /**
     * GPS经度
     */
    @ExcelProperty(value = "GPS经度",index = 13)
    private Double gpsLng;

    /**
     * GPS维度
     */
    @ExcelProperty(value = "GPS维度",index = 14)
    private Double gpsLat;

    /**
     * 认养金额
     */
    @ExcelProperty(value = "认养金额",index = 15)
    private Integer adoptPrice;

    /**
     * 认养期限
     */
    @ExcelProperty(value = "认养期限",index = 16)
    private Integer adoptTerm;

    /**
     * 认养状态名称
     */
    @ExcelProperty(value = "认养状态",index = 17)
    private String statusText;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间",index = 18)
    private String createdAt;
}
