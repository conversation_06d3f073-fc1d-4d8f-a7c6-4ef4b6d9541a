package cn.xzxx.seats.web.certificate.dto.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class CertificateRecord implements Serializable {

    /**
     * 证书ID
     */
    private Integer id;

    /**
     * 证书URL
     */
    private String imageUrl;

    /**
     * 用户open_id
     */
    private String openId;

    /**
     * 座椅分类 1-共建 2-认养
     */
    private Integer seatType;

    /**
     * 捐赠/认养座椅ID
     */
    private Integer seatId;

    /**
     * 状态 0:不显示 1:显示
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createdAt;

}
