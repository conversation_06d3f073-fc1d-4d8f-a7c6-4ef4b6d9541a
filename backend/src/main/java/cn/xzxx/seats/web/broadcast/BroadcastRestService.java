package cn.xzxx.seats.web.broadcast;

import cn.xzxx.seats.code.BroadcastStatus;
import cn.xzxx.seats.common.response.*;
import cn.xzxx.seats.common.utils.DateUtils;
import cn.xzxx.seats.component.PulldownComponent;
import cn.xzxx.seats.repository.entity.BroadcastSettingsEntity;
import cn.xzxx.seats.repository.mapper.BroadcastSettingsMapper;
import cn.xzxx.seats.web.broadcast.dto.BroadcastForm;
import cn.xzxx.seats.web.broadcast.dto.BroadcastSearchCondition;
import cn.xzxx.seats.web.broadcast.dto.entity.BroadcastRecord;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class BroadcastRestService {

  @Autowired
  private BroadcastSettingsMapper broadcastSettingsMapper;

  @Autowired
  private PulldownComponent pulldownComponent;

  public BaseResponse listInit(BroadcastSearchCondition condition) {

    ListResponse<BroadcastRecord> listResponse = new ListResponse<>();

    List<BroadcastSettingsEntity> broadcastSettingsEntities = broadcastSettingsMapper.selectList(new LambdaQueryWrapper<BroadcastSettingsEntity>()
            .eq(condition.getStatus() !=null, BroadcastSettingsEntity::getStatus, condition.getStatus()));
    List<BroadcastRecord> records = new ArrayList<>();
    broadcastSettingsEntities.forEach(x->{
      BroadcastRecord record = new BroadcastRecord();
      record.setId(x.getId());
      record.setContent(x.getContent());
      record.setStatus(x.getStatus());
      record.setStatusName(BroadcastStatus.statusName(x.getStatus()));
      record.setCreatedAt(DateUtils.localDateTimeFormat(x.getCreatedAt()));
      records.add(record);
    });

    listResponse.setValue(records);

    if (condition.isInit()) {
      List<PulldownItem> statusOptions = pulldownComponent.getBroadcastStatusPulldown();
      listResponse.putData("statusOptions", statusOptions);
    }
    return listResponse;
  }

  public BaseResponse init(Integer id) {

    BroadcastSettingsEntity broadcastSettingsEntity = broadcastSettingsMapper.selectById(id);
    if(broadcastSettingsEntity == null) {
      return MessageResponse.newInstance("广播信息不存在，请返回列表刷新后再操作。");
    }
    BroadcastForm broadcastForm = new BroadcastForm();
    broadcastForm.setContent(broadcastSettingsEntity.getContent());
    broadcastForm.setStatus(broadcastSettingsEntity.getStatus());
    DataResponse<BroadcastForm> result = new DataResponse<>();
    return result.setValue(broadcastForm);

  }

  @Transactional
  public BaseResponse edit(boolean add, BroadcastForm form) {
    BroadcastSettingsEntity entity;
    if (add) {
      entity = new BroadcastSettingsEntity();
      entity.setContent(form.getContent());
      entity.setStatus(form.getStatus());
      entity.setCreatedAt(LocalDateTime.now());
      broadcastSettingsMapper.insert(entity);
    } else {
      entity = broadcastSettingsMapper.selectById(form.getId());
      if (entity == null) {
        return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
      }
      entity.setContent(form.getContent());
      entity.setStatus(form.getStatus());
      broadcastSettingsMapper.updateById(entity);
    }
    return new BaseResponse();
  }


  @Transactional
  public BaseResponse delete(Integer id) {

    BroadcastSettingsEntity entity = broadcastSettingsMapper.selectById(id);
    if (entity == null) {
      return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
    }
    broadcastSettingsMapper.deleteById(id);
    return new BaseResponse();
  }
}
