package cn.xzxx.seats.web.info.dto;

import cn.xzxx.seats.common.base.UploadFiles;
import lombok.Data;

@Data
public class InformationDetailsForm {

  /**
   * 资讯详情ID
   */
  private Integer id;

  /**
   * 资讯分类 null:全部 1:新闻 2:公告 3:活动
   */
  private Integer type;

  /**
   * 封面图片URL
   */
  private String imageUrl;

  private UploadFiles image;

  /**
   * 标题
   */
  private String title;

  /**
   * 内容
   */
  private String content;

  /**
   * 跳转链接
   */
  private String linkUrl;

  /**
   * 状态 0:不显示 1:显示
   */
  private Integer status;

  /**
   * 创建时间
   */
  private String createdAt;

  /**
   * 删除flag 0-否 1-是
   */
  private Boolean deleteFlag;

}