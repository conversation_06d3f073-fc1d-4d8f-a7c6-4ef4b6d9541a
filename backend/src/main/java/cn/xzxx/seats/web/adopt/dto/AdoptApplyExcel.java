package cn.xzxx.seats.web.adopt.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER,verticalAlignment = VerticalAlignment.CENTER)//标题样式,垂直水平居中
@HeadFontStyle(fontName = "微软雅黑",fontHeightInPoints = 11,bold = false)//表头字体样式
@HeadRowHeight(value = 25)//表头行高
@ContentFontStyle(fontName = "微软雅黑",fontHeightInPoints = 11)//内容字体样式
@ContentRowHeight(value = 20)//内容行高
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER,verticalAlignment = VerticalAlignment.CENTER)//内容样式,垂直水平居中
public class AdoptApplyExcel {

    /**
     * 捐赠编号
     */
    @ExcelProperty(value = "捐赠编号",index = 0)
    private String donateNo;

    /**
     * 座椅编号
     */
    @ExcelProperty(value = "座椅编号",index = 1)
    private String seatNo;

    /**
     * 座椅名称
     */
    @ExcelProperty(value = "座椅名称",index = 2)
    private String seatName;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名",index = 3)
    private String name;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号",index = 4)
    private String idCardNo;

    /**
     * 认养状态名称
     */
    @ExcelProperty(value = "认养状态",index = 5)
    private String statusText;

    /**
     * 申请时间
     */
    @ExcelProperty(value = "认养申请时间",index = 6)
    private String applyTime;

    /**
     * 申请通过时间
     */
    @ExcelProperty(value = "申请通过时间",index = 7)
    private String applyPassTime;

    /**
     * 认养有效期
     */
    @ExcelProperty(value = "认养有效期",index = 8)
    private String validTime;

    /**
     * 所在省
     */
    @ExcelProperty(value = "所在省",index = 9)
    private String province;

    /**
     * 所在城市
     */
    @ExcelProperty(value = "所在城市",index = 10)
    private String city;

    /**
     * 所在区
     */
    @ExcelProperty(value = "所在区",index = 11)
    private String strict;

    /**
     * 所在街道
     */
    @ExcelProperty(value = "所在街道",index = 12)
    private String street;

}
