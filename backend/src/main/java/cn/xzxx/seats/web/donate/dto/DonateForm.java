package cn.xzxx.seats.web.donate.dto;

import cn.xzxx.seats.common.base.UploadFiles;
import cn.xzxx.seats.common.response.PulldownItem;
import lombok.Data;

import java.util.List;

@Data
public class DonateForm {

  /**
   * 捐赠ID
   */
  private Integer id;

  /**
   * 捐赠NO
   */
  private String donateNo;

  /**
   * 座椅ID
   */
  private Integer seatId;

  /**
   * 座椅编号
   */
  private String seatNo;

  /**
   * 座椅名称
   */
  private String seatName;

  /**
   * 座椅大小
   */
  private String seatSize;

  /**
   * 座椅材质
   */
  private String seatMaterial;

  /**
   * 座椅图片
   */
  private UploadFiles seatImage;
  private UploadFiles seatImageList;

  /**
   * 座椅介绍
   */
  private String seatIntroduce;

  /**
   * 座椅价格
   */
  private Integer seatPrice;

  /**
   * 所在省
   */
  private String province;

  /**
   * 所在城市
   */
  private String city;

  /**
   * 所在区
   */
  private String strict;

  /**
   * 所在街道
   */
  private String street;

  /**
   * 详细地址
   */
  private String adress;

  /**
   * GPS经度
   */
  private Double gpsLng;

  /**
   * GPS维度
   */
  private Double gpsLat;

  /**
   * 认捐截止时间（到达截止时间 且 认捐份额>=100 ）
   */
  private String validTime;

  /**
   * 捐赠状态 0:已取消 1:未发布 2:已发布 3:捐赠中 4:捐赠完成 5:建造中 6:建造完成
   */
  private int status;

  /**
   * 捐赠状态名称
   */
  private String statusText;

  /**
   * 建造单位
   */
  private String ownerUnit;

  /**
   * 施工单位
   */
  private String constructUnit;

  private List<PulldownItem> statusOptions;
}
