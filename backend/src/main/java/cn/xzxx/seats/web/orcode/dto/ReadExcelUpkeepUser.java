package cn.xzxx.seats.web.orcode.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ReadExcelUpkeepUser {

    // 手机号
    @ExcelProperty(value = "手机号", index = 0)
    private String telephone;

    // 姓名
    @ExcelProperty(value = "姓名", index = 1)
    private String name;

    // 所属单位
    @ExcelProperty(value = "所属单位", index = 2)
    private String company;
}
