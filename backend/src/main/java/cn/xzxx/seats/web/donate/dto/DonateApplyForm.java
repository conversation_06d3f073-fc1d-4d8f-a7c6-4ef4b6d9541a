package cn.xzxx.seats.web.donate.dto;

import cn.xzxx.seats.common.base.UploadFiles;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DonateApplyForm {

  /**
   * 捐赠申请ID
   */
  private Integer id;

  /**
   * 捐赠座椅NO
   */
  private String donateNo;

  /**
   * 可捐赠座椅No
   */
  private String seatNo;

  /**
   * 可捐赠座椅名称
   */
  private String seatName;

  /**
   * 座椅大小
   */
  private String seatSize;

  /**
   * 座椅材料
   */
  private String seatMaterial;

  /**
   * 可捐赠座椅价格
   */
  private Integer seatPrice;

  /**
   * 预估金额 用于记录用户填写数据
   */
  private String predictPrice;

  /**
   * 剩余捐赠金额
   */
  private Integer payedPriceRemain;

  /**
   * 座椅图片URL
   */
  private UploadFiles seatImage;


  /**
   * 所在省
   */
  private String seatProvince;

  /**
   * 所在城市
   */
  private String seatCity;

  /**
   * 所在区
   */
  private String seatStrict;

  /**
   * 所在街道
   */
  private String seatStreet;

  /**
   * 详细地址
   */
  private String seatAdress;

  /**
   * GPS经度
   */
  private Double seatGpsLng;

  /**
   * GPS维度
   */
  private Double seatGpsLat;

  /**
   * 认捐截止时间（到达截止时间 且 认捐份额>=100 ）
   */
  private String validTime;

  /**
   * 捐赠份额（1-100）
   */
  private Integer shareNum;

  /**
   * 姓名
   */
  private String name;

  /**
   * 身份证号
   */
  private String idCardNo;

  /**
   * 性别
   */
  private Integer sex;

  /**
   * 年龄
   */
  private Integer age;

  /**
   * 手机号
   */
  private String tel;

  /**
   * 所在省
   */
  private String province;

  /**
   * 所在城市
   */
  private String city;

  /**
   * 所在区
   */
  private String strict;

  /**
   * 街道
   */
  private String street;

  /**
   * 捐赠属性 1:街道 2:个人
   */
  private Integer type;

  private UploadFiles personCertificateImage;

  private UploadFiles aptitudeCertificateImage;

  /**
   * 了解渠道 1:
   */
  private String channel;

  /**
   * 捐赠公共座椅原因
   */
  private String reason;

  /**
   * 寄语
   */
  private String donationWord;

  /**
   * 寄语是否显示 0-否 1-是
   */
  private Boolean donationWordFlag;

  /**
   * 捐赠状态 1:申请中 2:捐赠审核通过 3:不通过 4:无效
   */
  private Integer status;

  /**
   * 捐赠状态名称
   */
  private String statusText;

  /**
   * 申请时间
   */
  private String applyTime;

  /**
   * 申请通过时间
   */
  private String applyPassTime;

  /**
   * 区划代码
   */
  private String code;

  /**
   * 实际应付金额
   */
  private Integer payedPrice;

  /**
   * 实际捐赠份额
   */
  private Integer realNum;

  /**
   * 捐赠状态
   */
  private Integer statusOptions;

}