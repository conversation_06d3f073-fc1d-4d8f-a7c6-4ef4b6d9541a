package cn.xzxx.seats.web.donate;

import cn.xzxx.seats.code.DonateApplyStatus;
import cn.xzxx.seats.code.DonateCommentStatus;
import cn.xzxx.seats.code.DonateStatus;
import cn.xzxx.seats.common.base.OprType;
import cn.xzxx.seats.common.base.UploadFiles;
import cn.xzxx.seats.common.exception.BusinessException;
import cn.xzxx.seats.common.response.*;
import cn.xzxx.seats.common.utils.BeanCopierUtil;
import cn.xzxx.seats.common.utils.DateUtils;
import cn.xzxx.seats.common.utils.ListUtils;
import cn.xzxx.seats.common.utils.StringUtil;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.component.PulldownComponent;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.*;
import cn.xzxx.seats.web.common.CommonService;
import cn.xzxx.seats.web.donate.dto.*;
import cn.xzxx.seats.web.seats.SeatRestService;
import cn.xzxx.seats.web.upkeep.UpkeepAssignService;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class DonateRestService {

  @Autowired
  private DonateRestMapper donateRestMapper;

  @Autowired
  private CommonService commonService;

  @Autowired
  private DonateSeatInfoMapper donateSeatInfoMapper;

  @Autowired
  private DonateSeatApplyMapper donateSeatApplyMapper;

  @Autowired
  private SeatInfoMapper seatInfoMapper;

  @Autowired
  private SeatImageMapper seatImageMapper;

  @Autowired
  private AdoptSeatInfoMapper adoptSeatInfoMapper;

  @Autowired
  private FileUploadComponent fileUploadComponent;

  @Autowired
  private PulldownComponent pulldownComponent;

  @Autowired
  private DonateCommentMapper donateCommentMapper;

  @Autowired
  private SeatRestService seatRestService;

  @Autowired
  private UpkeepAssignService upkeepAssignService;


  public BaseResponse listInit(DonateSearchCondition condition) {

    ListResponse<DonateRecord> listResponse = new ListResponse<>();
    condition.setDefaultSort(OrderItem.desc("di.created_at"));
    List<DonateRecord> dataList = getDonateRecords(condition, listResponse);
    listResponse.setValue(dataList);
    return listResponse;
  }

  public BaseResponse init(DonateParam param) {
    OprType optType = param.getOptType();

    DataResponse<DonateForm> response = new DataResponse<>();

    if (optType.isAdd()) {
      List<PulldownItem> statusOptions = pulldownComponent.getDonateStatusPulldown();
      response.putData("statusOptions", statusOptions);
      return response;
    }
    String id = param.getId();
    DonateSeatInfoEntity donateSeatInfoEntity = donateSeatInfoMapper.selectById(id);
    if(donateSeatInfoEntity == null) {
      return MessageResponse.newInstance("捐赠信息不存在，请返回列表刷新后再操作。");
    }

    SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(donateSeatInfoEntity.getSeatId());
    DonateForm donateForm = new DonateForm();
    BeanUtils.copyProperties(donateSeatInfoEntity, donateForm);
    donateForm.setSeatNo(seatInfoEntity.getSeatNo());
    donateForm.setSeatName(seatInfoEntity.getName());
    donateForm.setSeatMaterial(seatInfoEntity.getMaterial());
    donateForm.setSeatSize(seatInfoEntity.getSize());
    donateForm.setSeatPrice(seatInfoEntity.getPrice());
    donateForm.setOwnerUnit(seatInfoEntity.getOwnerUnit());
    donateForm.setConstructUnit(seatInfoEntity.getConstructUnit());
    if (!StringUtil.isEmpty(seatInfoEntity.getImage())) {
      UploadFiles uploadFiles = new UploadFiles();
      uploadFiles.addFile(seatInfoEntity.getImage(), "", fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
      donateForm.setSeatImage(uploadFiles);
    }
    List<SeatImageEntity> seatImageEntities = seatImageMapper.selectList(new LambdaQueryWrapper<SeatImageEntity>()
            .eq(SeatImageEntity::getSeatId, seatInfoEntity.getId()));
    if(!CollectionUtils.isEmpty(seatImageEntities)) {
      UploadFiles imageFiles = new UploadFiles();
      seatImageEntities.forEach(image->{
        imageFiles.addFile(image.getImage(), "", fileUploadComponent.getImageUrl(image.getImage()));
      });
      donateForm.setSeatImageList(imageFiles);
    }
    donateForm.setSeatIntroduce(seatInfoEntity.getIntroduce());
    donateForm.setValidTime(DateUtils.localDateTimeFormat(donateSeatInfoEntity.getValidTime()));
    donateForm.setStatusText(DonateStatus.statusName(donateSeatInfoEntity.getStatus()));
    donateForm.setGpsLng(donateSeatInfoEntity.getGpsLngBmap());
    donateForm.setGpsLat(donateSeatInfoEntity.getGpsLatBmap());

    List<PulldownItem> statusOptions = pulldownComponent.getDonateStatusPulldown();
    response.putData("statusOptions", statusOptions);

    return response.setValue(donateForm);

  }

  @Transactional
  public BaseResponse edit(boolean add, DonateForm form) {
    DonateSeatInfoEntity entity;
    SeatInfoEntity seatEntity;
    UploadFiles image = form.getSeatImage();
    UploadFiles imageList = form.getSeatImageList();
    if(image == null) {
      return MessageResponse.newInstance("保存失败，请设置座椅主图后再进行保存！");
    }
    List<UploadFiles.RawFile> addedFiles = image.addedFiles();
    List<UploadFiles.RawFile> deleteFiles = image.deletedFiles();

    if (add) {

      Integer donateCount = donateSeatInfoMapper.selectCount(new LambdaUpdateWrapper<DonateSeatInfoEntity>()
              .eq(DonateSeatInfoEntity::getDonateNo, form.getDonateNo()));
      if(donateCount > 0){
        return MessageResponse.newInstance("保存失败，捐赠编号已存在！");
      }

      seatEntity = new SeatInfoEntity();
      setSeatInfo(form, seatEntity);
      if (ListUtils.isNotEmpty(addedFiles)) {
        UploadFiles.RawFile rawFile = addedFiles.get(0);
        String fileUrl = fileUploadComponent.add(rawFile);
        seatEntity.setImage(fileUrl);
      }
      seatInfoMapper.insert(seatEntity);

      entity = new DonateSeatInfoEntity();
      entity.setSeatId(seatEntity.getId());
      form.setStatus(DonateStatus.PUBLISH_WAIT.status());
      setDonateInfo(form, entity);
      donateSeatInfoMapper.insert(entity);

    } else {

      entity = donateSeatInfoMapper.selectById(form.getId());
      if (entity == null) {
        return MessageResponse.newInstance("捐赠信息不存在，请返回列表刷新后再操作。");
      }

      if(!Objects.equals(entity.getDonateNo(), form.getDonateNo())) {
        Integer donateCount = donateSeatInfoMapper.selectCount(new LambdaUpdateWrapper<DonateSeatInfoEntity>()
                .eq(DonateSeatInfoEntity::getDonateNo, form.getDonateNo()));
        if(donateCount > 0){
          return MessageResponse.newInstance("保存失败，捐赠编号已存在！");
        }
      }

      seatEntity = seatInfoMapper.selectById(entity.getSeatId());
      if (seatEntity == null) {
        return MessageResponse.newInstance("捐赠座椅信息不存在，请返回列表刷新后再操作。");
      }
      setSeatInfo(form, seatEntity);
      if (ListUtils.isNotEmpty(addedFiles)) {
        UploadFiles.RawFile rawFile = addedFiles.get(0);
        String fileUrl = fileUploadComponent.add(rawFile);
        seatEntity.setImage(fileUrl);
      }
      seatInfoMapper.updateById(seatEntity);

      setDonateInfo(form, entity);
      donateSeatInfoMapper.updateById(entity);
    }

    // 座椅图片处理
    if (ListUtils.isNotEmpty(deleteFiles)) {
      fileUploadComponent.delete(deleteFiles.get(0));
    }
    if (imageList != null) {
      List<UploadFiles.RawFile> addedImageList = imageList.addedFiles();
      List<UploadFiles.RawFile> deletedImageList = imageList.deletedFiles();

      if (ListUtils.isNotEmpty(addedImageList)) {
        for (UploadFiles.RawFile rawFile : addedImageList) {
          String fileUrl = fileUploadComponent.add(rawFile);
          SeatImageEntity imageEntity = new SeatImageEntity();
          imageEntity.setSeatId(seatEntity.getId());
          imageEntity.setImage(fileUrl);
          imageEntity.setRemarks("");
          seatImageMapper.insert(imageEntity);
        }
      }

      if (ListUtils.isNotEmpty(deletedImageList)) {
        for (UploadFiles.RawFile rawFile : deletedImageList) {
          String fileId = rawFile.getFileId();
          fileUploadComponent.delete(rawFile);
          seatImageMapper.delete(new QueryWrapper<SeatImageEntity>().eq("image", fileId));
        }
      }
    }
    if(add) {
      return MessageResponse.newInstance().addSuccessMessage("捐赠信息新增成功！");
    } else {
      return MessageResponse.newInstance().addSuccessMessage("捐赠信息更新成功！");
    }
  }

  public BaseResponse applyListInit(DonateSearchCondition condition) {

    ListResponse<DonateApplyRecord> listResponse = new ListResponse<>();
    condition.setDefaultSort(OrderItem.desc("da.apply_time"));
    List<DonateApplyRecord> dataList = getDonateApplyRecords(condition, listResponse);
    listResponse.setValue(dataList);

    if (condition.isInit()) {
      List<PulldownItem> statusOptions = pulldownComponent.getDonateApplyStatusPulldown();
      listResponse.putData("statusOptions", statusOptions);
    }

    return listResponse;
  }

  /**
   * 捐赠申请信息取得
   * @param condition
   * @param listResponse
   * @return
   */
  private List<DonateApplyRecord> getDonateApplyRecords(DonateSearchCondition condition, ListResponse<DonateApplyRecord> listResponse) {
    List<DonateApplyRecord> dataList = donateRestMapper.selectApplyDataList(condition, listResponse, e -> {
      DonateApplyRecord record = new DonateApplyRecord();
      BeanUtils.copyProperties(e, record);
      record.setStatusText(DonateApplyStatus.statusName(e.getStatus()));
      record.setPcdName(commonService.getPcdName(e.getProvince(), e.getCity(), e.getStrict(), e.getStreet()));
      record.setApplyTime(DateUtils.localDateTimeFormat(e.getApplyTime()));
      record.setApplyPassTime(DateUtils.localDateTimeFormat(e.getApplyPassTime()));
      record.setValidTime(DateUtils.localDateTimeFormat(e.getValidTime()));
      return record;
    });
    return dataList;
  }

  public BaseResponse initApply(DonateParam param) {

    OprType optType = param.getOptType();
    DataResponse<DonateApplyForm> response = new DataResponse<>();
    if (optType.isAdd()) {
      List<PulldownItem> donateApplyTypeOptions = pulldownComponent.getDonateApplyTypePulldown();
      response.putData("donateApplyTypeOptions", donateApplyTypeOptions);
      return response;
    }
    String id = param.getId();

    DonateSeatApplyEntity donateSeatApplyEntity = donateSeatApplyMapper.selectById(id);
    if(donateSeatApplyEntity == null) {
      return MessageResponse.newInstance("捐赠申请信息不存在，请返回列表刷新后再操作。");
    }

    DonateSeatInfoEntity donateSeatInfoEntity = donateSeatInfoMapper.selectById(donateSeatApplyEntity.getDonateId());
    if(donateSeatInfoEntity == null) {
      return MessageResponse.newInstance("捐赠信息不存在，请返回列表刷新后再操作。");
    }
    SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(donateSeatInfoEntity.getSeatId());
    if(seatInfoEntity == null) {
      return MessageResponse.newInstance("座椅信息不存在，请返回列表刷新后再操作。");
    }

    List<DonateSeatApplyEntity> donateSeatApplyEntities = donateSeatApplyMapper.selectList(new LambdaUpdateWrapper<DonateSeatApplyEntity>()
            .eq(DonateSeatApplyEntity::getDonateId, donateSeatApplyEntity.getDonateId())
            .in(DonateSeatApplyEntity::getStatus,2,5,6));
    int payedPriceSum = donateSeatApplyEntities.stream().mapToInt(t-> t.getPayedPrice() == null? 0: t.getPayedPrice()).sum();
    int payedPriceRemain = seatInfoEntity.getPrice() - payedPriceSum;

    DonateApplyForm donateApplyForm = new DonateApplyForm();
    BeanUtils.copyProperties(donateSeatApplyEntity, donateApplyForm);
    donateApplyForm.setValidTime(DateUtils.localDateTimeFormat(donateSeatInfoEntity.getValidTime()));
    donateApplyForm.setApplyTime(DateUtils.localDateTimeFormat(donateSeatApplyEntity.getApplyTime()));
    donateApplyForm.setApplyPassTime(DateUtils.localDateTimeFormat(donateSeatApplyEntity.getApplyPassTime()));
    if (!StringUtil.isEmpty(donateSeatApplyEntity.getAptitudeCertificateImage())) {
      UploadFiles aptitudeFiles = new UploadFiles();
      aptitudeFiles.addFile(donateSeatApplyEntity.getAptitudeCertificateImage(), "", fileUploadComponent.getImageUrl(donateSeatApplyEntity.getAptitudeCertificateImage()));
      donateApplyForm.setAptitudeCertificateImage(aptitudeFiles);
    }
    if (!StringUtil.isEmpty(donateSeatApplyEntity.getPersonCertificateImage())) {
      UploadFiles personFiles = new UploadFiles();
      personFiles.addFile(donateSeatApplyEntity.getPersonCertificateImage(), "", fileUploadComponent.getImageUrl(donateSeatApplyEntity.getPersonCertificateImage()));
      donateApplyForm.setPersonCertificateImage(personFiles);
    }
    donateApplyForm.setDonateNo(donateSeatInfoEntity.getDonateNo());
    donateApplyForm.setSeatNo(seatInfoEntity.getSeatNo());
    donateApplyForm.setSeatName(seatInfoEntity.getName());
    donateApplyForm.setSeatPrice(seatInfoEntity.getPrice());
    donateApplyForm.setSeatSize(seatInfoEntity.getSize());
    donateApplyForm.setSeatMaterial(seatInfoEntity.getMaterial());
    donateApplyForm.setPayedPriceRemain(payedPriceRemain);
    donateApplyForm.setSeatProvince(donateSeatInfoEntity.getProvince());
    donateApplyForm.setSeatCity(donateSeatInfoEntity.getCity());
    donateApplyForm.setSeatStrict(donateSeatInfoEntity.getStrict());
    donateApplyForm.setSeatStreet(donateSeatInfoEntity.getStreet());
    donateApplyForm.setSeatAdress(donateSeatInfoEntity.getAdress());
//    GPS gps = GpsUtils.map_tx2bd(donateSeatInfoEntity.getGpsLat().toString(), donateSeatInfoEntity.getGpsLng().toString());
    donateApplyForm.setSeatGpsLat(donateSeatInfoEntity.getGpsLat());
    donateApplyForm.setSeatGpsLng(donateSeatInfoEntity.getGpsLng());
    donateApplyForm.setStatusText(DonateApplyStatus.statusName(donateSeatApplyEntity.getStatus()));
    if(donateApplyForm.getRealNum() == null) {
      donateApplyForm.setRealNum(donateSeatApplyEntity.getShareNum());
    }
    if (!StringUtil.isEmpty(seatInfoEntity.getImage())) {
      UploadFiles uploadFiles = new UploadFiles();
      uploadFiles.addFile(seatInfoEntity.getImage(), "", fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
      donateApplyForm.setSeatImage(uploadFiles);
    }

    List<PulldownItem> donateApplyTypeOptions = pulldownComponent.getDonateApplyTypePulldown();
    response.putData("donateApplyTypeOptions", donateApplyTypeOptions);

    return response.setValue(donateApplyForm);

  }

  @Transactional
  public BaseResponse editApply(boolean add, DonateApplyForm form) {

    DonateSeatApplyEntity donateSeatApply;
    UploadFiles aptitudeImage = form.getAptitudeCertificateImage();
    UploadFiles personImage = form.getPersonCertificateImage();

    if (add) {

      donateSeatApply = new DonateSeatApplyEntity();
      // 通过捐赠编号取得对应捐赠信息
      DonateSeatInfoEntity donateSeatInfo = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
              .eq(DonateSeatInfoEntity::getDonateNo, form.getDonateNo()).eq(DonateSeatInfoEntity::getDeleteFlag, false).one();
      if (donateSeatInfo == null) return MessageResponse.newInstance("捐赠信息不存在，请确认捐赠编号是否正确!");

      SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(donateSeatInfo.getSeatId());
      if (seatInfoEntity == null) return MessageResponse.newInstance("座椅信息不存在，请确认捐赠信息中的座椅是否添加!");

      List<DonateSeatApplyEntity> donateSeatApplyEntities = donateSeatApplyMapper.selectList(new LambdaQueryWrapper<DonateSeatApplyEntity>()
              .eq(DonateSeatApplyEntity::getDonateId, donateSeatInfo.getId())
              .in(DonateSeatApplyEntity::getStatus,2,5,6)
              .eq(DonateSeatApplyEntity::getDeleteFlag, false));
      int payedPriceSum = donateSeatApplyEntities.stream().mapToInt(t-> t.getPayedPrice() == null? 0: t.getPayedPrice()).sum();
      int payedPriceRemain = seatInfoEntity.getPrice() - payedPriceSum - form.getPayedPrice();
      if(payedPriceRemain < 0) {
        return MessageResponse.newInstance("捐赠金额已超过最大捐赠金额，请重新输入!剩余捐赠金额：" + (seatInfoEntity.getPrice() - payedPriceSum));
      }

      BeanCopierUtil.copy(form, donateSeatApply);
      donateSeatApply.setId(null);
      donateSeatApply.setDonateId(donateSeatInfo.getId());
      donateSeatApply.setOpenId("system");
      donateSeatApply.setPredictPrice(form.getPayedPrice().toString());
      donateSeatApply.setShareNum(form.getRealNum());
      donateSeatApply.setStatus(2);
      donateSeatApply.setApplyTime(LocalDateTime.now());
      donateSeatApply.setApplyPassTime(LocalDateTime.now());
      donateSeatApply.setUpdatedBy("system");

      if(aptitudeImage != null) {
        List<UploadFiles.RawFile> addedAptitudeFiles = aptitudeImage.addedFiles();
        List<UploadFiles.RawFile> deleteAptitudeFiles = aptitudeImage.deletedFiles();
        if (ListUtils.isNotEmpty(addedAptitudeFiles)) {
          UploadFiles.RawFile rawFile = addedAptitudeFiles.get(0);
          String fileUrl = fileUploadComponent.add(rawFile);
          donateSeatApply.setAptitudeCertificateImage(fileUrl);
        }
      }
      if(personImage != null) {
        List<UploadFiles.RawFile> addedPersonFiles = personImage.addedFiles();
        List<UploadFiles.RawFile> deletePersonFiles = personImage.deletedFiles();
        if (ListUtils.isNotEmpty(addedPersonFiles)) {
          UploadFiles.RawFile personRawFile = addedPersonFiles.get(0);
          String personFileUrl = fileUploadComponent.add(personRawFile);
          donateSeatApply.setPersonCertificateImage(personFileUrl);
        }
      }

      donateSeatApplyMapper.insert(donateSeatApply);

      // 如果申请份额为100份满份额，修改座椅的状态为被满额申请
      if (form.getRealNum() == 100) {
        donateSeatInfo.setStatus(DonateStatus.APPLY_FINISH.status());
        donateSeatInfoMapper.updateById(donateSeatInfo);
      }

    } else {

      donateSeatApply = donateSeatApplyMapper.selectById(form.getId());
      if (donateSeatApply == null) {
        return MessageResponse.newInstance("捐赠信息不存在，请返回列表刷新后再操作。");
      }
      if(aptitudeImage != null) {
        List<UploadFiles.RawFile> addedAptitudeFiles = aptitudeImage.addedFiles();
        if (ListUtils.isNotEmpty(addedAptitudeFiles)) {
          UploadFiles.RawFile rawFile = addedAptitudeFiles.get(0);
          String fileUrl = fileUploadComponent.add(rawFile);
          donateSeatApply.setAptitudeCertificateImage(fileUrl);
        }
      }
      if(personImage != null) {
        List<UploadFiles.RawFile> addedPersonFiles = personImage.addedFiles();
        if (ListUtils.isNotEmpty(addedPersonFiles)) {
          UploadFiles.RawFile personRawFile = addedPersonFiles.get(0);
          String personFileUrl = fileUploadComponent.add(personRawFile);
          donateSeatApply.setPersonCertificateImage(personFileUrl);
        }
      }
      BeanCopierUtil.copy(form, donateSeatApply);
      donateSeatApply.setPredictPrice(form.getPayedPrice().toString());
      donateSeatApply.setShareNum(form.getRealNum());
      donateSeatApply.setApplyPassTime(DateUtils.localDateTimeParse(form.getApplyPassTime()));
      donateSeatApplyMapper.updateById(donateSeatApply);


      // 如果申请份额为100份满份额，修改座椅的状态为被满额申请
      List<DonateSeatApplyEntity> donateSeatApplyEntities = donateSeatApplyMapper.selectList(new LambdaQueryWrapper<DonateSeatApplyEntity>()
              .eq(DonateSeatApplyEntity::getDonateId, donateSeatApply.getDonateId())
              .in(DonateSeatApplyEntity::getStatus, 1,2,5,6));
      int sum = donateSeatApplyEntities.stream().mapToInt(DonateSeatApplyEntity::getRealNum).sum();
      if(sum > 100) {
        return MessageResponse.newInstance("已满额申请，无法新增捐赠信息。");
      }
      if (sum == 100) {
        DonateSeatInfoEntity donateSeatInfoEntity = donateSeatInfoMapper.selectById(donateSeatApply.getDonateId());
        if(donateSeatInfoEntity.getStatus() == 2 || donateSeatInfoEntity.getStatus() == 3) {
          donateSeatInfoEntity.setId(donateSeatApply.getDonateId());
          donateSeatInfoEntity.setStatus(DonateStatus.APPLY_FINISH.status());
          donateSeatInfoMapper.updateById(donateSeatInfoEntity);
        }
      }
    }

    if(aptitudeImage != null) {
      List<UploadFiles.RawFile> deleteAptitudeFiles = aptitudeImage.deletedFiles();
      if (ListUtils.isNotEmpty(deleteAptitudeFiles)) {
        fileUploadComponent.delete(deleteAptitudeFiles.get(0));
      }
    }
    if(personImage != null) {
      List<UploadFiles.RawFile> deletePersonFiles = personImage.deletedFiles();
      if (ListUtils.isNotEmpty(deletePersonFiles)) {
        fileUploadComponent.delete(deletePersonFiles.get(0));
      }
    }

    if(add) {
      return MessageResponse.newInstance().addSuccessMessage("捐赠信息新增成功！");
    } else {
      return MessageResponse.newInstance().addSuccessMessage("捐赠信息更新成功！");
    }
  }

  private void setDonateInfo(DonateForm form, DonateSeatInfoEntity entity) {
    entity.setDonateNo(form.getDonateNo());
    entity.setProvince(form.getProvince());
    entity.setCity(form.getCity());
    entity.setStrict(form.getStrict());
    entity.setStreet(form.getStreet());
    entity.setAdress(form.getAdress());
//    GPS gps = GpsUtils.BdMapToTxMap(form.getGpsLat().toString(), form.getGpsLng().toString());
    entity.setGpsLng(form.getGpsLng());
    entity.setGpsLat(form.getGpsLat());
    entity.setGpsLatBmap(form.getGpsLat());
    entity.setGpsLngBmap(form.getGpsLng());
    entity.setValidTime(DateUtils.localDateTimeParse(form.getValidTime()));
    entity.setStatus(form.getStatus());
  }

  @Transactional
  public BaseResponse updStatus(DonateForm form) {
      DonateSeatInfoEntity entity = donateSeatInfoMapper.selectById(form.getId());
      if (entity == null) {
        return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
      }
      entity.setStatus(form.getStatus());
      donateSeatInfoMapper.updateById(entity);

      // 建造完成 时则记录 养护管理表
      if(DonateStatus.BUILD_FINISH.status() == form.getStatus()) {
        upkeepAssignService.insUpkeepAssign(entity.getSeatId(), 1);
      }


//      // 建造完成时则作为认养信息
//      if(DonateStatus.BUILD_FINISH.status() == form.getStatus()) {
//
//        AdoptSeatInfoEntity adoptSeatInfoEntity = new AdoptSeatInfoEntity();
//        BeanUtils.copyProperties(entity, adoptSeatInfoEntity,"updatedAt", "updatedBy");
//        adoptSeatInfoEntity.setAdoptNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
//        adoptSeatInfoEntity.setDonateId(entity.getId());
//        adoptSeatInfoEntity.setStatus(AdoptStatus.PUBLISH_WAIT.status());
//        adoptSeatInfoEntity.setUpdatedBy("system");
//        adoptSeatInfoMapper.insert(adoptSeatInfoEntity);
//      }
      return new BaseResponse();
  }

  @Transactional
  public BaseResponse updStatusApply(DonateApplyForm form) {

    DonateSeatApplyEntity entity = donateSeatApplyMapper.selectById(form.getId());
    if (entity == null) {
      return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
    }
    entity.setStatus(form.getStatus());
    if (DonateApplyStatus.APPLY_PASS.status() == form.getStatus()) {
      entity.setApplyPassTime(LocalDateTime.now());
      entity.setRealNum(form.getRealNum());
      entity.setPayedPrice(form.getPayedPrice());
    } else if(DonateApplyStatus.APPLY_SHARE_WAIT.status() == form.getStatus()) {

      // 检查份额总和不能大于100 -> 调整为金额
      List<DonateSeatApplyEntity> seatApplyEntities = donateSeatApplyMapper.selectList(new LambdaQueryWrapper<DonateSeatApplyEntity>()
              .eq(DonateSeatApplyEntity::getDonateId, entity.getDonateId())
              .in(DonateSeatApplyEntity::getStatus,2,5,6));
//      int realNumSum = seatApplyEntities.stream().mapToInt(t-> t.getRealNum() == null? 0: t.getRealNum()).sum();
//      if(realNumSum + form.getRealNum() > 100) {
//        return MessageResponse.newInstance("保存失败！份额已超过上限，可调整剩余份额：" + (100-realNumSum));
//      }
      int payedPriceSum = seatApplyEntities.stream().mapToInt(t-> t.getPayedPrice() == null? 0: t.getPayedPrice()).sum();
      int payedPriceTotal = payedPriceSum + form.getPayedPrice();

      DonateSeatInfoEntity donateSeatInfoEntity = donateSeatInfoMapper.selectById(entity.getDonateId());
      SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(donateSeatInfoEntity.getSeatId());

      if(payedPriceTotal > seatInfoEntity.getPrice()) {
        return MessageResponse.newInstance("保存失败！金额已超过上限，可调整剩余金额：" + (seatInfoEntity.getPrice() - payedPriceSum));
      }
      entity.setRealNum(form.getRealNum());
      entity.setPayedPrice(form.getPayedPrice());
    }
    if (DonateApplyStatus.APPLY_PASS.status() != form.getStatus()) {
      entity.setApplyPassTime(null);
    }
    donateSeatApplyMapper.updateById(entity);

    // 被满额申请状态时，申请未通过3或无效4时，满额状态修改
    DonateSeatInfoEntity donateSeatInfoEntity = donateSeatInfoMapper.selectById(entity.getDonateId());
    if(donateSeatInfoEntity.getStatus() == DonateStatus.APPLY_FINISH.status()
            && DonateApplyStatus.APPLY_REFUSE.status() == form.getStatus()
            && DonateApplyStatus.APPLY_INEFFECTIVE.status() == form.getStatus()) {
      List<DonateSeatApplyEntity> donateSeatApplyEntities = donateSeatApplyMapper.selectList(new LambdaQueryWrapper<DonateSeatApplyEntity>()
              .eq(DonateSeatApplyEntity::getDonateId, entity.getDonateId())
              .in(DonateSeatApplyEntity::getStatus,
                      DonateApplyStatus.APPLY_WAIT.status(),
                      DonateApplyStatus.APPLY_PASS.status(),
                      DonateApplyStatus.APPLY_SHARE_WAIT.status(),
                      DonateApplyStatus.APPLY_SHARE_CONFIRM.status()));
      int sum = donateSeatApplyEntities.stream().mapToInt(DonateSeatApplyEntity::getRealNum).sum();
      if (sum < 100) {
          donateSeatInfoEntity.setStatus(DonateStatus.DONATE_WAIT.status());
          donateSeatInfoMapper.updateById(donateSeatInfoEntity);
      }
    }

    return MessageResponse.newInstance().addSuccessMessage("保存成功！");
  }

  /**
   * Excel导出（捐赠）
   * @param os
   * @param condition
   * @return
   */
  public void exportDonate(ByteArrayOutputStream os, DonateSearchCondition condition) {

    condition.setPageNumber(0);
    condition.setPageSize(-1);
    ListResponse<DonateRecord> listResponse = new ListResponse<>();
    condition.setDefaultSort(OrderItem.desc("da.apply_time"));
    List<DonateRecord> dataList = getDonateRecords(condition, listResponse);

    List<DonateExcel> excelDataList = new ArrayList<>();
    dataList.forEach(data->{
      DonateExcel excelData = new DonateExcel();
      BeanUtils.copyProperties(data, excelData);
      excelDataList.add(excelData);
    });
    EasyExcel.write(os, DonateExcel.class).sheet(0).doWrite(excelDataList);
  }

  /**
   * Excel导出（捐赠申请）
   * @param os
   * @param condition
   * @return
   */
  public void exportDonateApply(ByteArrayOutputStream os, DonateSearchCondition condition) {

    condition.setPageNumber(0);
    condition.setPageSize(-1);
    ListResponse<DonateApplyRecord> listResponse = new ListResponse<>();
    condition.setDefaultSort(OrderItem.desc("da.apply_time"));
    List<DonateApplyRecord> dataList = getDonateApplyRecords(condition, listResponse);

    List<DonateApplyExcel> excelDataList = new ArrayList<>();
    dataList.forEach(data->{
      DonateApplyExcel excelData = new DonateApplyExcel();
      BeanUtils.copyProperties(data, excelData);
      excelDataList.add(excelData);
    });
    EasyExcel.write(os, DonateApplyExcel.class).sheet(0).doWrite(excelDataList);
  }

  /**
   * 捐赠信息删除
   * @param id
   * @return
   */
  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse delete(String id) {

    List<DonateSeatApplyEntity> donateSeatApplyEntities = donateSeatApplyMapper.selectList(
            new LambdaQueryWrapper<DonateSeatApplyEntity>().eq(DonateSeatApplyEntity::getDonateId, id));

    if(!CollectionUtils.isEmpty(donateSeatApplyEntities)){
      throw new BusinessException("该捐赠信息已存在申请记录，请删除后再操作。");
    }

    // 座椅信息删除
    DonateSeatInfoEntity donateSeatInfoEntity = donateSeatInfoMapper.selectById(id);
    SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(donateSeatInfoEntity.getSeatId());
    fileUploadComponent.delete(seatInfoEntity.getImage());
    seatInfoMapper.deleteById(seatInfoEntity.getId());

    // 座椅图片删除
    List<SeatImageEntity> imageEntityList = seatImageMapper.selectList(new LambdaQueryWrapper<SeatImageEntity>()
            .eq(SeatImageEntity::getSeatId, seatInfoEntity.getId()));

    imageEntityList.forEach(image->{
      fileUploadComponent.delete(image.getImage());
      seatImageMapper.deleteById(image.getId());
    });

    // 捐赠删除
    donateSeatInfoMapper.deleteById(id);

    return MessageResponse.newInstance().addSuccessMessage("捐赠删除成功！");
  }

  /**
   * 捐赠申请删除
   * @param id
   * @return
   */
  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse deleteApply(String id) {

    DonateSeatApplyEntity donateSeatApplyEntity = donateSeatApplyMapper.selectById(id);
    DonateSeatInfoEntity donateSeatInfoEntity = donateSeatInfoMapper.selectById(donateSeatApplyEntity.getDonateId());
    if(donateSeatInfoEntity.getStatus() == DonateStatus.BUILD_WAIT.status()
            || donateSeatInfoEntity.getStatus() == DonateStatus.BUILD_FINISH.status()) {
      throw new BusinessException("该捐赠信息已在建造中或建造完成，无法删除。");
    }
    donateSeatApplyMapper.deleteById(id);

    // 共建状态更新
    List<DonateSeatApplyEntity> donateSeatApplyEntities = donateSeatApplyMapper.selectList(new LambdaQueryWrapper<DonateSeatApplyEntity>()
            .eq(DonateSeatApplyEntity::getDonateId, donateSeatApplyEntity.getDonateId()));
    int sum = donateSeatApplyEntities.stream().mapToInt(t -> t.getRealNum() == null ? 0 : t.getRealNum()).sum();

    if(sum == 0) {
      donateSeatInfoEntity.setStatus(2);
      donateSeatInfoMapper.updateById(donateSeatInfoEntity);
    } else {
      donateSeatInfoEntity.setStatus(3);
      donateSeatInfoMapper.updateById(donateSeatInfoEntity);
    }

    return MessageResponse.newInstance().addSuccessMessage("捐赠申请删除成功！");
  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse cancel(String id) {

    DonateSeatInfoEntity entity = donateSeatInfoMapper.selectById(id);
    if(entity == null || entity.getStatus() != 2) {
      throw new BusinessException("该捐赠信息不在发布状态，无法取消发布，请确认。");
    }
    // 认养取消发布
    entity.setStatus(1);
    donateSeatInfoMapper.updateById(entity);

    return MessageResponse.newInstance().addSuccessMessage("取消发布成功！");
  }

  public BaseResponse commentListInit(DonateSearchCondition condition) {

    ListResponse<DonateCommentRecord> listResponse = new ListResponse<>();
    condition.setDefaultSort(OrderItem.desc("dc.created_at"));

    List<DonateCommentRecord> dataList = donateRestMapper.selectCommentDataList(condition, listResponse, e -> {
      DonateCommentRecord record = new DonateCommentRecord();
      BeanUtils.copyProperties(e, record);
      record.setStatusText(DonateCommentStatus.statusName(e.getStatus()));
      record.setCreatedAt(DateUtils.localDateTimeFormat(e.getCreatedAt()));
      return record;
    });
    listResponse.setValue(dataList);

    if (condition.isInit()) {
      List<PulldownItem> statusOptions = pulldownComponent.getDonateCommentStatusPulldown();
      listResponse.putData("statusOptions", statusOptions);
    }

    return listResponse;
  }

  @Transactional
  public BaseResponse updStatusComment(DonateCommentForm form) {
    DonateCommentEntity entity = donateCommentMapper.selectById(form.getId());
    if (entity == null) {
      return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
    }
    entity.setStatus(form.getStatus());
    donateCommentMapper.updateById(entity);

    return new BaseResponse();
  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse deleteComment(String id) {

    donateCommentMapper.deleteById(id);

    return MessageResponse.newInstance().addSuccessMessage("该评论删除成功！");
  }

  private void setSeatInfo(DonateForm form, SeatInfoEntity seatEntity) {
    seatEntity.setSeatNo(form.getSeatNo());
    seatEntity.setName(form.getSeatName());
    seatEntity.setSize(form.getSeatSize());
    seatEntity.setMaterial(form.getSeatMaterial());
    seatEntity.setIntroduce(form.getSeatIntroduce());
    seatEntity.setPrice(form.getSeatPrice());
    seatEntity.setOwnerUnit(form.getOwnerUnit());
    seatEntity.setConstructUnit(form.getConstructUnit());
  }

  private List<DonateRecord> getDonateRecords(DonateSearchCondition condition, ListResponse<DonateRecord> listResponse) {
    List<DonateRecord> dataList = donateRestMapper.selectDataList(condition, listResponse, e -> {
      DonateRecord record = new DonateRecord();
      BeanUtils.copyProperties(e, record);
      record.setStatusText(DonateStatus.statusName(e.getStatus()));
      record.setPcdName(commonService.getPcdName(e.getProvince(), e.getCity(), e.getStrict(), e.getStreet()));
      record.setValidTime(DateUtils.localDateTimeFormat(e.getValidTime()));
      record.setCreatedAt(DateUtils.localDateTimeFormat(e.getCreatedAt()));
      if(!StringUtil.isEmpty(e.getSeatImage())) {
        UploadFiles uploadFiles = new UploadFiles();
        uploadFiles.addFile(e.getSeatImage(), "", fileUploadComponent.getImageUrl(e.getSeatImage()));
        record.setSeatImage(uploadFiles);
      }
      return record;
    });
    return dataList;
  }

  @Transactional
  public void insDonateInfo(DonateExcel donateExcel) {

    DonateForm form = new DonateForm();
    BeanUtils.copyProperties(donateExcel, form);

    SeatInfoEntity seatEntity = new SeatInfoEntity();
    setSeatInfo(form, seatEntity);
    seatInfoMapper.insert(seatEntity);

    DonateSeatInfoEntity entity = new DonateSeatInfoEntity();
    entity.setSeatId(seatEntity.getId());
    form.setStatus(DonateStatus.PUBLISH_WAIT.status());
    setDonateInfo(form, entity);
    entity.setCreatedAt(LocalDateTime.now());
    donateSeatInfoMapper.insert(entity);
  }

  /**
   * 手动捐赠申请
   * @param donateApplyForm
   * @return
   */
  @Transactional(rollbackFor = Exception.class)
  public BaseResponse donateApply(DonateApplyForm donateApplyForm) {

    // 通过捐赠编号取得对应捐赠信息
    DonateSeatInfoEntity donateSeatInfo = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
            .eq(DonateSeatInfoEntity::getDonateNo, donateApplyForm.getDonateNo()).one();
    if (donateSeatInfo == null) return MessageResponse.newInstance("捐赠信息不存在，请确认捐赠编号是否正确!");

    SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(donateSeatInfo.getSeatId());
    if (seatInfoEntity == null) return MessageResponse.newInstance("座椅信息不存在，请确认捐赠信息中的座椅是否添加!");

    List<DonateSeatApplyEntity> donateSeatApplyEntities = donateSeatApplyMapper.selectList(new LambdaQueryWrapper<DonateSeatApplyEntity>()
            .eq(DonateSeatApplyEntity::getDonateId, donateSeatInfo.getId())
            .in(DonateSeatApplyEntity::getStatus,2,5,6)
            .eq(DonateSeatApplyEntity::getDeleteFlag, false));
    int payedPriceSum = donateSeatApplyEntities.stream().mapToInt(t-> t.getPayedPrice() == null? 0: t.getPayedPrice()).sum();
    int payedPriceRemain = seatInfoEntity.getPrice() - payedPriceSum - donateApplyForm.getPayedPrice();
    if(payedPriceRemain < 0) {
      return MessageResponse.newInstance("捐赠金额已超过最大捐赠金额，请重新输入!剩余捐赠金额：" + (seatInfoEntity.getPrice() - payedPriceSum));
    }

    DonateSeatApplyEntity donateSeatApply = new DonateSeatApplyEntity();
    BeanCopierUtil.copy(donateApplyForm, donateSeatApply);
    donateSeatApply.setId(null);
    donateSeatApply.setDonateId(donateSeatInfo.getId());
    donateSeatApply.setOpenId("system");
    donateSeatApply.setPredictPrice(donateApplyForm.getPayedPrice().toString());
    donateSeatApply.setShareNum(donateApplyForm.getRealNum());
    donateSeatApply.setStatus(2);
    donateSeatApply.setApplyTime(LocalDateTime.now());
    donateSeatApply.setApplyPassTime(LocalDateTime.now());
    donateSeatApply.setUpdatedBy("system");
    donateSeatApplyMapper.insert(donateSeatApply);
    return new BaseResponse();
  }

  @Transactional
  public BaseResponse getSeatInfoByDonateNo(String donateNo) {

    // 通过捐赠编号取得对应捐赠信息
    DonateSeatInfoEntity donateSeatInfo = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
            .eq(DonateSeatInfoEntity::getDonateNo, donateNo).eq(DonateSeatInfoEntity::getDeleteFlag, false).one();
    if (donateSeatInfo == null) return MessageResponse.newInstance("捐赠信息不存在，请确认捐赠编号是否正确!");
    if(donateSeatInfo.getStatus() != 2 && donateSeatInfo.getStatus() != 3) {
      return MessageResponse.newInstance("该捐赠信息不在申请捐赠状态，无法新增捐赠申请信息!");
    }
    return seatRestService.get(donateSeatInfo.getSeatId().toString());
  }
}
