package cn.xzxx.seats.web.broadcast;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.web.broadcast.dto.BroadcastForm;
import cn.xzxx.seats.web.broadcast.dto.BroadcastSearchCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class BroadcastRestController {

  @Autowired
  private BroadcastRestService service;

  @GetMapping("/broadcast/list")
  public BaseResponse listInit(BroadcastSearchCondition condition) {

    return service.listInit(condition);
  }

  @GetMapping("/broadcast/init")
  public BaseResponse init(Integer id) {

    return service.init(id);
  }

  @PostMapping("/broadcast/create")
  public BaseResponse create(BroadcastForm form) {

    return service.edit(true, form);
  }

  @PostMapping("/broadcast/edit")
  public BaseResponse edit(BroadcastForm form) {

    return service.edit(false, form);
  }

  @GetMapping("/broadcast/delete")
  public BaseResponse delete(Integer id) {

    return service.delete(id);
  }
}
