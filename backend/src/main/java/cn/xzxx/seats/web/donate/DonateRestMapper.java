package cn.xzxx.seats.web.donate;

import cn.xzxx.seats.common.base.IBasePagingMapper;
import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.repository.entity.DonateCommentEntity;
import cn.xzxx.seats.web.donate.dto.DonateApplyRecord;
import cn.xzxx.seats.web.donate.dto.DonateCommentRecord;
import cn.xzxx.seats.web.donate.dto.DonateRecord;
import cn.xzxx.seats.web.donate.dto.DonateSearchCondition;
import cn.xzxx.seats.web.donate.entity.DonateApplyRecordEntity;
import cn.xzxx.seats.web.donate.entity.DonateCommentRecordEntity;
import cn.xzxx.seats.web.donate.entity.DonateRecordEntity;
import cn.xzxx.seats.web.seats.dto.SeatRecord;
import cn.xzxx.seats.web.seats.dto.SeatSearchCondition;
import cn.xzxx.seats.web.seats.entity.SeatRecordEntity;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.function.Function;

@Mapper
public interface DonateRestMapper extends IBasePagingMapper {

  default List<DonateRecord> selectDataList(DonateSearchCondition condition, ListResponse<DonateRecord> listResponse,
                                            Function<DonateRecordEntity, DonateRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectList(page, condition));
  }

  Page<DonateRecordEntity> selectList(Page<DonateRecordEntity> page, @Param(value = "param") DonateSearchCondition condition);

  default List<DonateApplyRecord> selectApplyDataList(DonateSearchCondition condition, ListResponse<DonateApplyRecord> listResponse,
                                                             Function<DonateApplyRecordEntity, DonateApplyRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectApplyList(page, condition));
  }

  Page<DonateApplyRecordEntity> selectApplyList(Page<DonateApplyRecordEntity> page, @Param(value = "param") DonateSearchCondition condition);

  default List<DonateCommentRecord> selectCommentDataList(DonateSearchCondition condition, ListResponse<DonateCommentRecord> listResponse,
                                                                       Function<DonateCommentRecordEntity, DonateCommentRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectCommentList(page, condition));
  }

  Page<DonateCommentRecordEntity> selectCommentList(Page<DonateCommentRecordEntity> page, @Param(value = "param") DonateSearchCondition condition);
}
