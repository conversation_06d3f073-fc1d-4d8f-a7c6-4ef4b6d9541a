package cn.xzxx.seats.web.vote;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.web.vote.dto.FeedbackCondition;
import cn.xzxx.seats.web.vote.dto.VoteModel;
import cn.xzxx.seats.web.vote.dto.VoteSearchCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
public class VoteRestController {

  @Autowired
  private VoteRestService service;

  @PostMapping("/vote/search")
  public BaseResponse search(VoteSearchCondition condition) {
    return service.search(condition);
  }

  @GetMapping("vote/get/{id}")
  public BaseResponse get(@PathVariable String id) {
    MessageResponse messages = MessageResponse.newInstance();
    DataResponse<VoteModel> response = new DataResponse<>();
    VoteModel model = service.get(id, messages);
    if (messages.hasError()) {
      return messages;
    }
    return response.setValue(model);
  }

  @PostMapping("/vote/save")
  public BaseResponse save(@RequestBody VoteModel model) {

    MessageResponse messages = MessageResponse.newInstance();
    BaseResponse response = service.save(model, messages);
    if (messages.hasError()) {
      return messages;
    }
    return response;
  }

  @PostMapping("/vote/response/{voteId}")
  public BaseResponse feedback(@PathVariable("voteId") String voteId, FeedbackCondition condition) {
    MessageResponse messages = MessageResponse.newInstance();
    condition.setVoteId(voteId);
    BaseResponse response = service.feedback(voteId, condition, messages);
    if (messages.hasError()) {
      return messages;
    }
    return response;
  }

  @DeleteMapping("/vote/delete/{id}")
  public BaseResponse delete(@PathVariable String id) {
    return service.delete(id);
  }
}
