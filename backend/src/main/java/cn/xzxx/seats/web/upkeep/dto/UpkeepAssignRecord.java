package cn.xzxx.seats.web.upkeep.dto;

import lombok.Data;

@Data
public class UpkeepAssignRecord {

    /**
     * 养护分配ID
     */
    private Integer id;

    /**
     * 捐赠No
     */
    private String donateNo;

    /**
     * 认养No
     */
    private String adoptNo;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

    /**
     * 座椅价格
     */
    private Integer seatPrice;

    /**
     * 省市县街道
     */
    private String pcdName;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * 养护状态
     */
    private Integer status;

    /**
     * 养护状态名称
     */
    private String statusText;

    /**
     * 创建时间
     */
    private String createdAt;

}
