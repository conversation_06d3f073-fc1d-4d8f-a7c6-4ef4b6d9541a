package cn.xzxx.seats.web.vote.dto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class FeedbackRecord {

  /**
   * 投票反馈ID
   */
  private Integer id;

  /**
   * 问卷ID
   */
  private Integer voteId;

  /**
   * 用户openId
   */
  private String openId;

  /**
   * 投票填写开始时间
   */
  private String startTime;

  /**
   * 投票填写结束时间
   */
  private String endTime;

  private double duration;

  /**
   * 是否有效 0-无效 1-有效
   */
  private String valueFlg;

  private String wxName;

  private Map<Integer, List<String>> feedbackDetailMap;
}
