package cn.xzxx.seats.web.info.dto.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class InformationRecord implements Serializable {

    /**
     * 资讯标题图片ID
     */
    private Integer id;

    /**
     * 封面图片URL
     */
    private String imageUrl;

    /**
     * 跳转链接
     */
    private String linkUrl;

    /**
     * 状态 0:不显示 1:显示
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createdAt;


}
