package cn.xzxx.seats.web.auth.model;

import cn.xzxx.seats.config.security.model.LoginUser;
import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public class UserModel {
  private String sessionId;
  private String userId;
  private String userName;
  private String userEmail;
  private List<String> roles = new ArrayList<>();
  private List<String> authorities = new ArrayList<>();

  public UserModel(String sessionId, LoginUser user) {
    this.sessionId = sessionId;
    this.userId = user.getUserId();
    this.userName = user.getUserName();
    this.userEmail = user.getUserEmail();
    this.roles.addAll(Arrays.asList(user.getRoleIds()));
    this.authorities.addAll(user.getAuthorities().stream().map(GrantedAuthority::getAuthority)
        .collect(Collectors.toList()));
  }
}
