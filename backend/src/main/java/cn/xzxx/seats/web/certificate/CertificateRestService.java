package cn.xzxx.seats.web.certificate;

import cn.xzxx.seats.common.base.UploadFiles;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.common.utils.DateUtils;
import cn.xzxx.seats.common.utils.ListUtils;
import cn.xzxx.seats.common.utils.StringUtil;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.*;
import cn.xzxx.seats.web.certificate.dto.CertificateForm;
import cn.xzxx.seats.web.certificate.dto.CertificateSearchCondition;
import cn.xzxx.seats.web.certificate.dto.entity.CertificateRecord;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class CertificateRestService {

  @Autowired
  private CertificateSettingsMapper certificateSettingsMapper;

  @Autowired
  private FileUploadComponent fileUploadComponent;

  @Autowired
  private DonateSeatApplyMapper donateSeatApplyMapper;

  @Autowired
  private AdoptSeatApplyMapper adoptSeatApplyMapper;

  @Autowired
  private DonateSeatInfoMapper donateSeatInfoMapper;

  @Autowired
  private AdoptSeatInfoMapper adoptSeatInfoMapper;

  @Autowired
  private SeatInfoMapper seatInfoMapper;

  @Autowired
  private SeatInfoAdoptMapper seatInfoAdoptMapper;

  public BaseResponse listInit(CertificateSearchCondition condition) {

    ListResponse<CertificateRecord> listResponse = new ListResponse<>();
    List<CertificateSettingsEntity> entities = certificateSettingsMapper.selectList(new LambdaQueryWrapper<CertificateSettingsEntity>()
                    .eq(condition.getSeatType() != null, CertificateSettingsEntity::getSeatType, condition.getSeatType())
                    .eq(condition.getApplyId() != null, CertificateSettingsEntity::getApplyId, condition.getApplyId())
                    .eq(condition.getStatus() != null, CertificateSettingsEntity::getStatus, condition.getStatus()));
    List<CertificateRecord> records = new ArrayList<>();
    entities.forEach(x->{
      CertificateRecord record = new CertificateRecord();
      record.setId(x.getId());
      record.setOpenId(x.getOpenId());
      record.setImageUrl(x.getImageUrl());
      record.setStatus(x.getStatus());
      record.setCreatedAt(DateUtils.localDateTimeFormat(x.getCreatedAt()));
      records.add(record);
    });
    listResponse.setValue(records);
    return listResponse;
  }

  public BaseResponse init(Integer id) {

    CertificateSettingsEntity entity = certificateSettingsMapper.selectById(id);
    if(entity == null) {
      return MessageResponse.newInstance("证书信息不存在，请返回列表刷新后再操作。");
    }
    CertificateForm form = new CertificateForm();
    form.setId(entity.getId());
    form.setImageUrl(entity.getImageUrl());
    if (!StringUtil.isEmpty(entity.getImageUrl())) {
      UploadFiles personFiles = new UploadFiles();
      personFiles.addFile(entity.getImageUrl(), "", fileUploadComponent.getImageUrl(entity.getImageUrl()));
      form.setImage(personFiles);
    }
    DataResponse<CertificateForm> result = new DataResponse<>();
    return result.setValue(form);

  }

  @Transactional
  public BaseResponse edit(boolean add, CertificateForm form) {
    CertificateSettingsEntity entity;
    UploadFiles image = form.getImage();
    if(image == null) {
      return MessageResponse.newInstance("保存失败，请添加证书图片后再进行保存！");
    }
    List<UploadFiles.RawFile> addedFiles = image.addedFiles();
    List<UploadFiles.RawFile> deleteFiles = image.deletedFiles();

    if (add) {
      entity = new CertificateSettingsEntity();

      if(form.getSeatType() == 1) {
        // 共建
        DonateSeatApplyEntity donateSeatApplyEntity = donateSeatApplyMapper.selectById(form.getApplyId());
        if(donateSeatApplyEntity == null) {
          return MessageResponse.newInstance("共建申请信息不存在，请返回列表刷新后再操作。");
        }
        DonateSeatInfoEntity donateSeatInfoEntity = donateSeatInfoMapper.selectById(donateSeatApplyEntity.getDonateId());
        if(donateSeatInfoEntity == null) {
          return MessageResponse.newInstance("共建信息不存在，请返回列表刷新后再操作。");
        }
        SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(donateSeatInfoEntity.getSeatId());
        if(seatInfoEntity == null) {
          return MessageResponse.newInstance("共建座椅信息不存在，请返回列表刷新后再操作。");
        }
        entity.setSeatNo(seatInfoEntity.getSeatNo());
        entity.setSeatName(seatInfoEntity.getName());
      } else {
        AdoptSeatApplyEntity adoptSeatApplyEntity = adoptSeatApplyMapper.selectById(form.getApplyId());
        if(adoptSeatApplyEntity == null) {
          return MessageResponse.newInstance("认养申请信息不存在，请返回列表刷新后再操作。");
        }
        AdoptSeatInfoEntity adoptSeatInfoEntity = adoptSeatInfoMapper.selectById(adoptSeatApplyEntity.getAdoptId());
        if(adoptSeatInfoEntity == null) {
          return MessageResponse.newInstance("认养信息不存在，请返回列表刷新后再操作。");
        }
        SeatInfoAdoptEntity seatInfoAdoptEntity = seatInfoAdoptMapper.selectById(adoptSeatInfoEntity.getSeatId());
        if(seatInfoAdoptEntity == null) {
          return MessageResponse.newInstance("认养座椅信息不存在，请返回列表刷新后再操作。");
        }
        entity.setSeatNo(seatInfoAdoptEntity.getSeatNo());
        entity.setSeatName(seatInfoAdoptEntity.getName());
      }

      LocalDateTime localDateTime = LocalDateTime.now();
      String format = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(localDateTime);
      entity.setCertNo(format + form.getApplyId());
      entity.setOpenId(form.getOpenId());
      entity.setSeatType(form.getSeatType());
      entity.setApplyId(form.getApplyId());
      if (ListUtils.isNotEmpty(addedFiles)) {
        UploadFiles.RawFile rawFile = addedFiles.get(0);
        String fileUrl = fileUploadComponent.add(rawFile);
        entity.setImageUrl(fileUrl);
      }
      entity.setStatus(form.getStatus());
      entity.setCreatedAt(LocalDateTime.now());
      certificateSettingsMapper.insert(entity);
    } else {
      entity = certificateSettingsMapper.selectById(form.getId());
      if (entity == null) {
        return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
      }
      if (ListUtils.isNotEmpty(addedFiles)) {
        UploadFiles.RawFile rawFile = addedFiles.get(0);
        String fileUrl = fileUploadComponent.add(rawFile);
        entity.setImageUrl(fileUrl);
      }
      entity.setStatus(form.getStatus());
      certificateSettingsMapper.updateById(entity);
    }
    // 座椅图片处理
    if (ListUtils.isNotEmpty(deleteFiles)) {
      fileUploadComponent.delete(deleteFiles.get(0));
    }
    return new BaseResponse();
  }

  @Transactional
  public BaseResponse delete(Integer id) {

    CertificateSettingsEntity entity = certificateSettingsMapper.selectById(id);
    if (entity == null) {
      return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
    }
    fileUploadComponent.delete(entity.getImageUrl());

    certificateSettingsMapper.deleteById(id);
    return new BaseResponse();
  }
}
