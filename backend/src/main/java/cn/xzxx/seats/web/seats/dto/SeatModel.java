package cn.xzxx.seats.web.seats.dto;

import cn.xzxx.seats.common.base.UploadFiles;
import lombok.Data;

@Data
public class SeatModel {

  /**
   * 座椅ID
   */
  private Integer id;

  /**
   * 座椅编号
   */
  private String seatNo;

  /**
   * 座椅名称
   */
  private String seatName;

  /**
   * 座椅大小
   */
  private String seatSize;

  /**
   * 座椅材质
   */
  private String material;

  /**
   * 座椅介绍
   */
  private String introduce;

  /**
   * 座椅状态 0:未发布 1:发布 2:下架
   */
  private Integer status;

  private String statusText;

  /**
   * 座椅价格 单位(分)
   */
  private Integer price;

  /**
   * 座椅主图URL
   */
  private UploadFiles image;

  private UploadFiles imageList;

}
