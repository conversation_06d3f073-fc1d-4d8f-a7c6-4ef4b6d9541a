package cn.xzxx.seats.web.orcode;

import cn.xzxx.seats.web.orcode.dto.ReadExcelUpkeepUser;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ReadDataListener extends AnalysisEventListener<ReadExcelUpkeepUser> {

  private QrCodeService qrCodeService;

  public static final Logger logger = LoggerFactory.getLogger(ReadDataListener.class);

  public ReadDataListener(QrCodeService qrCodeService) {
      this.qrCodeService = qrCodeService;
  }

  @Override
  public void invoke(ReadExcelUpkeepUser qrCode, AnalysisContext analysisContext) {

      // 导入DB
      qrCodeService.insUpkeepUser(qrCode);

  }

  @Override
  public void doAfterAllAnalysed(AnalysisContext analysisContext) {
  }
}
