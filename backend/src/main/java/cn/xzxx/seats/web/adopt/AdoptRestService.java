package cn.xzxx.seats.web.adopt;

import cn.xzxx.seats.code.AdoptApplyStatus;
import cn.xzxx.seats.code.AdoptStatus;
import cn.xzxx.seats.common.base.OprType;
import cn.xzxx.seats.common.base.UploadFiles;
import cn.xzxx.seats.common.exception.BusinessException;
import cn.xzxx.seats.common.response.*;
import cn.xzxx.seats.common.utils.*;
import cn.xzxx.seats.common.utils.GpsUtils.GPS;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.component.PulldownComponent;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.*;
import cn.xzxx.seats.web.adopt.dto.*;
import cn.xzxx.seats.web.common.CommonService;
import cn.xzxx.seats.web.donate.dto.DonateApplyForm;
import cn.xzxx.seats.web.donate.dto.DonateForm;
import cn.xzxx.seats.web.donate.dto.DonateParam;
import cn.xzxx.seats.web.upkeep.UpkeepAssignService;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class AdoptRestService {

  @Autowired
  private AdoptRestMapper adoptRestMapper;

  @Autowired
  private CommonService commonService;

  @Autowired
  private AdoptSeatInfoMapper adoptSeatInfoMapper;

  @Autowired
  private AdoptSeatApplyMapper adoptSeatApplyMapper;

  @Autowired
  private AdoptUpkeepMapper adoptUpkeepMapper;

  @Autowired
  private DonateSeatInfoMapper donateSeatInfoMapper;

  @Autowired
  private SeatInfoAdoptMapper seatInfoMapper;

  @Autowired
  private SeatImageAdoptMapper seatImageMapper;

  @Autowired
  private FileUploadComponent fileUploadComponent;

  @Autowired
  private PulldownComponent pulldownComponent;

  @Autowired
  private UpkeepAssignService upkeepAssignService;

  public BaseResponse listInit(AdoptSearchCondition condition) {

    ListResponse<AdoptRecord> listResponse = new ListResponse<>();

    condition.setDefaultSort(OrderItem.desc("ai.created_at"));
    List<AdoptRecord> dataList = getAdoptRecords(condition, listResponse);
    listResponse.setValue(dataList);
    return listResponse;
  }

  public BaseResponse init(DonateParam param) {

    DataResponse<AdoptForm> result = new DataResponse<>();
    OprType optType = param.getOptType();
    if (optType.isAdd()) {
      return result;
    }
    String id = param.getId();
    AdoptSeatInfoEntity adoptSeatInfoEntity = adoptSeatInfoMapper.selectById(id);
    if(adoptSeatInfoEntity == null) {
      return MessageResponse.newInstance("认养信息不存在，请返回列表刷新后再操作。");
    }

    SeatInfoAdoptEntity seatInfoEntity = seatInfoMapper.selectById(adoptSeatInfoEntity.getSeatId());
    AdoptForm adoptForm = new AdoptForm();
    BeanUtils.copyProperties(adoptSeatInfoEntity, adoptForm);
    adoptForm.setDonateNo(adoptSeatInfoEntity.getDonateNo());
    adoptForm.setSeatNo(seatInfoEntity.getSeatNo());
    adoptForm.setSeatName(seatInfoEntity.getName());
    adoptForm.setSeatMaterial(seatInfoEntity.getMaterial());
    adoptForm.setSeatSize(seatInfoEntity.getSize());
    adoptForm.setSeatPrice(seatInfoEntity.getPrice());
    adoptForm.setOwnerUnit(seatInfoEntity.getOwnerUnit());
    adoptForm.setConstructUnit(seatInfoEntity.getConstructUnit());

    if (!StringUtil.isEmpty(seatInfoEntity.getImage())) {
      UploadFiles uploadFiles = new UploadFiles();
      uploadFiles.addFile(seatInfoEntity.getImage(), "", fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
      adoptForm.setSeatImage(uploadFiles);
    }
    List<SeatImageAdoptEntity> seatImageEntities = seatImageMapper.selectList(new LambdaQueryWrapper<SeatImageAdoptEntity>()
            .eq(SeatImageAdoptEntity::getSeatId, seatInfoEntity.getId()));
    if(!CollectionUtils.isEmpty(seatImageEntities)) {
      UploadFiles imageFiles = new UploadFiles();
      seatImageEntities.forEach(image->{
        imageFiles.addFile(image.getImage(), "", fileUploadComponent.getImageUrl(image.getImage()));
      });
      adoptForm.setSeatImageList(imageFiles);
    }
    adoptForm.setPcdName(commonService.getPcdName(adoptSeatInfoEntity.getProvince(), adoptSeatInfoEntity.getCity(), adoptSeatInfoEntity.getStrict(), adoptSeatInfoEntity.getStreet()));
    adoptForm.setSeatIntroduce(seatInfoEntity.getIntroduce());
    adoptForm.setStatusText(AdoptStatus.statusName(adoptSeatInfoEntity.getStatus()));
    adoptForm.setGpsLng(adoptSeatInfoEntity.getGpsLngBmap());
    adoptForm.setGpsLat(adoptSeatInfoEntity.getGpsLatBmap());

    return result.setValue(adoptForm);

  }

  @Transactional
  public BaseResponse edit(boolean add, AdoptForm form) {
    AdoptSeatInfoEntity entity;
    SeatInfoAdoptEntity seatEntity;

    UploadFiles image = form.getSeatImage();
    UploadFiles imageList = form.getSeatImageList();
    if(image == null) {
      return MessageResponse.newInstance("保存失败，请设置座椅主图后再进行保存！");
    }
    List<UploadFiles.RawFile> addedFiles = image.addedFiles();
    List<UploadFiles.RawFile> deleteFiles = image.deletedFiles();


    if(!StringUtils.isEmpty(form.getDonateNo())) {
      // 输入捐赠No时判断是否有效
      DonateSeatInfoEntity donateSeatInfo = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
              .eq(DonateSeatInfoEntity::getDonateNo, form.getDonateNo()).one();
      if(donateSeatInfo == null) {
        return MessageResponse.newInstance("捐赠No所关联捐赠信息不存在，请确认无误后再操作。");
      }

      // 输入捐赠No时判断是否已经被关联
      List<AdoptSeatInfoEntity> adoptSeatInfos = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
              .eq(AdoptSeatInfoEntity::getDonateNo, form.getDonateNo()).list();
      if(!CollectionUtils.isEmpty(adoptSeatInfos)) {
        return MessageResponse.newInstance("捐赠No已被其他认养座椅关联，请关联其他捐赠座椅信息。");
      }
    }

    if (add) {
      seatEntity = new SeatInfoAdoptEntity();
      setSeatInfo(form, seatEntity);
      if (ListUtils.isNotEmpty(addedFiles)) {
        UploadFiles.RawFile rawFile = addedFiles.get(0);
        String fileUrl = fileUploadComponent.add(rawFile);
        seatEntity.setImage(fileUrl);
      }
      seatInfoMapper.insert(seatEntity);

      entity = new AdoptSeatInfoEntity();
      entity.setSeatId(seatEntity.getId());
      form.setStatus(AdoptStatus.PUBLISH_WAIT.status());
      setInfo(form, entity);
      entity.setCreatedAt(LocalDateTime.now());
      adoptSeatInfoMapper.insert(entity);

      // 新增养护 新增 养护管理表
      upkeepAssignService.insUpkeepAssign(entity.getSeatId(), 2);

    } else {

      entity = adoptSeatInfoMapper.selectById(form.getId());
      if (entity == null) {
        return MessageResponse.newInstance("认养信息不存在，请返回列表刷新后再操作。");
      }

      seatEntity = seatInfoMapper.selectById(entity.getSeatId());
      if (seatEntity == null) {
        return MessageResponse.newInstance("认养座椅信息不存在，请返回列表刷新后再操作。");
      }
      setSeatInfo(form, seatEntity);
      if (ListUtils.isNotEmpty(addedFiles)) {
        UploadFiles.RawFile rawFile = addedFiles.get(0);
        String fileUrl = fileUploadComponent.add(rawFile);
        seatEntity.setImage(fileUrl);
      }
      seatInfoMapper.updateById(seatEntity);

      setInfo(form, entity);
      adoptSeatInfoMapper.updateById(entity);
    }

    // 座椅图片处理
    if (ListUtils.isNotEmpty(deleteFiles)) {
      fileUploadComponent.delete(deleteFiles.get(0));
    }
    if (imageList != null) {
      List<UploadFiles.RawFile> addedImageList = imageList.addedFiles();
      List<UploadFiles.RawFile> deletedImageList = imageList.deletedFiles();

      if (ListUtils.isNotEmpty(addedImageList)) {
        for (UploadFiles.RawFile rawFile : addedImageList) {
          String fileUrl = fileUploadComponent.add(rawFile);
          SeatImageAdoptEntity imageEntity = new SeatImageAdoptEntity();
          imageEntity.setSeatId(seatEntity.getId());
          imageEntity.setImage(fileUrl);
          imageEntity.setRemarks("");
          seatImageMapper.insert(imageEntity);
        }
      }

      if (ListUtils.isNotEmpty(deletedImageList)) {
        for (UploadFiles.RawFile rawFile : deletedImageList) {
          String fileId = rawFile.getFileId();
          fileUploadComponent.delete(rawFile);
          seatImageMapper.delete(new QueryWrapper<SeatImageAdoptEntity>().eq("image", fileId));
        }
      }
    }
    if(add) {
      return MessageResponse.newInstance().addSuccessMessage("认养信息新增成功！");
    } else {
      return MessageResponse.newInstance().addSuccessMessage("认养信息更新成功！");
    }
  }

  private void setInfo(AdoptForm form, AdoptSeatInfoEntity entity) {

    entity.setAdoptNo(form.getAdoptNo());
    entity.setDonateNo(form.getDonateNo());
    entity.setAdoptPrice(form.getAdoptPrice());
    entity.setAdoptTerm(form.getAdoptTerm());
    entity.setProvince(form.getProvince());
    entity.setCity(form.getCity());
    entity.setStrict(form.getStrict());
    entity.setStreet(form.getStreet());
    entity.setAdress(form.getAdress());
//    GPS gps = GpsUtils.BdMapToTxMap(form.getGpsLat().toString(), form.getGpsLng().toString());
    entity.setGpsLng(form.getGpsLng());
    entity.setGpsLat(form.getGpsLat());
    entity.setGpsLngBmap(form.getGpsLng());
    entity.setGpsLatBmap(form.getGpsLat());
    entity.setStatus(form.getStatus());
  }

  public BaseResponse applyListInit(AdoptSearchCondition condition) {

    ListResponse<AdoptApplyRecord> listResponse = new ListResponse<>();
    condition.setDefaultSort(OrderItem.desc("aa.apply_time"));
    List<AdoptApplyRecord> dataList = getAdoptApplyRecords(condition, listResponse);
    listResponse.setValue(dataList);

    if (condition.isInit()) {
      List<PulldownItem> statusOptions = pulldownComponent.getAdoptApplyStatusPulldown();
      listResponse.putData("statusOptions", statusOptions);
    }

    return listResponse;
  }

  /**
   * 认养信息取得
   * @param condition
   * @param listResponse
   * @return
   */
  private List<AdoptApplyRecord> getAdoptApplyRecords(AdoptSearchCondition condition, ListResponse<AdoptApplyRecord> listResponse) {
    List<AdoptApplyRecord> dataList = adoptRestMapper.selectApplyDataList(condition, listResponse, e -> {
      AdoptApplyRecord record = new AdoptApplyRecord();
      BeanUtils.copyProperties(e, record);
      record.setStatusText(AdoptApplyStatus.statusName(e.getStatus()));
      record.setPcdName(commonService.getPcdName(e.getProvince(), e.getCity(), e.getStrict(), e.getStreet()));
      record.setApplyTime(DateUtils.localDateTimeFormat(e.getApplyTime()));
      record.setApplyPassTime(DateUtils.localDateTimeFormat(e.getApplyPassTime()));
      record.setValidTime(DateUtils.localDateTimeFormat(e.getValidTime()));
      return record;
    });
    return dataList;
  }

  public BaseResponse initApply(String id) {

    AdoptSeatApplyEntity adoptSeatApplyEntity = adoptSeatApplyMapper.selectById(id);
    if(adoptSeatApplyEntity == null) {
      return MessageResponse.newInstance("认养申请信息不存在，请返回列表刷新后再操作。");
    }
    AdoptSeatInfoEntity adoptSeatInfoEntity = adoptSeatInfoMapper.selectById(adoptSeatApplyEntity.getAdoptId());
    if(adoptSeatInfoEntity == null) {
      return MessageResponse.newInstance("认养信息不存在，请返回列表刷新后再操作。");
    }
    SeatInfoAdoptEntity seatInfoEntity = seatInfoMapper.selectById(adoptSeatInfoEntity.getSeatId());
    if(seatInfoEntity == null) {
      return MessageResponse.newInstance("座椅信息不存在，请返回列表刷新后再操作。");
    }
    AdoptApplyForm adoptApplyForm = new AdoptApplyForm();
    BeanUtils.copyProperties(adoptSeatApplyEntity, adoptApplyForm);
    adoptApplyForm.setDonateNo(adoptSeatInfoEntity.getDonateNo());
    adoptApplyForm.setSeatNo(seatInfoEntity.getSeatNo());
    adoptApplyForm.setSeatName(seatInfoEntity.getName());
    adoptApplyForm.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
    adoptApplyForm.setSeatPrice(seatInfoEntity.getPrice());
    adoptApplyForm.setSeatProvince(adoptSeatInfoEntity.getProvince());
    adoptApplyForm.setSeatCity(adoptSeatInfoEntity.getCity());
    adoptApplyForm.setSeatStrict(adoptSeatInfoEntity.getStrict());
    adoptApplyForm.setSeatStreet(adoptSeatInfoEntity.getStreet());
    adoptApplyForm.setSeatAdress(adoptSeatInfoEntity.getAdress());
    adoptApplyForm.setSeatGpsLat(adoptSeatInfoEntity.getGpsLatBmap());
    adoptApplyForm.setSeatGpsLng(adoptSeatInfoEntity.getGpsLngBmap());
    DataResponse<AdoptApplyForm> result = new DataResponse<>();
    return result.setValue(adoptApplyForm);

  }

  public BaseResponse editApply(boolean add, AdoptApplyForm form) {

    AdoptSeatApplyEntity adoptSeatApply;

    if (add) {
      adoptSeatApply = new AdoptSeatApplyEntity();
      // 通过捐赠编号取得对应捐赠信息
      AdoptSeatInfoEntity adoptSeatInfo = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
              .eq(AdoptSeatInfoEntity::getAdoptNo, form.getAdoptNo()).eq(AdoptSeatInfoEntity::getDeleteFlag, false).one();
      if (adoptSeatInfo == null) return MessageResponse.newInstance("认养信息不存在，请确认捐赠编号是否正确!");
      if (adoptSeatInfo.getStatus() == 3) return MessageResponse.newInstance("该座椅已被认养，无法新增认养申请信息!");

      BeanCopierUtil.copy(form, adoptSeatApply);
      adoptSeatApply.setId(null);
      adoptSeatApply.setAdoptId(adoptSeatInfo.getId());
      adoptSeatApply.setOpenId("system");
      adoptSeatApply.setStatus(2);
      adoptSeatApply.setApplyTime(LocalDateTime.now());
      adoptSeatApply.setApplyPassTime(LocalDateTime.now());
      adoptSeatApply.setUpdatedBy("system");

      adoptSeatApplyMapper.insert(adoptSeatApply);

      // 审核通过时更新 认养座椅状态设置为 被认养
      adoptSeatInfo.setStatus(3);
      adoptSeatInfoMapper.updateById(adoptSeatInfo);

    } else {

      adoptSeatApply = adoptSeatApplyMapper.selectById(form.getId());
      if (adoptSeatApply == null) {
        return MessageResponse.newInstance("认养信息不存在，请返回列表刷新后再操作。");
      }
      BeanCopierUtil.copy(form, adoptSeatApply);
      adoptSeatApplyMapper.updateById(adoptSeatApply);
    }

    if(add) {
      return MessageResponse.newInstance().addSuccessMessage("捐赠信息新增成功！");
    } else {
      return MessageResponse.newInstance().addSuccessMessage("捐赠信息更新成功！");
    }
  }

  public BaseResponse upkeepListInit(AdoptSearchCondition condition) {

    ListResponse<AdoptUpkeepRecord> listResponse = new ListResponse<>();
    condition.setDefaultSort(OrderItem.desc("au.upkeep_time"));
    List<AdoptUpkeepRecord> dataList = getAdoptUpkeepRecords(condition, listResponse);
    listResponse.setValue(dataList);
    return listResponse;
  }

  /**
   * 维护信息取得
   * @param condition
   * @param listResponse
   * @return
   */
  private List<AdoptUpkeepRecord> getAdoptUpkeepRecords(AdoptSearchCondition condition, ListResponse<AdoptUpkeepRecord> listResponse) {
    List<AdoptUpkeepRecord> dataList = adoptRestMapper.selectUpkeepDataList(condition, listResponse, e -> {
      AdoptUpkeepRecord record = new AdoptUpkeepRecord();
      BeanUtils.copyProperties(e, record);
      record.setStatusText(AdoptApplyStatus.statusName(e.getStatus()));
      record.setSeatPcdName(commonService.getPcdName(e.getSeatProvince(), e.getSeatCity(), e.getSeatStrict(), e.getSeatStreet()));
      record.setPcdName(commonService.getPcdName(e.getProvince(), e.getCity(), e.getStrict(), e.getStreet()));
//      GPS gps = GpsUtils.map_tx2bd(e.getGpsLat().toString(), e.getGpsLng().toString());
      record.setGpsLng(e.getGpsLng());
      record.setGpsLat(e.getGpsLat());
      return record;
    });
    return dataList;
  }

  public BaseResponse initUpkeep(String id) {

    AdoptUpkeepEntity adoptUpkeepEntity = adoptUpkeepMapper.selectById(id);
    if(adoptUpkeepEntity == null) {
      return MessageResponse.newInstance("维护申请信息不存在，请返回列表刷新后再操作。");
    }
    List<AdoptSeatApplyEntity> adoptSeatApplyEntitys = adoptSeatApplyMapper.selectList(new LambdaQueryWrapper<AdoptSeatApplyEntity>()
            .eq(AdoptSeatApplyEntity::getAdoptId, adoptUpkeepEntity.getAdoptId()));
    if(CollectionUtils.isEmpty(adoptSeatApplyEntitys)) {
      return MessageResponse.newInstance("认养申请信息不存在，请返回列表刷新后再操作。");
    }
    AdoptSeatApplyEntity adoptSeatApplyEntity = adoptSeatApplyEntitys.get(0);
    AdoptSeatInfoEntity adoptSeatInfoEntity = adoptSeatInfoMapper.selectById(adoptSeatApplyEntity.getAdoptId());
    if(adoptSeatInfoEntity == null) {
      return MessageResponse.newInstance("认养信息不存在，请返回列表刷新后再操作。");
    }
    SeatInfoAdoptEntity seatInfoEntity = seatInfoMapper.selectById(adoptSeatInfoEntity.getSeatId());
    if(seatInfoEntity == null) {
      return MessageResponse.newInstance("座椅信息不存在，请返回列表刷新后再操作。");
    }
    AdoptUpkeepForm adoptUpkeepForm = new AdoptUpkeepForm();
    BeanUtils.copyProperties(adoptUpkeepEntity, adoptUpkeepForm);
    adoptUpkeepForm.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
    adoptUpkeepForm.setUpkeepImageBefore(fileUploadComponent.getImageUrl(adoptUpkeepEntity.getUpkeepImageBefore()));
    adoptUpkeepForm.setUpkeepImageAfter(fileUploadComponent.getImageUrl(adoptUpkeepEntity.getUpkeepImageAfter()));
    adoptUpkeepForm.setUpkeepName(adoptSeatApplyEntity.getName());
    adoptUpkeepForm.setUpkeepCompany(adoptSeatApplyEntity.getIdCardNo());
    adoptUpkeepForm.setDonateNo(adoptSeatInfoEntity.getDonateNo());
    adoptUpkeepForm.setSeatNo(seatInfoEntity.getSeatNo());
    adoptUpkeepForm.setSeatName(seatInfoEntity.getName());
    adoptUpkeepForm.setSeatProvince(adoptSeatInfoEntity.getProvince());
    adoptUpkeepForm.setSeatCity(adoptSeatInfoEntity.getCity());
    adoptUpkeepForm.setSeatStrict(adoptSeatInfoEntity.getStrict());
    adoptUpkeepForm.setSeatStreet(adoptSeatInfoEntity.getStreet());
    adoptUpkeepForm.setSeatAdress(adoptSeatInfoEntity.getAdress());
    adoptUpkeepForm.setSeatGpsLat(adoptSeatInfoEntity.getGpsLatBmap());
    adoptUpkeepForm.setSeatGpsLng(adoptSeatInfoEntity.getGpsLngBmap());
    adoptUpkeepForm.setValidTime(adoptSeatApplyEntity.getValidTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    adoptUpkeepForm.setUpkeepTime(adoptUpkeepEntity.getUpkeepTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    DataResponse<AdoptUpkeepForm> result = new DataResponse<>();
    return result.setValue(adoptUpkeepForm);

  }

  @Transactional
  public BaseResponse updStatus(DonateForm form) {
    AdoptSeatInfoEntity entity = adoptSeatInfoMapper.selectById(form.getId());
    if (entity == null) {
      return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
    }
    entity.setStatus(form.getStatus());
    adoptSeatInfoMapper.updateById(entity);
    return new BaseResponse();
  }

  @Transactional
  public BaseResponse updStatusApply(DonateApplyForm form) {

    AdoptSeatApplyEntity entity = adoptSeatApplyMapper.selectById(form.getId());
    if (entity == null) {
      return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
    }
    entity.setStatus(form.getStatus());
    if (AdoptApplyStatus.APPROVED.status() == form.getStatus()) {
      entity.setApplyPassTime(LocalDateTime.now());
      entity.setValidTime(LocalDateTime.now().plusYears(1));
    }
    adoptSeatApplyMapper.updateById(entity);

    // 审核通过则认养信息作为已被认养
    if(AdoptApplyStatus.APPROVED.status() == form.getStatus()) {

      AdoptSeatInfoEntity adoptSeatInfoEntity = adoptSeatInfoMapper.selectById(entity.getAdoptId());
      if (adoptSeatInfoEntity == null) {
        return MessageResponse.newInstance("该信息不存在，请返回列表刷新后再操作。");
      }
      if (adoptSeatInfoEntity.getStatus() == AdoptStatus.PUBLISH_WAIT.status()) {
        return MessageResponse.newInstance("该座椅还未发布，不能进行认养。");
      }
      if (adoptSeatInfoEntity.getStatus() == AdoptStatus.ADOPT_FINISH.status()) {
        return MessageResponse.newInstance("该座椅已被认养，不能重复认养。");
      }
      adoptSeatInfoEntity.setStatus(AdoptStatus.ADOPT_FINISH.status());
      adoptSeatInfoMapper.updateById(adoptSeatInfoEntity);
    }
    return new BaseResponse();
  }

  /**
   * Excel导出（认养信息）
   * @param os
   * @param condition
   * @return
   */
  public void exportAdopt(ByteArrayOutputStream os, AdoptSearchCondition condition) {

    condition.setPageNumber(0);
    condition.setPageSize(-1);
    ListResponse<AdoptRecord> listResponse = new ListResponse<>();
    List<AdoptRecord> dataList = getAdoptRecords(condition, listResponse);

    List<AdoptExcel> excelDataList = new ArrayList<>();
    dataList.forEach(data->{
      AdoptExcel excelData = new AdoptExcel();
      BeanUtils.copyProperties(data, excelData);
      excelDataList.add(excelData);
    });
    EasyExcel.write(os, AdoptExcel.class).sheet(0).doWrite(excelDataList);
  }

  /**
   * Excel导出（认养申请）
   * @param os
   * @param condition
   * @return
   */
  public void exportAdoptApply(ByteArrayOutputStream os, AdoptSearchCondition condition) {

    condition.setPageNumber(0);
    condition.setPageSize(-1);
    ListResponse<AdoptApplyRecord> listResponse = new ListResponse<>();
    List<AdoptApplyRecord> dataList = getAdoptApplyRecords(condition, listResponse);

    List<AdoptApplyExcel> excelDataList = new ArrayList<>();
    dataList.forEach(data->{
      AdoptApplyExcel excelData = new AdoptApplyExcel();
      BeanUtils.copyProperties(data, excelData);
      excelDataList.add(excelData);
    });
    EasyExcel.write(os, AdoptApplyExcel.class).sheet(0).doWrite(excelDataList);
  }

  /**
   * Excel导出（维护信息）
   * @param os
   * @param condition
   * @return
   */
  public void exportAdoptUpkeepApply(ByteArrayOutputStream os, AdoptSearchCondition condition) {

    condition.setPageNumber(0);
    condition.setPageSize(-1);
    ListResponse<AdoptUpkeepRecord> listResponse = new ListResponse<>();
    List<AdoptUpkeepRecord> dataList = getAdoptUpkeepRecords(condition, listResponse);

    List<UpkeepApplyExcel> excelDataList = new ArrayList<>();
    dataList.forEach(data->{
      UpkeepApplyExcel excelData = new UpkeepApplyExcel();
      BeanUtils.copyProperties(data, excelData);
      excelDataList.add(excelData);
    });
    EasyExcel.write(os, UpkeepApplyExcel.class).sheet(0).doWrite(excelDataList);
  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse delete(String id) {

    List<AdoptSeatApplyEntity> adoptSeatApplyEntities = adoptSeatApplyMapper.selectList(
            new LambdaQueryWrapper<AdoptSeatApplyEntity>().eq(AdoptSeatApplyEntity::getAdoptId, id));

    if(!CollectionUtils.isEmpty(adoptSeatApplyEntities)){
      throw new BusinessException("该认养信息已存在申请记录，请删除后再操作。");
    }

    // 座椅信息删除
    AdoptSeatInfoEntity adoptSeatInfoEntity = adoptSeatInfoMapper.selectById(id);
    SeatInfoAdoptEntity seatInfoEntity = seatInfoMapper.selectById(adoptSeatInfoEntity.getSeatId());
    fileUploadComponent.delete(seatInfoEntity.getImage());
    seatInfoMapper.deleteById(seatInfoEntity.getId());

    // 座椅图片删除
    List<SeatImageAdoptEntity> imageEntityList = seatImageMapper.selectList(new LambdaQueryWrapper<SeatImageAdoptEntity>()
            .eq(SeatImageAdoptEntity::getSeatId, seatInfoEntity.getId()));

    imageEntityList.forEach(image->{
      fileUploadComponent.delete(image.getImage());
      seatImageMapper.deleteById(image.getId());
    });

    // 认养删除
    adoptSeatInfoMapper.deleteById(id);

    return MessageResponse.newInstance().addSuccessMessage("认养删除成功！");
  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse deleteApply(String id) {

    List<AdoptUpkeepEntity> adoptUpkeepEntities = adoptUpkeepMapper.selectList(
            new LambdaQueryWrapper<AdoptUpkeepEntity>().eq(AdoptUpkeepEntity::getAdoptId, id));

    if(!CollectionUtils.isEmpty(adoptUpkeepEntities)){
      throw new BusinessException("该认养信息已存在维护申请记录，请删除后再操作。");
    }

    // 认养申请删除
    adoptSeatApplyMapper.deleteById(id);

    return MessageResponse.newInstance().addSuccessMessage("认养申请删除成功！");
  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse deleteUpkeep(String id) {

    // 维护申请删除
    adoptUpkeepMapper.deleteById(id);

    return MessageResponse.newInstance().addSuccessMessage("维护记录删除成功！");
  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse cancel(String id) {

    AdoptSeatInfoEntity adoptSeatInfoEntity = adoptSeatInfoMapper.selectById(id);
    if(adoptSeatInfoEntity == null || adoptSeatInfoEntity.getStatus() != 2) {
      throw new BusinessException("该认养信息不在发布状态，无法取消发布，请确认。");
    }
    // 认养取消发布
    adoptSeatInfoEntity.setStatus(1);
    adoptSeatInfoMapper.updateById(adoptSeatInfoEntity);

    return MessageResponse.newInstance().addSuccessMessage("取消发布成功！");
  }

  private List<AdoptRecord> getAdoptRecords(AdoptSearchCondition condition, ListResponse<AdoptRecord> listResponse) {
    List<AdoptRecord> dataList = adoptRestMapper.selectDataList(condition, listResponse, e -> {
      AdoptRecord record = new AdoptRecord();
      BeanUtils.copyProperties(e, record);
      record.setStatusText(AdoptStatus.statusName(e.getStatus()));
      record.setPcdName(commonService.getPcdName(e.getProvince(), e.getCity(), e.getStrict(), e.getStreet()));
      record.setCreatedAt(DateUtils.localDateTimeFormat(e.getCreatedAt()));
      return record;
    });
    return dataList;
  }

  private void setSeatInfo(AdoptForm form, SeatInfoAdoptEntity seatEntity) {
    seatEntity.setSeatNo(form.getSeatNo());
    seatEntity.setName(form.getSeatName());
    seatEntity.setSize(form.getSeatSize());
    seatEntity.setMaterial(form.getSeatMaterial());
    seatEntity.setIntroduce(form.getSeatIntroduce());
    seatEntity.setPrice(form.getSeatPrice());
    seatEntity.setOwnerUnit(form.getOwnerUnit());
    seatEntity.setConstructUnit(form.getConstructUnit());
  }

  @Transactional
  public void insAdoptInfo(AdoptExcel adoptExcel) {

    AdoptForm form = new AdoptForm();
    BeanUtils.copyProperties(adoptExcel, form);

    SeatInfoAdoptEntity seatEntity = new SeatInfoAdoptEntity();
    setSeatInfo(form, seatEntity);
    seatInfoMapper.insert(seatEntity);

    AdoptSeatInfoEntity entity = new AdoptSeatInfoEntity();
    entity.setSeatId(seatEntity.getId());
    form.setStatus(AdoptStatus.PUBLISH_WAIT.status());
    setInfo(form, entity);
    entity.setCreatedAt(LocalDateTime.now());
    adoptSeatInfoMapper.insert(entity);
  }
}
