package cn.xzxx.seats.web.vote.dto;

import java.util.List;
import lombok.Data;

@Data
public class QuestionModel {

  private String uuid;
  private String questionId;
  private Integer id;
  private String title;
  private List<String> imageList;
  private String details;
  private String type;
  private String number;
  private boolean must;
  private boolean description;
  private String userDisplay;
  private String questionRelation;
  private List<QuestionOptionModel> options;
  private List<QuestionModel> appendQues;
  private Integer parentId;

  private String appendCondition;
  private String appendMark;
  private Integer fillboxType;

  private String showByQuestionId;
  private Integer showByQuestionOption;
}
