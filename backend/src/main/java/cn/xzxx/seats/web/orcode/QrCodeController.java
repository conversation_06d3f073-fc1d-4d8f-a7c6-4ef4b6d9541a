package cn.xzxx.seats.web.orcode;

import cn.xzxx.seats.common.message.MessageConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.config.ProjectConfig;
import cn.xzxx.seats.config.security.ResponseSupport;
import cn.xzxx.seats.web.orcode.dto.ReadExcelUpkeepUser;
import cn.xzxx.seats.web.orcode.dto.UpkeepUserSearchCondition;
import com.alibaba.excel.EasyExcel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
public class QrCodeController {

  @Autowired
  private QrCodeService qrCodeService;

  public static final Logger logger = LoggerFactory.getLogger(QrCodeController.class);

//  /**
//   * create a new qrCode
//   *
//   * @return response ok
//   */
//  @GetMapping(value = { "/qrCode/download/{donateId}"})
//  public ResponseEntity<byte[]> qrCodeDownload(@PathVariable Integer donateId) {
//
//    try {
//      ByteArrayOutputStream os = new ByteArrayOutputStream();
//      String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
//
//      ByteArrayInputStream is = qrCodeService.createQrCode(donateId);
//      StreamUtils.copy(is, os);
//
//      String suffix = ".jpg";
//      MultiValueMap<String, String> headerMap = new LinkedMultiValueMap<>();
//      headerMap.set("Content-Type", String
//              .format("application/x-msdownload;filename=%s", URLEncoder.encode("id_" + donateId + "_qrcode_" + time + suffix, StandardCharsets.UTF_8.name())));
//      headerMap.set("Content-Length", String.valueOf(os.size()));
//      HttpHeaders headers = new HttpHeaders(headerMap);
//      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
//    } catch (Exception e) {
//      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0083);
//    }
//  }

  /**
   * create a new qrCode
   *
   * @return response ok
   */
  @GetMapping(value = { "/qrCode/download/{donateId}"})
  public ResponseEntity<byte[]> qrCodeDownload(@PathVariable String donateId) {

    try {

        //获取接口调用凭证access_token
        String appId = ProjectConfig.APP_ID;//小程序id
        String appKey = ProjectConfig.APP_KEY;//小程序密钥
        String token = qrCodeService.postToken(appId, appKey);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        //生成二维码
        BufferedInputStream is = qrCodeService.generateQrCode( "pages/chairInformation/chairInformation",  donateId.toString(), token);

        StreamUtils.copy(is, os);

        String suffix = ".jpg";
        MultiValueMap<String, String> headerMap = new LinkedMultiValueMap<>();
        headerMap.set("Content-Type", String
                .format("application/x-msdownload;filename=%s", URLEncoder.encode("id_" + donateId + "_qrcode_" + time + suffix, StandardCharsets.UTF_8.name())));
        headerMap.set("Content-Length", String.valueOf(os.size()));
        HttpHeaders headers = new HttpHeaders(headerMap);
        return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
        return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0083);
    }
  }

  /**
   * Excel导入
   *
   * @return response ok
   */
  @PostMapping(value = { "/import/user" })
  public BaseResponse userImport(MultipartFile file) {

    try {
      EasyExcel.read(file.getInputStream(), ReadExcelUpkeepUser.class,
              new ReadDataListener(qrCodeService)).sheet().doRead();

    } catch (Exception e) {
      logger.error(e.getMessage(), e);
    }
    return MessageResponse.newInstance().addSuccessMessage("养护人员导入成功！");
  }

    @GetMapping("/import/list")
    public BaseResponse listInit(UpkeepUserSearchCondition condition) {

        return qrCodeService.listInit(condition);
    }
}

