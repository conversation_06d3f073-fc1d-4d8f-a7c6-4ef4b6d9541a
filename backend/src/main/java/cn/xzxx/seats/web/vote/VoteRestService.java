package cn.xzxx.seats.web.vote;

import cn.xzxx.seats.code.ResponseStatus;
import cn.xzxx.seats.common.exception.BusinessException;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.VoteResponseDetailsMapper;
import cn.xzxx.seats.web.vote.dto.FeedbackDetail;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import com.google.common.collect.Maps;

import cn.xzxx.seats.code.VoteStatus;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.common.response.PulldownItem;
import cn.xzxx.seats.common.utils.DateUtils;
import cn.xzxx.seats.common.utils.ListUtils;
import cn.xzxx.seats.common.utils.StringUtil;
import cn.xzxx.seats.component.PulldownComponent;
import cn.xzxx.seats.repository.mapper.VoteInfoMapper;
import cn.xzxx.seats.repository.mapper.VoteQuestionMapper;
import cn.xzxx.seats.repository.mapper.VoteQuestionOptionsMapper;
import cn.xzxx.seats.web.vote.dto.FeedbackCondition;
import cn.xzxx.seats.web.vote.dto.FeedbackRecord;
import cn.xzxx.seats.web.vote.dto.QuestionModel;
import cn.xzxx.seats.web.vote.dto.QuestionOptionModel;
import cn.xzxx.seats.web.vote.dto.VoteModel;
import cn.xzxx.seats.web.vote.dto.VoteRecord;
import cn.xzxx.seats.web.vote.dto.VoteSearchCondition;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.google.common.collect.Lists;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class VoteRestService {

  @Autowired
  private VoteInfoMapper voteInfoMapper;

  @Autowired
  private VoteQuestionMapper questionMapper;

  @Autowired
  private VoteQuestionOptionsMapper questionOptionsMapper;

  @Autowired
  private VoteRestMapper voteRestMapper;

  @Autowired
  private PulldownComponent pulldownComponent;

  @Autowired
  private VoteResponseDetailsMapper responseDetailsMapper;

  @Autowired
  private FileUploadComponent fileUploadComponent;

  public BaseResponse search(VoteSearchCondition condition) {

    ListResponse<VoteRecord> listResponse = new ListResponse<>();

    if (condition.isInit()) {
      List<PulldownItem> statusOptions = pulldownComponent.voteStatusPulldown();
      listResponse.putData("statusOptions", statusOptions);
    }

    voteRestMapper.selectDataList(condition, listResponse, e -> {
      VoteRecord record = new VoteRecord();
      record.setId(e.getId());
      record.setTitle(e.getTitle());
      record.setStatus(e.getStatus());
      record.setStatusText(VoteStatus.statusName(e.getStatus()));
      record.setStartTime(DateUtils.localDateTimeFormat(e.getStartTime()));
      record.setEndTime(DateUtils.localDateTimeFormat(e.getEndTime()));
      record.setIntroduce(e.getIntroduce());
      record.setCreatedAt(DateUtils.localDateTimeFormat(e.getCreatedAt()));
      record.setQuesCount(e.getQuesCount());
      record.setFeedbackCount(e.getFeedbackCount());
      return record;
    });
    return listResponse;
  }

  public VoteModel get(String id, MessageResponse messages) {

    VoteModel model = new VoteModel();
    VoteInfoEntity infoEntity = voteInfoMapper.selectById(id);
    if (infoEntity == null) {
      messages.addMessage("该问卷已被删除，请重新检索后操作。");
      return null;
    }
    model.setId(infoEntity.getId());
    model.setTitle(infoEntity.getTitle());
    model.setDescribe(infoEntity.getIntroduce());
    model.setTitleImage(infoEntity.getTitleImage());
    model.setStatus(infoEntity.getStatus());
    model.setCreatedTime(DateUtils.localDateTimeFormat(infoEntity.getCreatedAt()));

    // 题目一览 检索
    List<VoteQuestionEntity> questionEntityList = questionMapper
      .selectList(new QueryWrapper<VoteQuestionEntity>().eq("vote_id", id));

    model.setQuestionList(questionEntityList.stream()
      .sorted(Comparator.comparingInt(VoteQuestionEntity::getDisplayOrder)).map(e -> {
        QuestionModel questionModel = new QuestionModel();
        questionModel.setUuid(e.getUuid());
        questionModel.setQuestionId(e.getQuestionNo());
        questionModel.setId(e.getId());
        questionModel.setTitle(e.getTitle());
        List<String> imageList = new ArrayList<>();
        if (!StringUtil.isEmpty(e.getTitleImage())) {
          imageList.add(e.getTitleImage());
        }
        if (!StringUtil.isEmpty(e.getTitleImage2())) {
          imageList.add(e.getTitleImage2());
        }
        if (!StringUtil.isEmpty(e.getTitleImage3())) {
          imageList.add(e.getTitleImage3());
        }
        questionModel.setImageList(imageList);
        questionModel.setDetails(e.getDetails());
        questionModel.setType(e.getType());
        questionModel.setNumber(e.getQuestionNo());
        questionModel.setMust(true);
        questionModel.setOptions(Lists.newArrayList());
        return questionModel;
      }).collect(Collectors.toList()));

    // 选项内容 检索
    List<VoteQuestionOptionsEntity> optionsEntityList = questionOptionsMapper
      .selectList(new QueryWrapper<VoteQuestionOptionsEntity>().eq("vote_id", id));

    Map<Integer, List<QuestionOptionModel>> questionOptionMap = optionsEntityList.stream().map(e -> {
      QuestionOptionModel optionModel = new QuestionOptionModel();
      optionModel.setQuestionId(e.getQuestionId());
      optionModel.setOptionId(e.getAnswerId());
      optionModel.setText(e.getText());
      return optionModel;
    }).collect(Collectors.groupingBy(QuestionOptionModel::getQuestionId));

    for (QuestionModel questionModel : model.getQuestionList()) {
      Integer questionId = questionModel.getId();
      List<QuestionOptionModel> optionModelList = questionOptionMap.get(questionId);
      questionModel.setOptions(optionModelList);
    }
    return model;
  }

  @Transactional
  public BaseResponse save(VoteModel model, MessageResponse messages) {

    VoteInfoEntity entity;
    if ("add".equals(model.getOptType())) {
      entity = new VoteInfoEntity();
    } else {
      Integer wjId = model.getId();
      entity = voteInfoMapper.selectById(wjId);

      if (entity == null) {
        return messages.addMessage("该问卷已被删除，请重新检索后操作。");
      }
    }
    entity.setTitle(model.getTitle());
    entity.setTitleImage(model.getTitleImage());
    entity.setStatus(model.getStatus());
    entity.setIntroduce(model.getDescribe());

    if ("add".equals(model.getOptType())) {
      voteInfoMapper.insert(entity);
    } else {
      voteInfoMapper.updateById(entity);
    }

    List<QuestionModel> questionList = model.getQuestionList();

    questionMapper.delete(new QueryWrapper<VoteQuestionEntity>().eq("vote_id", entity.getId()));
    questionOptionsMapper.delete(new QueryWrapper<VoteQuestionOptionsEntity>().eq("vote_id", entity.getId()));

    for (QuestionModel questionModel : questionList) {
      VoteQuestionEntity questionEntity = new VoteQuestionEntity();
      questionEntity.setVoteId(entity.getId());
      questionEntity.setQuestionNo(questionModel.getNumber());
      questionEntity.setUuid(questionModel.getUuid());
      questionEntity.setType(questionModel.getType());
      questionEntity.setTitle(questionModel.getTitle());
      questionEntity.setTitleCss("");
      List<String> imageList = questionModel.getImageList();
      if (ListUtils.isNotEmpty(imageList)) {
        if (imageList.size() > 0) {
          questionEntity.setTitleImage(imageList.get(0));
        }
        if (imageList.size() > 1) {
          questionEntity.setTitleImage(imageList.get(1));
        }
        if (imageList.size() > 2) {
          questionEntity.setTitleImage(imageList.get(2));
        }
      }
      questionEntity.setDetails(questionModel.getDetails());
      questionEntity.setDisplayOrder(0);
      questionEntity.setDisplay(1);

      questionMapper.insert(questionEntity);

      List<QuestionOptionModel> optionModels = questionModel.getOptions();

      for (int i = 0, optionModelsSize = optionModels.size(); i < optionModelsSize; i++) {
        QuestionOptionModel optionModel = optionModels.get(i);
        VoteQuestionOptionsEntity optionsEntity = new VoteQuestionOptionsEntity();
        optionsEntity.setVoteId(entity.getId());
        optionsEntity.setQuestionId(questionEntity.getId());
        optionsEntity.setAnswerId(i);
        optionsEntity.setText(optionModel.getText());
        optionsEntity.setImage(null);
        questionOptionsMapper.insert(optionsEntity);
      }
    }
    return new BaseResponse();
  }

  public BaseResponse feedback(String voteId, FeedbackCondition condition, MessageResponse messages) {
    int count = voteInfoMapper.selectCount(new QueryWrapper<VoteInfoEntity>()
      .eq("id", voteId)
      .and(i -> i.in("status", Arrays.asList(VoteStatus.PUBLISH.status(), VoteStatus.FINISHED.status()))));
    if (count == 0) {
      return messages.addMessage("该问卷不存在或者还未发布。");
    }
    ListResponse<FeedbackRecord> dataList = new ListResponse<>();

    if (condition.isInit()) {

      List<PulldownItem> statusOptions = pulldownComponent.getAnswerStatusPulldown();
      dataList.putData("statusOptions", statusOptions);

      VoteModel questionnaire = get(voteId, messages);
      if (messages.hasError()) {
        return messages;
      }

      List<QuestionModel> questionList = questionnaire.getQuestionList();
      questionnaire.setQuestionList(questionList);
      dataList.putData("questionnaire", questionnaire);
    }

    if (ListUtils.isEmpty(condition.getSorts())) {
      condition.setSorts(Collections.singletonList(OrderItem.desc("a.created_at")));
    }

    voteRestMapper.selectFeedbackList(condition, dataList, e -> {
      FeedbackRecord record = new FeedbackRecord();
      record.setId(e.getId());
      record.setVoteId(e.getVoteId());
      record.setOpenId(e.getOpenId());
      record.setStartTime(DateUtils.localDateTimeFormat(e.getStartTime()));
      record.setEndTime(DateUtils.localDateTimeFormat(e.getEndTime()));
      record.setValueFlg(ResponseStatus.label(e.getValueFlg()));
      record.setWxName(e.getWxName());

      if (e.getEndTime() != null) {
        long end = Date.from(e.getEndTime().toInstant(ZoneOffset.UTC)).getTime();
        long start = Date.from(e.getStartTime().toInstant(ZoneOffset.UTC)).getTime();
        record.setDuration(BigDecimal.valueOf((end - start) / 1000.0 / 60).setScale(1, RoundingMode.HALF_UP).doubleValue());
      }

      Map<Integer, List<VoteResponseDetailsEntity>> responseDetailMap = responseDetailsMapper
        .selectList(new QueryWrapper<VoteResponseDetailsEntity>()
          .eq("vote_id", e.getVoteId()).eq("response_id", e.getId())).stream()
        .collect(Collectors.groupingBy(VoteResponseDetailsEntity::getQuestionId));
      record.setFeedbackDetailMap(Maps.newHashMap());

      Map<Integer, List<String>> questionAnswerMap = new HashMap<>();

      responseDetailMap.forEach((k, v) -> {
        List<String> userOptions = v.stream().filter(uo -> uo.getUserOption() != null)
          .map(uo -> String.valueOf(uo.getUserOption())).collect(Collectors.toList());
        questionAnswerMap.put(k, userOptions);
      });
      record.setFeedbackDetailMap(questionAnswerMap);
      return record;
    });

    return dataList;
  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse delete(String id) {
    // 问卷删除
    VoteInfoEntity voteInfo = voteInfoMapper.selectById(id);
    if (voteInfo == null) {
      throw new BusinessException("该问卷已被删除，请刷新页面后再操作。");
    }
    voteInfoMapper.deleteById(voteInfo.getId());

    String image = voteInfo.getTitleImage();
    fileUploadComponent.delete(image);

    // TODO 问卷关联信息删除

    return MessageResponse.newInstance().addSuccessMessage("问卷删除成功！");
  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse cancel(String id) {

    VoteInfoEntity entity = voteInfoMapper.selectById(id);
    if(entity == null || entity.getStatus() != 1) {
      throw new BusinessException("该投票信息不在发布状态，无法取消发布，请确认。");
    }
    // 认养取消发布
    entity.setStatus(0);
    voteInfoMapper.updateById(entity);

    return MessageResponse.newInstance().addSuccessMessage("取消发布成功！");
  }
}
