package cn.xzxx.seats.web.donate;

import cn.xzxx.seats.common.message.MessageConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.config.security.ResponseSupport;
import cn.xzxx.seats.web.common.CommonService;
import cn.xzxx.seats.web.donate.dto.*;
import cn.xzxx.seats.web.seats.SeatRestService;
import java.io.ByteArrayOutputStream;

import com.alibaba.excel.EasyExcel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
public class DonateRestController {

  @Autowired
  private DonateRestService service;

  @Autowired
  private SeatRestService seatRestService;

  @Autowired
  private CommonService commonService;

  public static final Logger logger = LoggerFactory.getLogger(DonateRestController.class);

  @GetMapping("/donate/list")
  public BaseResponse listInit(DonateSearchCondition condition) {

    return service.listInit(condition);
  }

  @GetMapping("/donate/init")
  public BaseResponse init(DonateParam param) {

    return service.init(param);
  }

  @GetMapping("/donate/seat/{id}")
  public BaseResponse getSeat(@PathVariable String id) {
    return seatRestService.get(id);
  }

  @PostMapping("/donate/add")
  public BaseResponse add(DonateForm donateForm) {

    return service.edit(true, donateForm);
  }

  @PostMapping("/donate/edit")
  public BaseResponse edit(DonateForm donateForm) {

    return service.edit(false, donateForm);
  }

  @PostMapping("/donate/updStatus")
  public BaseResponse updStatus(DonateForm donateForm) {

    return service.updStatus(donateForm);
  }

  @GetMapping("/donate-apply/list")
  public BaseResponse applyListInit(DonateSearchCondition condition) {

    return service.applyListInit(condition);
  }

  @GetMapping("/donate-apply/init")
  public BaseResponse initApply(DonateParam param) {

    return service.initApply(param);
  }

  @PostMapping("/donate-apply/add")
  public BaseResponse addApply(DonateApplyForm donateApplyForm) {

    return service.editApply(true, donateApplyForm);
  }

  @PostMapping("/donate-apply/edit")
  public BaseResponse editApply(DonateApplyForm donateApplyForm) {

    return service.editApply(false, donateApplyForm);
  }

  @PostMapping("/donate-apply/updStatus")
  public BaseResponse updStatusApply(DonateApplyForm form) {

    return service.updStatusApply(form);
  }

  @GetMapping("/donate/export")
  public ResponseEntity<byte[]> donateExport(DonateSearchCondition condition) {

    try {
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      service.exportDonate(os, condition);
      String name = commonService.getExcelName("捐赠统计");
      HttpHeaders headers = commonService.getHttpHeaders(name, os);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0081);
    }
  }

  @GetMapping("/donate-apply/export")
  public ResponseEntity<byte[]> donateApplyExport(DonateSearchCondition condition) {

    try {
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      service.exportDonateApply(os, condition);
      String name = commonService.getExcelName("捐赠申请统计");
      HttpHeaders headers = commonService.getHttpHeaders(name, os);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0081);
    }
  }

  @DeleteMapping("/donate/delete/{id}")
  public BaseResponse delete(@PathVariable String id) {
    return service.delete(id);
  }

  @DeleteMapping("/donate-apply/delete/{id}")
  public BaseResponse deleteApply(@PathVariable String id) {
    return service.deleteApply(id);
  }

  @GetMapping("/donate/cancel/{id}")
  public BaseResponse cancel(@PathVariable String id) {
    return service.cancel(id);
  }

  @GetMapping("/donate-comment/list")
  public BaseResponse commentListInit(DonateSearchCondition condition) {

    return service.commentListInit(condition);
  }


  @PostMapping("/donate-comment/updStatus")
  public BaseResponse updStatusComment(DonateCommentForm form) {

    return service.updStatusComment(form);
  }

  @DeleteMapping("/donate-comment/delete/{id}")
  public BaseResponse deleteComment(@PathVariable String id) {
    return service.deleteComment(id);
  }

  @PostMapping(value = { "/import/donate" })
  public BaseResponse donateImport(MultipartFile file) {

    try {
      EasyExcel.read(file.getInputStream(), DonateExcel.class,
              new ReadDataListener(service)).sheet().doRead();

    } catch (Exception e) {
      logger.error(e.getMessage(), e);
    }
    return MessageResponse.newInstance().addSuccessMessage("认养信息导入成功！");
  }

  @GetMapping("/donate-apply/getSeatInfoByDonateNo/{donateNo}")
  public BaseResponse getSeatInfoByDonateNo(@PathVariable String donateNo) {

    return service.getSeatInfoByDonateNo(donateNo);
  }

}

