package cn.xzxx.seats.web.seats.dto;

import lombok.Data;

@Data
public class SeatRecord {

  /**
   * 座椅ID
   */
  private Integer id;

  /**
   * 座椅编号
   */
  private String seatNo;

  /**
   * 座椅名称
   */
  private String name;

  /**
   * 座椅大小
   */
  private String size;

  /**
   * 座椅材质
   */
  private String material;

  /**
   * 座椅主图URL
   */
  private String image;

  /**
   * 座椅介绍
   */
  private String introduce;

  /**
   * 座椅状态 0:未发布 1:发布 2:下架
   */
  private Integer status;

  /**
   * 座椅状态 0:未发布 1:发布 2:下架
   */
  private String statusText;

  /**
   * 座椅价格 单位(分)
   */
  private Integer price;

  /**
   * 发布开始时间
   */
  private String startTime;

  /**
   * 发布结束时间
   */
  private String endTime;
}
