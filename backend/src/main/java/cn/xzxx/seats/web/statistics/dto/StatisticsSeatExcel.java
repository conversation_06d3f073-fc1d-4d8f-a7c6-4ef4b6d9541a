package cn.xzxx.seats.web.statistics.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER,verticalAlignment = VerticalAlignment.CENTER)//标题样式,垂直水平居中
@HeadFontStyle(fontName = "微软雅黑",fontHeightInPoints = 11,bold = false)//表头字体样式
@HeadRowHeight(value = 25)//表头行高
@ContentFontStyle(fontName = "微软雅黑",fontHeightInPoints = 11)//内容字体样式
@ContentRowHeight(value = 20)//内容行高
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER,verticalAlignment = VerticalAlignment.CENTER)//内容样式,垂直水平居中
public class StatisticsSeatExcel {

    /**
     * 所在省
     */
    @ColumnWidth(value = 15)
    @ExcelProperty(value = "所在省",index = 0)
    private String province;

    /**
     * 所在城市
     */
    @ExcelProperty(value = "所在城市",index = 1)
    @ColumnWidth(value = 15)
    private String city;

    /**
     * 所在区
     */
    @ExcelProperty(value = "所在区",index = 2)
    @ColumnWidth(value = 15)
    private String strict;

    /**
     * 街道
     */
    @ExcelProperty(value = "所在街道",index = 3)
    @ColumnWidth(value = 15)
    private String street;

    /**
     * 发布座椅总数
     */
    @ExcelProperty(value = "发布座椅总数",index = 4)
    @ColumnWidth(value = 18)
    private Integer seatCount;

    /**
     * 捐赠率
     */
    @ExcelProperty(value = "捐赠率",index = 5)
    @ColumnWidth(value = 15)
    private double donateRate;

    /**
     * 空置率
     */
    @ExcelProperty(value = "空置率",index = 6)
    @ColumnWidth(value = 15)
    private double emptyRate;

    /**
     * 捐赠总数量
     */
    @ExcelProperty(value = "捐赠总数量",index = 7)
    @ColumnWidth(value = 18)
    private Integer donateCountTotal;

    /**
     * 捐赠总金额
     */
    @ExcelProperty(value = "捐赠总金额",index = 8)
    @ColumnWidth(value = 18)
    private Integer payedPriceTotal;
}
