package cn.xzxx.seats.web.vote.entity;

import cn.xzxx.seats.common.base.BaseRecordEntity;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class VoteRecordEntity extends BaseRecordEntity {
  private long id;
  private String title;
  private Integer status;
  private LocalDateTime startTime;
  private LocalDateTime endTime;
  private String introduce;
  private LocalDateTime createdAt;
  private int quesCount;
  private int feedbackCount;
}
