package cn.xzxx.seats.web.donate.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 座椅捐赠评论表
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class DonateCommentRecord implements Serializable {


    /**
     * 评论ID
     */
    private Integer id;

    /**
     * 可捐赠座椅ID
     */
    private Integer donateId;

    /**
     * 评论内容
     */
    private String comment;

    /**
     * 评论人open_id
     */
    private String openId;

    /**
     * 评论人昵称
     */
    private String name;

    /**
     * 审核状态 0-审核中(不展示) 1-审核通过(展示)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 修改时间
     */
    private String updatedAt;

    /**
     * 更新者
     */
    private String updatedBy;

    /**
     * 状态名称
     */
    private String statusText;

}
