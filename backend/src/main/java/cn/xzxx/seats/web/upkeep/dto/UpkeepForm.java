package cn.xzxx.seats.web.upkeep.dto;

import cn.xzxx.seats.common.base.UploadFiles;
import cn.xzxx.seats.common.response.PulldownItem;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class UpkeepForm {

  /**
   * 养护管理ID
   */
  private Integer id;

  /**
   * 认养NO
   */
  private String adoptNo;

  /**
   * 捐赠NO
   */
  private String donateNo;

  /**
   * 座椅ID
   */
  private Integer seatId;

  /**
   * 座椅编号
   */
  private String seatNo;

  /**
   * 座椅名称
   */
  private String seatName;

  /**
   * 座椅大小
   */
  private String seatSize;

  /**
   * 座椅材质
   */
  private String seatMaterial;

  /**
   * 座椅图片
   */
  private UploadFiles seatImage;

  /**
   * 座椅介绍
   */
  private String seatIntroduce;

  /**
   * 座椅价格
   */
  private Integer seatPrice;

  /**
   * 所在省
   */
  private String province;

  /**
   * 所在城市
   */
  private String city;

  /**
   * 所在区
   */
  private String strict;

  /**
   * 所在街道
   */
  private String street;

  /**
   * 详细地址
   */
  private String adress;

  /**
   * 养护状态
   */
  private Integer status;

  /**
   * 捐赠状态名称
   */
  private String statusText;

  /**
   * 省市县街道
   */
  private String pcdName;

  /**
   * 建造单位
   */
  private String ownerUnit;

  /**
   * 施工单位
   */
  private String constructUnit;

  /**
   * 维护开始日期
   */
  private String upkeepDateBegin;

  /**
   * 维护结束日期
   */
  private String upkeepDateEnd;

  private List<PulldownItem> upkeepUserOptions;

  private Integer upkeepUserId;
}