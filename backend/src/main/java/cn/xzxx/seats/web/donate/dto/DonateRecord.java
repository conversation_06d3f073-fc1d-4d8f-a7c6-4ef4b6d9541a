package cn.xzxx.seats.web.donate.dto;

import cn.xzxx.seats.common.base.UploadFiles;
import lombok.Data;

@Data
public class DonateRecord {

    /**
     * 可捐赠座椅ID
     */
    private Integer id;

    /**
     * 捐赠/认养座椅NO
     */
    private String donateNo;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * GPS经度
     */
    private Double gpsLng;

    /**
     * GPS维度
     */
    private Double gpsLat;

    /**
     * 认捐截止时间（到达截止时间 且 认捐份额>=100 ）
     */
    private String validTime;

    /**
     * 捐赠状态 0:已取消 1:未发布 2:已发布 3:捐赠中 4:捐赠完成 5:建造中 6:建造完成
     */
    private Integer status;

    /**
     * 捐赠状态名称
     */
    private String statusText;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

    /**
     * 座椅价格
     */
    private Integer seatPrice;

    /**
     * 省市县街道
     */
    private String pcdName;

    /**
     * 座椅大小
     */
    private String seatSize;

    /**
     * 座椅材质
     */
    private String seatMaterial;

    /**
     * 座椅主图
     */
    private UploadFiles seatImage;

    private String seatIntroduce;
}
