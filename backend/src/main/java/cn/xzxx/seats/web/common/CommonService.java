package cn.xzxx.seats.web.common;

import cn.xzxx.seats.code.AreaTypeStatus;
import cn.xzxx.seats.repository.entity.SysAreaEntity;
import cn.xzxx.seats.repository.mapper.SysAreaMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class CommonService {

    @Autowired
    private SysAreaMapper sysAreaMapper;

    public String getPcdName(String province, String city, String district, String street) {

//        if(StringUtils.isEmpty(province) || StringUtils.isEmpty(city)
//                || StringUtils.isEmpty(district) || StringUtils.isEmpty(street)) {
//            return null;
//        }
//        List<SysAreaEntity> sysAreaEntities = sysAreaMapper.selectList(new LambdaQueryWrapper<SysAreaEntity>()
//                .eq(SysAreaEntity::getIdProvince, province)
//                .eq(SysAreaEntity::getIdCity, city)
//                .eq(SysAreaEntity::getIdDistrict, district)
//                .eq(SysAreaEntity::getIdStreet, street));
//        if(CollectionUtils.isEmpty(sysAreaEntities)) {
//            return null;
//        }
//
//        return sysAreaEntities.get(0).getNameProvince()
//                + "/" + sysAreaEntities.get(0).getNameCity()
//                + "/" + sysAreaEntities.get(0).getNameDistrict()
//                + "/" + sysAreaEntities.get(0).getNameStreet();
        return String.join("/", province, city, district, street);
    }

    public String getStrictName(String province, String city, String district) {

        return String.join("/", province, city, district);
    }

    public String getCityName(String province, String city) {

        return String.join("/", province, city);
    }

    public String getPcdName(Integer type, String province, String city, String district, String street) {

        if(type == AreaTypeStatus.CITY.status()) {
            return getCityName(province, city);
        } else if(type == AreaTypeStatus.STRICT.status()) {
            return getStrictName(province, city, district);
        } else if(type == AreaTypeStatus.STREET.status()) {
            return getPcdName(province, city, district, street);
        } else {
           return province;
        }
    }

    public String getExcelName(String name) {

        name += LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        if(!name.endsWith(".xlsx")) {
            name += ".xlsx";
        }
        return name;
    }

    @NotNull
    public HttpHeaders getHttpHeaders(String name, ByteArrayOutputStream os) throws UnsupportedEncodingException {
        MultiValueMap<String, String> headerMap = new LinkedMultiValueMap<>();
        headerMap.set("Content-Type", String
                .format("application/x-msdownload;filename=%s", URLEncoder.encode(name, StandardCharsets.UTF_8.name())));
        headerMap.set("Content-Length", String.valueOf(os.size()));
        HttpHeaders headers = new HttpHeaders(headerMap);
        return headers;
    }
}
