package cn.xzxx.seats.web.adopt.dto;

import lombok.Data;

@Data
public class AdoptUpkeepRecord {

    /**
     * ID
     */
    private Integer id;

    /**
     * 捐赠编号
     */
    private String donateNo;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 认养状态 1:申请中 2:审核通过 3:已过期
     */
    private Integer status;

    /**
     * 认养状态名称
     */
    private String statusText;

    /**
     * 认养有效期（默认一年）
     */
    private String validTime;

    /**
     * 所在省
     */
    private String seatProvince;

    /**
     * 所在城市
     */
    private String seatCity;

    /**
     * 所在区
     */
    private String seatStrict;

    /**
     * 所在街道
     */
    private String seatStreet;

    /**
     * 省市县街道
     */
    private String seatPcdName;

    /**
     * 维护时间
     */
    private String upkeepTime;

    /**
     * 维护前图片
     */
    private String upkeepImageBefore;

    /**
     * 维护后图片
     */
    private String upkeepImageAfter;

    /**
     * 维护备注
     */
    private String memo;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * GPS经度
     */
    private Double gpsLng;

    /**
     * GPS维度
     */
    private Double gpsLat;

    /**
     * 省市县街道
     */
    private String pcdName;
}
