package cn.xzxx.seats.web.info;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.web.info.dto.InformationDetailsForm;
import cn.xzxx.seats.web.info.dto.InformationForm;
import cn.xzxx.seats.web.info.dto.InformationSearchCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class InformationRestController {

  @Autowired
  private InformationRestService service;

  @GetMapping("/info/list")
  public BaseResponse listInit(InformationSearchCondition condition) {

    return service.listInit(condition);
  }

  @GetMapping("/info/init")
  public BaseResponse init(Integer id) {

    return service.init(id);
  }

  @PostMapping("/info/create")
  public BaseResponse create(InformationForm form) {

    return service.edit(true, form);
  }

  @PostMapping("/info/edit")
  public BaseResponse edit(InformationForm form) {

    return service.edit(false, form);
  }

  @GetMapping("/info/delete")
  public BaseResponse delete(Integer id) {

    return service.delete(id);
  }

  @GetMapping("/info/list-details")
  public BaseResponse listInitDetails(InformationSearchCondition condition) {

    return service.listInitDetails(condition);
  }

  @GetMapping("/info/init-details")
  public BaseResponse initDetails(Integer id) {

    return service.initDetails(id);
  }

  @PostMapping("/info/create-details")
  public BaseResponse createDetails(InformationDetailsForm form) {

    return service.editDetails(true, form);
  }

  @PostMapping("/info/edit-details")
  public BaseResponse editDetails(InformationDetailsForm form) {

    return service.editDetails(false, form);
  }

  @GetMapping("/info/delete-details")
  public BaseResponse deleteDetails(Integer id) {

    return service.deleteDetails(id);
  }
}
