package cn.xzxx.seats.web.orcode;

import cn.xzxx.seats.common.exception.BusinessException;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.common.utils.StringUtil;
import cn.xzxx.seats.config.ProjectConfig;
import cn.xzxx.seats.repository.entity.AdoptUpkeepUserEntity;
import cn.xzxx.seats.repository.mapper.AdoptUpkeepUserMapper;
import cn.xzxx.seats.web.adopt.QRUtil;
import cn.xzxx.seats.web.orcode.dto.ReadExcelUpkeepUser;
import cn.xzxx.seats.web.orcode.dto.UpkeepUserSearchCondition;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;

@Service
public class QrCodeService {

    @Autowired
    private AdoptUpkeepUserMapper adoptUpkeepUserMapper;

    public ByteArrayInputStream createQrCode(Integer donateId) {
        try {
            String newNotifyUrl = ProjectConfig.REDIRECT_SEAT_INTRODUCE_URL
                    + "?donateId=" + donateId;

            String urlCode = ProjectConfig.WX_REQUEST_CODE_URL
                    + "appid=" + ProjectConfig.APP_ID
                    + "&redirect_uri=" + URLEncoder.encode(newNotifyUrl, "UTF-8")
                    + "&response_type=code"
                    + "&scope=snsapi_userinfo"
                    + "&state=123"
                    + "#wechat_redirect";

            //生成二维码
            return createBitMatrix(urlCode);

        } catch (WriterException | IOException e) {
            throw new BusinessException("二维码生成失败");
        }
    }

    /**
     *
     * @param urlCode
     * @return
     * @throws WriterException
     * @throws IOException
     */
    protected ByteArrayInputStream createBitMatrix(String urlCode) throws WriterException, IOException {
        HashMap<EncodeHintType, Object> hints = new HashMap<>();
        //内容所使用编码
        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
        BitMatrix bitMatrix = new MultiFormatWriter().encode(urlCode, BarcodeFormat.QR_CODE, 300, 300, hints);
//        bitMatrix = deleteWhite(bitMatrix);
        BufferedImage bufferedImage = QRUtil.toBufferedImage(bitMatrix);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(bufferedImage, "JPG", os);
        return new ByteArrayInputStream(os.toByteArray());

    }

    /**
     * 接口调用凭证 access_token
     */
    public String postToken(String appId, String appKey) throws Exception {

        String requestUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appId + "&secret=" + appKey;
        URL url = new URL(requestUrl);
        // 打开和URL之间的连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        // 设置通用的请求属性
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setUseCaches(false);
        connection.setDoOutput(true);
        connection.setDoInput(true);

        // 得到请求的输出流对象
        DataOutputStream out = new DataOutputStream(connection.getOutputStream());
        out.writeBytes("");
        out.flush();
        out.close();

        // 建立实际的连接
        connection.connect();
        // 定义 BufferedReader输入流来读取URL的响应
        BufferedReader in;
        if (requestUrl.contains("nlp"))
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "GBK"));
        else
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
        StringBuilder result = new StringBuilder();
        String getLine;
        while ((getLine = in.readLine()) != null) {
            result.append(getLine);
        }
        in.close();
        JSONObject jsonObject = JSONObject.parseObject(result.toString());
        return jsonObject.getString("access_token");
    }

    /*
      * @param filePath
      *         本地生成二维码路径
      * @param page
      *         当前小程序相对页面 必须是已经发布的小程序存在的页面（否则报错），例如 pages/index/index, 根路径前不要填加 /,不能携带参数（参数请放在scene字段里），如果不填写这个字段，默认跳主页面
      * @param scene
      *         最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）
      * @param accessToken
      *         接口调用凭证
      */
    public BufferedInputStream generateQrCode(String page, String scene, String accessToken) {

        try {

            //调用微信接口生成二维码
            URL url = new URL("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken);
            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
            httpURLConnection.setRequestMethod("POST");// 提交模式
            // conn.setConnectTimeout(10000);//连接超时 单位毫秒
            // conn.setReadTimeout(2000);//读取超时 单位毫秒
            // 发送POST请求必须设置如下两行
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            PrintWriter printWriter = new PrintWriter(httpURLConnection.getOutputStream());
            // 发送请求参数
            JSONObject paramJson = new JSONObject();
            //这就是你二维码里携带的参数 String型  名称不可变
            paramJson.put("scene", scene);
            //注意该接口传入的是page而不是path
            paramJson.put("page", page);
            //这是设置扫描二维码后跳转的页面
            paramJson.put("width", 200);
            paramJson.put("is_hyaline", true);
            paramJson.put("auto_color", true);
            printWriter.write(paramJson.toString());
            // flush输出流的缓冲
            printWriter.flush();

            //开始获取数据
            BufferedInputStream bis = new BufferedInputStream(httpURLConnection.getInputStream());
            return bis;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * excel入库
     * @param qrCode
     */
    public void insUpkeepUser(ReadExcelUpkeepUser qrCode) {

        List<AdoptUpkeepUserEntity> adoptUpkeepUserEntities = adoptUpkeepUserMapper.selectList(new LambdaQueryWrapper<AdoptUpkeepUserEntity>().eq(AdoptUpkeepUserEntity::getTelephone, qrCode.getTelephone()));
        if(CollectionUtils.isEmpty(adoptUpkeepUserEntities)) {
            AdoptUpkeepUserEntity adoptUpkeepUserEntity = new AdoptUpkeepUserEntity();
            setUpkeepInfo(qrCode, adoptUpkeepUserEntity);
            adoptUpkeepUserEntity.setUpdatedBy("system");
            adoptUpkeepUserMapper.insert(adoptUpkeepUserEntity);
        } else {
            AdoptUpkeepUserEntity adoptUpkeepUserEntity = adoptUpkeepUserEntities.get(0);
            setUpkeepInfo(qrCode, adoptUpkeepUserEntity);
            adoptUpkeepUserMapper.updateById(adoptUpkeepUserEntity);
        }
    }

    private void setUpkeepInfo(ReadExcelUpkeepUser qrCode, AdoptUpkeepUserEntity adoptUpkeepUserEntity) {
        adoptUpkeepUserEntity.setTelephone(qrCode.getTelephone());
        adoptUpkeepUserEntity.setName(qrCode.getName());
        adoptUpkeepUserEntity.setCompany(qrCode.getCompany());
    }

    public BaseResponse listInit(UpkeepUserSearchCondition condition) {

        ListResponse<AdoptUpkeepUserEntity> listResponse = new ListResponse<>();

        List<AdoptUpkeepUserEntity> adoptUpkeepUserEntities = adoptUpkeepUserMapper.selectList(new LambdaQueryWrapper<AdoptUpkeepUserEntity>()
                .eq(!StringUtil.isEmpty(condition.getTelephone()), AdoptUpkeepUserEntity::getTelephone, condition.getTelephone())
                .orderByDesc(AdoptUpkeepUserEntity::getCreatedAt));

        listResponse.setValue(adoptUpkeepUserEntities);
        return listResponse;
    }
}
