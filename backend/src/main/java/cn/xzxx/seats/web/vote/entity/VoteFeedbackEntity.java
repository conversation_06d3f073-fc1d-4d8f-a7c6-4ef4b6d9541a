package cn.xzxx.seats.web.vote.entity;

import cn.xzxx.seats.common.base.BaseRecordEntity;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class VoteFeedbackEntity extends BaseRecordEntity {

  /**
   * 投票反馈ID
   */
  private Integer id;

  /**
   * 问卷ID
   */
  private Integer voteId;

  /**
   * 投票反馈ID
   */
  private Integer responseId;

  /**
   * 用户openId
   */
  private String openId;

  /**
   * 投票填写开始时间
   */
  private LocalDateTime startTime;

  /**
   * 投票填写结束时间
   */
  private LocalDateTime endTime;

  /**
   * 是否有效 0-无效 1-有效
   */
  private Integer valueFlg;

  private String wxName;
}
