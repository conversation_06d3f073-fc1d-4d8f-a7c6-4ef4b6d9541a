package cn.xzxx.seats.web.donate.entity;

import cn.xzxx.seats.common.base.BaseRecordEntity;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DonateApplyRecordEntity extends BaseRecordEntity {

    /**
     * ID
     */
    private Integer id;

    /**
     * 捐赠编号
     */
    private String donateNo;

    /**
     * 捐赠人open_id
     */
    private String openId;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

    /**
     * 座椅价格
     */
    private Integer seatPrice;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 捐赠份额（1-100）
     */
    private Integer shareNum;

    /**
     * 捐赠状态 1:申请中 2:捐赠审核通过 3:不通过 4:无效
     */
    private Integer status;

    /**
     * 认捐截止时间（到达截止时间 且 认捐份额>=100 ）
     */
    private LocalDateTime validTime;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 申请通过时间
     */
    private LocalDateTime applyPassTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 修改时间
     */
    private LocalDateTime updatedAt;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 区划代码
     */
    private String code;

}
