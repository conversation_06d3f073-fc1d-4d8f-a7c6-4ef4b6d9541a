package cn.xzxx.seats.web.upkeep;

import cn.xzxx.seats.code.UpkeepAssignStatus;
import cn.xzxx.seats.common.base.UploadFiles;
import cn.xzxx.seats.common.response.*;
import cn.xzxx.seats.common.utils.DateUtils;
import cn.xzxx.seats.common.utils.StringUtil;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.component.PulldownComponent;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.*;
import cn.xzxx.seats.web.adopt.dto.AdoptUpkeepForm;
import cn.xzxx.seats.web.common.CommonService;
import cn.xzxx.seats.web.upkeep.dto.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class UpkeepAssignService {

  @Autowired
  private UpkeepAssignRestMapper assignRestMapper;

  @Autowired
  private UpkeepAssignMapper upkeepAssignMapper;

  @Autowired
  private CommonService commonService;

  @Autowired
  private PulldownComponent pulldownComponent;

  @Autowired
  private DonateSeatInfoMapper donateSeatInfoMapper;

  @Autowired
  private AdoptSeatInfoMapper adoptSeatInfoMapper;

  @Autowired
  private SeatInfoMapper seatInfoMapper;

  @Autowired
  private SeatInfoAdoptMapper seatInfoAdoptMapper;

  @Autowired
  private FileUploadComponent fileUploadComponent;

  @Autowired
  private AdoptUpkeepMapper adoptUpkeepMapper;

  @Autowired
  private AdoptUpkeepUserMapper adoptUpkeepUserMapper;

  public BaseResponse listInit(UpkeepAssignSearchCondition condition) {

    upkeepAssignMapper.update(null, new LambdaUpdateWrapper<UpkeepAssignEntity>()
            .eq(UpkeepAssignEntity::getStatus, 1)
            .lt(UpkeepAssignEntity::getUpkeepDateEnd, LocalDate.now())
            .set(UpkeepAssignEntity::getStatus, 2));

    ListResponse<UpkeepAssignRecord> listResponse = new ListResponse<>();

    condition.setDefaultSort(OrderItem.desc("total.created_at"));

    List<UpkeepAssignRecord> dataList = getUpkeepAssignRecords(condition, listResponse);
    listResponse.setValue(dataList);

    if (condition.isInit()) {
      List<PulldownItem> statusOptions = pulldownComponent.getUpkeepAssignStatusPulldown();
      listResponse.putData("statusOptions", statusOptions);
    }

    return listResponse;
  }

  private List<UpkeepAssignRecord> getUpkeepAssignRecords(UpkeepAssignSearchCondition condition, ListResponse<UpkeepAssignRecord> listResponse) {
    List<UpkeepAssignRecord> dataList = assignRestMapper.selectDataList(condition, listResponse, e -> {
      UpkeepAssignRecord record = new UpkeepAssignRecord();
      BeanUtils.copyProperties(e, record);
      record.setStatusText(UpkeepAssignStatus.statusName(e.getStatus()));
      record.setPcdName(commonService.getPcdName(e.getProvince(), e.getCity(), e.getStrict(), e.getStreet()));
      record.setCreatedAt(DateUtils.localDateTimeFormat(e.getCreatedAt()));
      return record;
    });
    return dataList;
  }

  /**
   * 养护管理详情 初期化显示
   * @param param
   * @return
   */
  public BaseResponse init(UpkeepParam param) {

    DataResponse<UpkeepForm> result = new DataResponse<>();
    UpkeepForm upkeepForm = new UpkeepForm();
    if (param.getInit() != null && param.getInit()) {
      List<PulldownItem> upkeepUserOptions = new ArrayList<>();
      List<AdoptUpkeepUserEntity> adoptUpkeepUsers = adoptUpkeepUserMapper.selectList(new LambdaQueryWrapper<>());
      for (AdoptUpkeepUserEntity s : adoptUpkeepUsers) {
        upkeepUserOptions.add(new PulldownItem(s.getName(), s.getId()));
      }
      upkeepForm.setUpkeepUserOptions(upkeepUserOptions);
    }
    UpkeepAssignEntity upkeepAssignEntity = upkeepAssignMapper.selectById(param.getId());
    BeanUtils.copyProperties(upkeepAssignEntity, upkeepForm);
    if(upkeepAssignEntity.getSeatType() == 1) {
      // 捐赠
      SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(upkeepAssignEntity.getSeatId());
      if(seatInfoEntity == null) {
        return MessageResponse.newInstance("数据异常，座椅信息不存在。");
      }
      List<DonateSeatInfoEntity> donateInfos = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
              .eq(DonateSeatInfoEntity::getSeatId, seatInfoEntity.getId()).list();
      if(CollectionUtils.isEmpty(donateInfos)) {
        return MessageResponse.newInstance("数据异常，捐赠信息不存在。");
      }
      setUpkeepInitInfo(upkeepForm, upkeepAssignEntity, seatInfoEntity, donateInfos.get(0));

    } else {

      // 认养
      SeatInfoAdoptEntity seatInfoEntity = seatInfoAdoptMapper.selectById(upkeepAssignEntity.getSeatId());
      if(seatInfoEntity == null) {
        return MessageResponse.newInstance("数据异常，座椅信息(认养)不存在。");
      }
      List<AdoptSeatInfoEntity> adoptInfos = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
              .eq(AdoptSeatInfoEntity::getSeatId, seatInfoEntity.getId()).list();
      if(CollectionUtils.isEmpty(adoptInfos)) {
        return MessageResponse.newInstance("数据异常，认养信息不存在。");
      }
      setUpkeepInitInfoAdopt(upkeepForm, upkeepAssignEntity, seatInfoEntity, adoptInfos.get(0));
    }
    return result.setValue(upkeepForm);
  }

  /**
   * 养护详情修改（设定养护人，养护日期）
   * @param form
   * @return
   */
  @Transactional
  public BaseResponse edit(UpkeepForm form) {

    UpkeepAssignEntity upkeepAssignEntity = upkeepAssignMapper.selectById(form.getId());
    upkeepAssignEntity.setUpkeepDateBegin(DateUtils.localDateParse(form.getUpkeepDateBegin()));
    upkeepAssignEntity.setUpkeepDateEnd(DateUtils.localDateParse(form.getUpkeepDateEnd()));
    if(upkeepAssignEntity.getUpkeepDateEnd().isBefore(upkeepAssignEntity.getUpkeepDateBegin())) {
      return MessageResponse.newInstance("结束时间不能小于开始时间。");
    }
    upkeepAssignEntity.setUpkeepUserId(form.getUpkeepUserId());
    if(upkeepAssignEntity.getStatus() == 2
            && (!LocalDate.now().isBefore(upkeepAssignEntity.getUpkeepDateBegin())
              && !LocalDate.now().isAfter(upkeepAssignEntity.getUpkeepDateEnd()))) {
      upkeepAssignEntity.setStatus(1);
    }

    upkeepAssignMapper.updateById(upkeepAssignEntity);

    return MessageResponse.newInstance().addSuccessMessage("养护分配成功！");

  }

  /**
   * 座椅养护记录列表
   * @param condition
   * @return
   */
  public BaseResponse listInitSeat(UpkeepAssignSearchCondition condition) {

    ListResponse<UpkeepAssignSeatRecord> listResponse = new ListResponse<>();
    List<UpkeepAssignSeatRecord> dataList = getUpkeepAssignSeatRecords(condition, listResponse);
    listResponse.setValue(dataList);

    return listResponse;
  }

  private List<UpkeepAssignSeatRecord> getUpkeepAssignSeatRecords(UpkeepAssignSearchCondition condition, ListResponse<UpkeepAssignSeatRecord> listResponse) {
    List<UpkeepAssignSeatRecord> dataList = assignRestMapper.selectDataSeatList(condition, listResponse, e -> {
      UpkeepAssignSeatRecord record = new UpkeepAssignSeatRecord();
      BeanUtils.copyProperties(e, record);
      record.setUpkeepTime(DateUtils.localDateTimeFormat(e.getUpkeepTime()));
      return record;
    });
    return dataList;
  }

  /**
   * 座椅养护记录详情
   * @param id
   * @return
   */
  public BaseResponse initUpkeep(String id) {

    AdoptUpkeepEntity adoptUpkeepEntity = adoptUpkeepMapper.selectById(id);
    if(adoptUpkeepEntity == null) {
      return MessageResponse.newInstance("维护申请信息不存在，请返回列表刷新后再操作。");
    }
    AdoptUpkeepForm adoptUpkeepForm = new AdoptUpkeepForm();
    BeanUtils.copyProperties(adoptUpkeepEntity, adoptUpkeepForm);
    adoptUpkeepForm.setUpkeepImageBefore(fileUploadComponent.getImageUrl(adoptUpkeepEntity.getUpkeepImageBefore()));
    adoptUpkeepForm.setUpkeepImageAfter(fileUploadComponent.getImageUrl(adoptUpkeepEntity.getUpkeepImageAfter()));
    adoptUpkeepForm.setUpkeepTime(adoptUpkeepEntity.getUpkeepTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

    UpkeepAssignEntity upkeepAssignEntity = upkeepAssignMapper.selectById(adoptUpkeepEntity.getUpkeepId());
    if(upkeepAssignEntity.getSeatType() == 1) {
      // 共建
      SeatInfoEntity seatInfoEntity = seatInfoMapper.selectById(upkeepAssignEntity.getSeatId());
      if(seatInfoEntity == null) {
        return MessageResponse.newInstance("数据异常，座椅信息不存在。");
      }
      List<DonateSeatInfoEntity> donateInfos = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
              .eq(DonateSeatInfoEntity::getSeatId, seatInfoEntity.getId()).list();
      if(CollectionUtils.isEmpty(donateInfos)) {
        return MessageResponse.newInstance("数据异常，捐赠信息不存在。");
      }
      DonateSeatInfoEntity donateInfo = donateInfos.get(0);
      adoptUpkeepForm.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
      adoptUpkeepForm.setSeatNo(seatInfoEntity.getSeatNo());
      adoptUpkeepForm.setSeatName(seatInfoEntity.getName());
      adoptUpkeepForm.setSeatProvince(donateInfo.getProvince());
      adoptUpkeepForm.setSeatCity(donateInfo.getCity());
      adoptUpkeepForm.setSeatStrict(donateInfo.getStrict());
      adoptUpkeepForm.setSeatStreet(donateInfo.getStreet());
      adoptUpkeepForm.setSeatAdress(donateInfo.getAdress());

    } else {

      // 认养
      SeatInfoAdoptEntity seatInfoEntity = seatInfoAdoptMapper.selectById(upkeepAssignEntity.getSeatId());
      if(seatInfoEntity == null) {
        return MessageResponse.newInstance("数据异常，座椅信息(认养)不存在。");
      }
      List<AdoptSeatInfoEntity> adoptInfos = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
              .eq(AdoptSeatInfoEntity::getSeatId, seatInfoEntity.getId()).list();
      if(CollectionUtils.isEmpty(adoptInfos)) {
        return MessageResponse.newInstance("数据异常，认养信息不存在。");
      }
      AdoptSeatInfoEntity adoptInfo = adoptInfos.get(0);
      adoptUpkeepForm.setSeatImageUrl(fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
      adoptUpkeepForm.setSeatNo(seatInfoEntity.getSeatNo());
      adoptUpkeepForm.setSeatName(seatInfoEntity.getName());
      adoptUpkeepForm.setSeatProvince(adoptInfo.getProvince());
      adoptUpkeepForm.setSeatCity(adoptInfo.getCity());
      adoptUpkeepForm.setSeatStrict(adoptInfo.getStrict());
      adoptUpkeepForm.setSeatStreet(adoptInfo.getStreet());
      adoptUpkeepForm.setSeatAdress(adoptInfo.getAdress());
    }
    DataResponse<AdoptUpkeepForm> result = new DataResponse<>();
    return result.setValue(adoptUpkeepForm);

  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse deleteUpkeep(String id) {

    // 维护详情记录删除
    adoptUpkeepMapper.deleteById(id);

    return MessageResponse.newInstance().addSuccessMessage("维护记录删除成功！");
  }

  /**
   * 养护管理新增
   * @param seatId
   * @param type 1-共建 2-认养
   */
  public void insUpkeepAssign(Integer seatId, int type) {

    List<UpkeepAssignEntity> upkeepAssigns = new LambdaQueryChainWrapper<>(upkeepAssignMapper)
            .eq(UpkeepAssignEntity::getSeatId, seatId)
            .eq(UpkeepAssignEntity::getSeatType, type).list();

    UpkeepAssignEntity upkeepAssignEntity = new UpkeepAssignEntity();
    upkeepAssignEntity.setSeatType(type);
    upkeepAssignEntity.setSeatId(seatId);
    if(type == 1) {
      // 共建
      if(!CollectionUtils.isEmpty(upkeepAssigns)) {
        return;
      }
      DonateSeatInfoEntity donateSeatInfo = new LambdaQueryChainWrapper<>(donateSeatInfoMapper)
              .eq(DonateSeatInfoEntity::getSeatId, seatId).one();
      upkeepAssignEntity.setDonateNo(donateSeatInfo.getDonateNo());
      upkeepAssignMapper.insert(upkeepAssignEntity);
    } else {

      AdoptSeatInfoEntity adoptSeatInfo = new LambdaQueryChainWrapper<>(adoptSeatInfoMapper)
              .eq(AdoptSeatInfoEntity::getSeatId, seatId).one();

      // 判断是否有捐赠编号，有捐赠编号则 更新 养护管理记录中的认养编号，无则新增生成记录
      if(StringUtils.isEmpty(adoptSeatInfo.getDonateNo())) {
        if(!CollectionUtils.isEmpty(upkeepAssigns)) {
          return;
        }
        upkeepAssignEntity.setDonateNo(adoptSeatInfo.getDonateNo());
        upkeepAssignEntity.setAdoptNo(adoptSeatInfo.getAdoptNo());
        upkeepAssignMapper.insert(upkeepAssignEntity);
      } else {
        UpkeepAssignEntity upkeepAssign = new LambdaQueryChainWrapper<>(upkeepAssignMapper)
                .eq(UpkeepAssignEntity::getDonateNo, adoptSeatInfo.getDonateNo()).one();
        if(upkeepAssign != null) {
          upkeepAssignEntity.setAdoptNo(upkeepAssign.getAdoptNo());
          upkeepAssignMapper.updateById(upkeepAssign);
        }
      }
    }
  }

  private void setUpkeepInitInfoAdopt(UpkeepForm upkeepForm, UpkeepAssignEntity upkeepAssignEntity, SeatInfoAdoptEntity seatInfoEntity, AdoptSeatInfoEntity adoptInfo) {
    upkeepForm.setStatusText(UpkeepAssignStatus.statusName(upkeepAssignEntity.getStatus()));
    upkeepForm.setUpkeepDateBegin(DateUtils.localDateFormat(upkeepAssignEntity.getUpkeepDateBegin()));
    upkeepForm.setUpkeepDateEnd(DateUtils.localDateFormat(upkeepAssignEntity.getUpkeepDateEnd()));
    upkeepForm.setSeatNo(seatInfoEntity.getSeatNo());
    upkeepForm.setSeatName(seatInfoEntity.getName());
    upkeepForm.setSeatMaterial(seatInfoEntity.getMaterial());
    upkeepForm.setSeatSize(seatInfoEntity.getSize());
    upkeepForm.setSeatPrice(seatInfoEntity.getPrice());
    upkeepForm.setOwnerUnit(seatInfoEntity.getOwnerUnit());
    upkeepForm.setConstructUnit(seatInfoEntity.getConstructUnit());
    upkeepForm.setSeatIntroduce(seatInfoEntity.getIntroduce());
    upkeepForm.setProvince(adoptInfo.getProvince());
    upkeepForm.setCity(adoptInfo.getCity());
    upkeepForm.setStrict(adoptInfo.getStrict());
    upkeepForm.setStreet(adoptInfo.getStreet());
    upkeepForm.setAdress(adoptInfo.getAdress());
    upkeepForm.setPcdName(commonService.getPcdName(adoptInfo.getProvince(), adoptInfo.getCity(), adoptInfo.getStrict(), adoptInfo.getStreet()));

    if (!StringUtil.isEmpty(seatInfoEntity.getImage())) {
      UploadFiles uploadFiles = new UploadFiles();
      uploadFiles.addFile(seatInfoEntity.getImage(), "", fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
      upkeepForm.setSeatImage(uploadFiles);
    }
  }

  private void setUpkeepInitInfo(UpkeepForm upkeepForm, UpkeepAssignEntity upkeepAssignEntity, SeatInfoEntity seatInfoEntity, DonateSeatInfoEntity donateInfo) {

    upkeepForm.setStatusText(UpkeepAssignStatus.statusName(upkeepAssignEntity.getStatus()));
    upkeepForm.setUpkeepDateBegin(DateUtils.localDateFormat(upkeepAssignEntity.getUpkeepDateBegin()));
    upkeepForm.setUpkeepDateEnd(DateUtils.localDateFormat(upkeepAssignEntity.getUpkeepDateEnd()));
    upkeepForm.setSeatNo(seatInfoEntity.getSeatNo());
    upkeepForm.setSeatName(seatInfoEntity.getName());
    upkeepForm.setSeatMaterial(seatInfoEntity.getMaterial());
    upkeepForm.setSeatSize(seatInfoEntity.getSize());
    upkeepForm.setSeatPrice(seatInfoEntity.getPrice());
    upkeepForm.setOwnerUnit(seatInfoEntity.getOwnerUnit());
    upkeepForm.setConstructUnit(seatInfoEntity.getConstructUnit());
    upkeepForm.setSeatIntroduce(seatInfoEntity.getIntroduce());
    upkeepForm.setProvince(donateInfo.getProvince());
    upkeepForm.setCity(donateInfo.getCity());
    upkeepForm.setStrict(donateInfo.getStrict());
    upkeepForm.setStreet(donateInfo.getStreet());
    upkeepForm.setAdress(donateInfo.getAdress());
    upkeepForm.setPcdName(commonService.getPcdName(donateInfo.getProvince(), donateInfo.getCity(), donateInfo.getStrict(), donateInfo.getStreet()));

    if (!StringUtil.isEmpty(seatInfoEntity.getImage())) {
      UploadFiles uploadFiles = new UploadFiles();
      uploadFiles.addFile(seatInfoEntity.getImage(), "", fileUploadComponent.getImageUrl(seatInfoEntity.getImage()));
      upkeepForm.setSeatImage(uploadFiles);
    }
  }


}
