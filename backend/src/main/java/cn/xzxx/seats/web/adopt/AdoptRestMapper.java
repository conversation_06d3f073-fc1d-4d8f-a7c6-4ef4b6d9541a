package cn.xzxx.seats.web.adopt;

import cn.xzxx.seats.common.base.IBasePagingMapper;
import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.web.adopt.dto.AdoptApplyRecord;
import cn.xzxx.seats.web.adopt.dto.AdoptRecord;
import cn.xzxx.seats.web.adopt.dto.AdoptSearchCondition;
import cn.xzxx.seats.web.adopt.dto.AdoptUpkeepRecord;
import cn.xzxx.seats.web.adopt.entity.AdoptApplyRecordEntity;
import cn.xzxx.seats.web.adopt.entity.AdoptRecordEntity;
import cn.xzxx.seats.web.adopt.entity.AdoptUpkeepRecordEntity;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.function.Function;

@Mapper
public interface AdoptRestMapper extends IBasePagingMapper {

  default List<AdoptRecord> selectDataList(AdoptSearchCondition condition, ListResponse<AdoptRecord> listResponse,
                                           Function<AdoptRecordEntity, AdoptRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectList(page, condition));
  }

  Page<AdoptRecordEntity> selectList(Page<AdoptRecordEntity> page, @Param(value = "param") AdoptSearchCondition condition);


  default List<AdoptApplyRecord> selectApplyDataList(AdoptSearchCondition condition, ListResponse<AdoptApplyRecord> listResponse,
                                                     Function<AdoptApplyRecordEntity, AdoptApplyRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectApplyList(page, condition));
  }

  Page<AdoptApplyRecordEntity> selectApplyList(Page<AdoptApplyRecordEntity> page, @Param(value = "param") AdoptSearchCondition condition);


  default List<AdoptUpkeepRecord> selectUpkeepDataList(AdoptSearchCondition condition, ListResponse<AdoptUpkeepRecord> listResponse,
                                           Function<AdoptUpkeepRecordEntity, AdoptUpkeepRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectUpkeepList(page, condition));
  }

  Page<AdoptUpkeepRecordEntity> selectUpkeepList(Page<AdoptUpkeepRecordEntity> page, @Param(value = "param") AdoptSearchCondition condition);

}
