package cn.xzxx.seats.web.broadcast.dto.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class BroadcastRecord implements Serializable {

    /**
     * 广播ID
     */
    private Integer id;

    /**
     * 内容
     */
    private String content;

    /**
     * 状态 0:关闭 1:开启 2:暂停
     */
    private Integer status;

    /**
     * 状态 0:关闭 1:开启 2:暂停
     */
    private String statusName;

    /**
     * 创建时间
     */
    private String createdAt;

}
