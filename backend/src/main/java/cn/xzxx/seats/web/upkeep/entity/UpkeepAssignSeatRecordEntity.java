package cn.xzxx.seats.web.upkeep.entity;

import cn.xzxx.seats.common.base.BaseRecordEntity;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UpkeepAssignSeatRecordEntity extends BaseRecordEntity {

    /**
     * ID
     */
    private Integer id;

    /**
     * 养护分配ID
     */
    private String upkeepId;

    /**
     * 养护姓名
     */
    private String upkeepName;

    /**
     * 养护单位
     */
    private String upkeepCompany;

    /**
     * 养护时间
     */
    private LocalDateTime upkeepTime;

    /**
     * 备注
     */
    private String memo;
}
