package cn.xzxx.seats.web.seats;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.web.seats.dto.SeatForm;
import cn.xzxx.seats.web.seats.dto.SeatSearchCondition;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class SeatRestController {

  @Autowired
  private SeatRestService service;

  @GetMapping("/seats/list")
  public BaseResponse listSearch(SeatSearchCondition condition) {
    condition.setDefaultSort(OrderItem.desc("a.created_at"));
    return service.listSearch(condition);
  }

  @GetMapping("/seats/get/{id}")
  public BaseResponse get(@PathVariable String id) {
    return service.get(id);
  }

  @PostMapping("/seats/create")
  public BaseResponse add(SeatForm form) {
    return service.edit(true, form);
  }

  @PostMapping("seats/update")
  public BaseResponse edit(SeatForm form) {
    return service.edit(false, form);
  }

  @DeleteMapping("/seats/delete/{id}")
  public BaseResponse delete(@PathVariable String id) {
    return service.delete(id);
  }

  @GetMapping("/seats/cancel/{id}")
  public BaseResponse cancel(@PathVariable String id) {
    return service.cancel(id);
  }
}
