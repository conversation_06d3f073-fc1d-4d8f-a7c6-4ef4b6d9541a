package cn.xzxx.seats.web.adopt.entity;

import cn.xzxx.seats.common.base.BaseRecordEntity;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AdoptRecordEntity extends BaseRecordEntity {

    /**
     * 可认养座椅ID
     */
    private Integer id;

    /**
     * 可认养座椅NO
     */
    private String adoptNo;

    /**
     * 捐赠座椅NO
     */
    private String donateNo;

    /**
     * 认养金额
     */
    private Integer adoptPrice;

    private Integer adoptTerm;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String adress;

    /**
     * GPS经度
     */
    private Double gpsLng;

    /**
     * GPS维度
     */
    private Double gpsLat;

    /**
     * 认养状态 1:未发布 2:已发布(待认养) 3:被认养
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

    /**
     * 座椅价格
     */
    private Integer seatPrice;

    /**
     * 座椅大小
     */
    private String seatSize;

    /**
     * 座椅材质
     */
    private String seatMaterial;

    /**
     * 座椅主图
     */
    private String seatImage;

    private String seatIntroduce;
}
