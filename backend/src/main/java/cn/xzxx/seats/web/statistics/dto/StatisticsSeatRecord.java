package cn.xzxx.seats.web.statistics.dto;

import lombok.Data;

@Data
public class StatisticsSeatRecord {

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 街道
     */
    private String street;

    /**
     * 发布座椅数量
     */
    private Integer seatCount;

    /**
     * 捐赠率
     */
    private double donateRate;

    /**
     * 空置率
     */
    private double emptyRate;

    /**
     * 捐赠总数量
     */
    private Integer donateCountTotal;

    /**
     * 捐赠总金额
     */
    private Integer payedPriceTotal;

    /**
     * 省市区街道
     */
    private String pcdName;

    /**
     * 认养率
     */
    private double adoptRate;

    /**
     * 认养总数量
     */
    private Integer adoptCountTotal;
}
