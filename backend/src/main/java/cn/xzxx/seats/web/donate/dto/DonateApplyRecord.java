package cn.xzxx.seats.web.donate.dto;

import lombok.Data;

@Data
public class DonateApplyRecord {

    /**
     * 捐赠编号
     */
    private Integer id;

    /**
     * 捐赠编号
     */
    private String donateNo;

    /**
     * 捐赠人open_id
     */
    private String openId;

    /**
     * 座椅编号
     */
    private String seatNo;

    /**
     * 座椅名称
     */
    private String seatName;

    /**
     * 座椅价格
     */
    private Integer seatPrice;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 捐赠份额（1-100）
     */
    private Integer shareNum;

    /**
     * 捐赠状态 1:申请中 2:捐赠审核通过 3:不通过 4:无效
     */
    private Integer status;

    /**
     * 捐赠状态名称
     */
    private String statusText;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 申请通过时间
     */
    private String applyPassTime;

    /**
     * 认捐截止时间（到达截止时间 且 认捐份额>=100 ）
     */
    private String validTime;

    /**
     * 区划代码
     */
    private String code;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 所在街道
     */
    private String street;

    /**
     * 省市县街道
     */
    private String pcdName;
}
