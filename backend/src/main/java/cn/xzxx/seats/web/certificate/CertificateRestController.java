package cn.xzxx.seats.web.certificate;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.web.certificate.dto.CertificateForm;
import cn.xzxx.seats.web.certificate.dto.CertificateSearchCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CertificateRestController {

  @Autowired
  private CertificateRestService service;

  @GetMapping("/certificate/list")
  public BaseResponse listInit(CertificateSearchCondition condition) {

    return service.listInit(condition);
  }

  @GetMapping("/certificate/init")
  public BaseResponse init(Integer id) {

    return service.init(id);
  }

  @PostMapping("/certificate/create")
  public BaseResponse create(CertificateForm form) {

    return service.edit(true, form);
  }

  @PostMapping("/certificate/edit")
  public BaseResponse edit(CertificateForm form) {

    return service.edit(false, form);
  }

  @GetMapping("/certificate/delete")
  public BaseResponse delete(Integer id) {

    return service.delete(id);
  }
}
