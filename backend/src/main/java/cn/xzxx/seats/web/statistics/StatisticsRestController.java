package cn.xzxx.seats.web.statistics;

import cn.xzxx.seats.common.message.MessageConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.config.security.ResponseSupport;
import cn.xzxx.seats.web.common.CommonService;
import cn.xzxx.seats.web.statistics.dto.StatisticsSearchCondition;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@RestController
public class StatisticsRestController {

  @Autowired
  private StatisticsRestService service;

  @Autowired
  private CommonService commonService;

  private static final Logger LOGGER = LoggerFactory.getLogger(StatisticsRestController.class);

  @GetMapping("/statistics/user/list")
  public BaseResponse userListInit(StatisticsSearchCondition condition) {

    return service.userListInit(condition);
  }

  @GetMapping("/statistics/seat/list")
  public BaseResponse seatListInit(StatisticsSearchCondition condition) {

    return service.seatListInit(condition);
  }

  @GetMapping("/statistics/payment/list")
  public BaseResponse paymentListInit(StatisticsSearchCondition condition) {

    return service.paymentListInit(condition);
  }

  @GetMapping("/statistics/user-adopt/list")
  public BaseResponse userAdoptListInit(StatisticsSearchCondition condition) {

    return service.userAdoptListInit(condition);
  }

  @GetMapping("/statistics/seat-adopt/list")
  public BaseResponse seatAdoptListInit(StatisticsSearchCondition condition) {

    return service.seatAdoptListInit(condition);
  }

  @GetMapping("/statistics/user-all/list")
  public BaseResponse userListAllInit(StatisticsSearchCondition condition) {

    return service.userListAllInit(condition);
  }

  @GetMapping("/statistics/export/user")
  public ResponseEntity<byte[]> userExport(StatisticsSearchCondition condition) {

    try {
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      service.exportUserStatistics(os, condition);
      String name = commonService.getExcelName("捐赠用户统计");
      HttpHeaders headers = commonService.getHttpHeaders(name, os);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0081);
    }
  }

  @GetMapping("/statistics/export/seat")
  public ResponseEntity<byte[]> seatExport(StatisticsSearchCondition condition) {

    try {
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      service.exportSeatStatistics(os, condition);
      String name = commonService.getExcelName("座椅统计");
      HttpHeaders headers = commonService.getHttpHeaders(name, os);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0081);
    }
  }

  @GetMapping("/statistics/export/payment")
  public ResponseEntity<byte[]> paymentExport(StatisticsSearchCondition condition) {

    try {
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      service.exportPaymentStatistics(os, condition);
      String name = commonService.getExcelName("款项统计");
      HttpHeaders headers = commonService.getHttpHeaders(name, os);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0081);
    }
  }

  @GetMapping("/statistics/export/user-adopt")
  public ResponseEntity<byte[]> userAdoptExport(StatisticsSearchCondition condition) {

    try {
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      service.exportUserAdoptStatistics(os, condition);
      String name = commonService.getExcelName("认养用户统计");
      HttpHeaders headers = commonService.getHttpHeaders(name, os);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0081);
    }
  }

  @GetMapping("/statistics/export/seat-adopt")
  public ResponseEntity<byte[]> seatAdoptExport(StatisticsSearchCondition condition) {

    try {
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      service.exportSeatAdoptStatistics(os, condition);
      String name = commonService.getExcelName("座椅统计");
      HttpHeaders headers = commonService.getHttpHeaders(name, os);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0081);
    }
  }

  @GetMapping("/statistics/export/user-all")
  public ResponseEntity<byte[]> userAllExport(StatisticsSearchCondition condition) {

    try {
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      service.exportUserAllStatistics(os, condition);
      String name = commonService.getExcelName("捐养用户统计");
      HttpHeaders headers = commonService.getHttpHeaders(name, os);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0081);
    }
  }
}

