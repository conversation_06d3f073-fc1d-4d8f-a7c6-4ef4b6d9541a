package cn.xzxx.seats.web.statistics.dto;

import lombok.Data;

@Data
public class StatisticsUserRecord {

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 性别(1-男 0-女)
     */
    private String sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 省市区
     */
    private String pcdName;

    /**
     * 捐赠总次数
     */
    private Integer donateCount;

    /**
     * 捐赠总金额
     */
    private Integer donateTotalAmount;

    /**
     * 总体捐赠履约率
     */
    private double donateRate;

    /**
     * 认养总次数
     */
    private Integer adoptCount;

    /**
     * 总体认养履约率
     */
    private double adoptRate;
}
