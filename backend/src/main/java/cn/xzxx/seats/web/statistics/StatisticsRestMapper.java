package cn.xzxx.seats.web.statistics;

import cn.xzxx.seats.common.base.IBasePagingMapper;
import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.web.donate.dto.DonateApplyRecord;
import cn.xzxx.seats.web.donate.dto.DonateRecord;
import cn.xzxx.seats.web.donate.dto.DonateSearchCondition;
import cn.xzxx.seats.web.donate.entity.DonateApplyRecordEntity;
import cn.xzxx.seats.web.donate.entity.DonateRecordEntity;
import cn.xzxx.seats.web.statistics.dto.StatisticsSearchCondition;
import cn.xzxx.seats.web.statistics.dto.StatisticsSeatRecord;
import cn.xzxx.seats.web.statistics.dto.StatisticsUserRecord;
import cn.xzxx.seats.web.statistics.entity.StatisticsSeatRecordEntity;
import cn.xzxx.seats.web.statistics.entity.StatisticsUserRecordEntity;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.function.Function;

@Mapper
public interface StatisticsRestMapper extends IBasePagingMapper {

  default List<StatisticsUserRecord> selectUserDataList(StatisticsSearchCondition condition, ListResponse<StatisticsUserRecord> listResponse,
                                                        Function<StatisticsUserRecordEntity, StatisticsUserRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectUserList(page, condition));
  }
  Page<StatisticsUserRecordEntity> selectUserList(Page<StatisticsUserRecordEntity> page, @Param(value = "param") StatisticsSearchCondition condition);


  default List<StatisticsSeatRecord> selectSeatDataList(StatisticsSearchCondition condition, ListResponse<StatisticsSeatRecord> listResponse,
                                                        Function<StatisticsSeatRecordEntity, StatisticsSeatRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectSeatList(page, condition));
  }
  Page<StatisticsSeatRecordEntity> selectSeatList(Page<StatisticsSeatRecordEntity> page, @Param(value = "param") StatisticsSearchCondition condition);


  default List<StatisticsUserRecord> selectUserAdoptDataList(StatisticsSearchCondition condition, ListResponse<StatisticsUserRecord> listResponse,
                                                        Function<StatisticsUserRecordEntity, StatisticsUserRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectUserAdoptList(page, condition));
  }
  Page<StatisticsUserRecordEntity> selectUserAdoptList(Page<StatisticsUserRecordEntity> page, @Param(value = "param") StatisticsSearchCondition condition);


  default List<StatisticsSeatRecord> selectSeatAdoptDataList(StatisticsSearchCondition condition, ListResponse<StatisticsSeatRecord> listResponse,
                                                        Function<StatisticsSeatRecordEntity, StatisticsSeatRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectSeatAdoptList(page, condition));
  }
  Page<StatisticsSeatRecordEntity> selectSeatAdoptList(Page<StatisticsSeatRecordEntity> page, @Param(value = "param") StatisticsSearchCondition condition);

  default List<StatisticsUserRecord> selectUserAllDataList(StatisticsSearchCondition condition, ListResponse<StatisticsUserRecord> listResponse,
                                                        Function<StatisticsUserRecordEntity, StatisticsUserRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectUserAllList(page, condition));
  }
  Page<StatisticsUserRecordEntity> selectUserAllList(Page<StatisticsUserRecordEntity> page, @Param(value = "param") StatisticsSearchCondition condition);

}
