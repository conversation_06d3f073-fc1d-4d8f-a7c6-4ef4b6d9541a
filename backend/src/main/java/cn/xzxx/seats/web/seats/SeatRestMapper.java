package cn.xzxx.seats.web.seats;

import cn.xzxx.seats.common.base.IBasePagingMapper;
import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.web.seats.dto.SeatRecord;
import cn.xzxx.seats.web.seats.dto.SeatSearchCondition;
import cn.xzxx.seats.web.seats.entity.SeatRecordEntity;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.function.Function;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SeatRestMapper extends IBasePagingMapper {

  default List<SeatRecord> selectDataList(SeatSearchCondition condition, ListResponse<SeatRecord> listResponse,
    Function<SeatRecordEntity, SeatRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectList(page, condition));
  }

  Page<SeatRecordEntity> selectList(Page<SeatRecordEntity> page, @Param(value = "param") SeatSearchCondition condition);
}
