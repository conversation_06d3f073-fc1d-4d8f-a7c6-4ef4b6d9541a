package cn.xzxx.seats.web.statistics;

import cn.xzxx.seats.code.SexStatus;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.common.response.PulldownItem;
import cn.xzxx.seats.component.DownloadComponent;
import cn.xzxx.seats.component.PulldownComponent;
import cn.xzxx.seats.repository.entity.AdoptSeatApplyEntity;
import cn.xzxx.seats.repository.entity.DonateSeatApplyEntity;
import cn.xzxx.seats.repository.mapper.AdoptSeatApplyMapper;
import cn.xzxx.seats.repository.mapper.DonateSeatApplyMapper;
import cn.xzxx.seats.web.common.CommonService;
import cn.xzxx.seats.web.statistics.dto.*;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StatisticsRestService {

  @Autowired
  private StatisticsRestMapper statisticsRestMapper;

  @Autowired
  private DonateSeatApplyMapper donateSeatApplyMapper;

  @Autowired
  private AdoptSeatApplyMapper adoptSeatApplyMapper;

  @Autowired
  private CommonService commonService;

  @Autowired
  private PulldownComponent pulldownComponent;

  @Autowired
  DownloadComponent downloadComponent;

  public BaseResponse userListInit(StatisticsSearchCondition condition) {

    ListResponse<StatisticsUserRecord> listResponse = new ListResponse<>();

    List<StatisticsUserRecord> dataList = getStatisticsUserRecords(condition, listResponse);
    listResponse.setValue(dataList);
    return listResponse;
  }

  /**
   * 捐赠用户信息取得
   * @param condition
   * @param listResponse
   * @return
   */
  private List<StatisticsUserRecord> getStatisticsUserRecords(StatisticsSearchCondition condition, ListResponse<StatisticsUserRecord> listResponse) {
    List<DonateSeatApplyEntity> donateSeatApplyEntities = donateSeatApplyMapper.selectList(new LambdaQueryWrapper<>());
    Map<String, List<DonateSeatApplyEntity>> donateMap = donateSeatApplyEntities.stream().collect(Collectors.groupingBy(t -> t.getIdCardNo() + t.getName()));

    List<StatisticsUserRecord> dataList = statisticsRestMapper.selectUserDataList(condition, listResponse, e -> {
      StatisticsUserRecord record = new StatisticsUserRecord();
      List<DonateSeatApplyEntity> seatApplyEntities = donateMap.get(e.getIdCardNo() + e.getName());
      BeanUtils.copyProperties(e, record);
      if(!CollectionUtils.isEmpty(seatApplyEntities)) {
        DonateSeatApplyEntity donateSeatApplyEntity = seatApplyEntities.get(0);
        record.setSex(SexStatus.statusName(donateSeatApplyEntity.getSex()));
        record.setAge(donateSeatApplyEntity.getAge());
        record.setTel(donateSeatApplyEntity.getTel());
        record.setProvince(donateSeatApplyEntity.getProvince());
        record.setCity(donateSeatApplyEntity.getCity());
        record.setStrict(donateSeatApplyEntity.getStrict());
        record.setPcdName(commonService.getStrictName(donateSeatApplyEntity.getProvince() ,donateSeatApplyEntity.getCity(),donateSeatApplyEntity.getStrict()));
        record.setDonateRate(Math.round(Double.valueOf(e.getDonateCount())/Double.valueOf(seatApplyEntities.size())*100));
      }
      return record;
    });
    return dataList;
  }

  public BaseResponse seatListInit(StatisticsSearchCondition condition) {

    ListResponse<StatisticsSeatRecord> listResponse = new ListResponse<>();

    List<StatisticsSeatRecord> dataList = getStatisticsSeatRecords(condition, listResponse);
    listResponse.setValue(dataList);

    if (condition.isInit()) {
      List<PulldownItem> areaTypeOptions = pulldownComponent.getAreaTypeStatusPulldown();
      listResponse.putData("areaTypeOptions", areaTypeOptions);
    }
    return listResponse;
  }

  /**
   * 捐赠座椅信息取得
   * @param condition
   * @param listResponse
   * @return
   */
  private List<StatisticsSeatRecord> getStatisticsSeatRecords(StatisticsSearchCondition condition, ListResponse<StatisticsSeatRecord> listResponse) {
    List<StatisticsSeatRecord> dataList = statisticsRestMapper.selectSeatDataList(condition, listResponse, e -> {
      StatisticsSeatRecord record = new StatisticsSeatRecord();
      BeanUtils.copyProperties(e, record);
      record.setPcdName(commonService.getPcdName(condition.getAreaType(), e.getProvince(), e.getCity(), e.getStrict(), e.getStreet()));
      if(e.getSeatCount() == 0) {
        record.setDonateRate(0);
      } else {
        record.setDonateRate(Math.round(Double.valueOf(e.getDonateCountTotal())/Double.valueOf(e.getSeatCount())*100));
      }
      record.setEmptyRate(100-record.getDonateRate());
      record.setDonateCountTotal(e.getDonateCountTotal());
      record.setPayedPriceTotal(e.getPayedPriceTotal());

      return record;
    });
    return dataList;
  }

  public BaseResponse paymentListInit(StatisticsSearchCondition condition) {

    ListResponse<StatisticsSeatRecord> listResponse = new ListResponse<>();

    List<StatisticsSeatRecord> dataList = getStatisticsSeatRecords(condition, listResponse);
    listResponse.setValue(dataList);

    if (condition.isInit()) {
      List<PulldownItem> areaTypeOptions = pulldownComponent.getAreaTypeStatusPulldown();
      listResponse.putData("areaTypeOptions", areaTypeOptions);
    }
    return listResponse;
  }

  public BaseResponse userAdoptListInit(StatisticsSearchCondition condition) {

    ListResponse<StatisticsUserRecord> listResponse = new ListResponse<>();

    List<StatisticsUserRecord> dataList = getUserAdoptRecords(condition, listResponse);
    listResponse.setValue(dataList);
    return listResponse;
  }

  /**
   * 认养用户信息取得
   * @param condition
   * @param listResponse
   * @return
   */
  private List<StatisticsUserRecord> getUserAdoptRecords(StatisticsSearchCondition condition, ListResponse<StatisticsUserRecord> listResponse) {
    List<AdoptSeatApplyEntity> adoptSeatApplyEntities = adoptSeatApplyMapper.selectList(new LambdaQueryWrapper<>());
    Map<String, List<AdoptSeatApplyEntity>> adoptMap = adoptSeatApplyEntities.stream().collect(Collectors.groupingBy(t -> t.getIdCardNo() + t.getName()));

    List<StatisticsUserRecord> dataList = statisticsRestMapper.selectUserAdoptDataList(condition, listResponse, e -> {
      StatisticsUserRecord record = new StatisticsUserRecord();
      List<AdoptSeatApplyEntity> seatApplyEntities = adoptMap.get(e.getIdCardNo() + e.getName());
      BeanUtils.copyProperties(e, record);
      if(!CollectionUtils.isEmpty(seatApplyEntities)) {
        AdoptSeatApplyEntity adoptSeatpplyEntity = seatApplyEntities.get(0);
        record.setSex(SexStatus.statusName(adoptSeatpplyEntity.getSex()));
        record.setAge(adoptSeatpplyEntity.getAge());
        record.setTel(adoptSeatpplyEntity.getTel());
        record.setProvince(adoptSeatpplyEntity.getProvince());
        record.setCity(adoptSeatpplyEntity.getCity());
        record.setStrict(adoptSeatpplyEntity.getStrict());
        record.setPcdName(commonService.getStrictName(adoptSeatpplyEntity.getProvince() , adoptSeatpplyEntity.getCity(), adoptSeatpplyEntity.getStrict()));
        if(CollectionUtils.isEmpty(seatApplyEntities)) {
          record.setAdoptRate(0);
        } else {
          record.setAdoptRate(Math.round(Double.valueOf(e.getAdoptCount())/Double.valueOf(seatApplyEntities.size())*100));
        }
      }
      return record;
    });
    return dataList;
  }

  public BaseResponse seatAdoptListInit(StatisticsSearchCondition condition) {

    ListResponse<StatisticsSeatRecord> listResponse = new ListResponse<>();

    List<StatisticsSeatRecord> dataList = getSeatAdoptRecords(condition, listResponse);
    listResponse.setValue(dataList);

    if (condition.isInit()) {
      List<PulldownItem> areaTypeOptions = pulldownComponent.getAreaTypeStatusPulldown();
      listResponse.putData("areaTypeOptions", areaTypeOptions);
    }
    return listResponse;
  }

  /**
   * 认养座椅信息取得
   * @param condition
   * @param listResponse
   * @return
   */
  private List<StatisticsSeatRecord> getSeatAdoptRecords(StatisticsSearchCondition condition, ListResponse<StatisticsSeatRecord> listResponse) {
    List<StatisticsSeatRecord> dataList = statisticsRestMapper.selectSeatAdoptDataList(condition, listResponse, e -> {
      StatisticsSeatRecord record = new StatisticsSeatRecord();
      BeanUtils.copyProperties(e, record);
      record.setPcdName(commonService.getPcdName(condition.getAreaType(), e.getProvince(), e.getCity(), e.getStrict(), e.getStreet()));
      if(e.getSeatCount() == 0) {
        record.setAdoptRate(0);
      } else {
        record.setAdoptRate(Math.round(Double.valueOf(e.getAdoptCountTotal())/Double.valueOf(e.getSeatCount())*100));
      }
      record.setEmptyRate(100-record.getAdoptRate());
      record.setAdoptCountTotal(e.getAdoptCountTotal());

      return record;
    });
    return dataList;
  }

  public BaseResponse userListAllInit(StatisticsSearchCondition condition) {

    ListResponse<StatisticsUserRecord> listResponse = new ListResponse<>();

    List<StatisticsUserRecord> dataList = getStatisticsUserAllRecords(condition, listResponse);
    listResponse.setValue(dataList);
    return listResponse;
  }

  /**
   * 捐赠认养用户信息取得
   * @param condition
   * @param listResponse
   * @return
   */
  private List<StatisticsUserRecord> getStatisticsUserAllRecords(StatisticsSearchCondition condition, ListResponse<StatisticsUserRecord> listResponse) {

    // 捐赠
    List<DonateSeatApplyEntity> donateSeatApplyEntities = donateSeatApplyMapper.selectList(new LambdaQueryWrapper<DonateSeatApplyEntity>().eq(DonateSeatApplyEntity::getDeleteFlag, false));
    Map<String, List<DonateSeatApplyEntity>> donateMap = donateSeatApplyEntities.stream().collect(Collectors.groupingBy(t -> t.getIdCardNo() + t.getName()));

    // 认养
    List<AdoptSeatApplyEntity> adoptSeatApplyEntities = adoptSeatApplyMapper.selectList(new LambdaQueryWrapper<>());
    Map<String, List<AdoptSeatApplyEntity>> adoptMap = adoptSeatApplyEntities.stream().collect(Collectors.groupingBy(t -> t.getIdCardNo() + t.getName()));

    List<StatisticsUserRecord> dataList = statisticsRestMapper.selectUserAllDataList(condition, listResponse, e -> {
      StatisticsUserRecord record = new StatisticsUserRecord();
      BeanUtils.copyProperties(e, record);

      List<DonateSeatApplyEntity> donateSeatApplyList = donateMap.get(e.getIdCardNo() + e.getName());
      List<AdoptSeatApplyEntity> adoptSeatApplyList = adoptMap.get(e.getIdCardNo() + e.getName());
      if(!CollectionUtils.isEmpty(donateSeatApplyList)) {

        DonateSeatApplyEntity donateSeatApplyEntity = donateSeatApplyList.get(0);
        record.setSex(SexStatus.statusName(donateSeatApplyEntity.getSex()));
        record.setAge(donateSeatApplyEntity.getAge());
        record.setTel(donateSeatApplyEntity.getTel());
        record.setProvince(donateSeatApplyEntity.getProvince());
        record.setCity(donateSeatApplyEntity.getCity());
        record.setStrict(donateSeatApplyEntity.getStrict());
        record.setPcdName(commonService.getStrictName(donateSeatApplyEntity.getProvince() ,donateSeatApplyEntity.getCity(),donateSeatApplyEntity.getStrict()));
        record.setDonateRate(Math.round(Double.valueOf(e.getDonateCount())/Double.valueOf(donateSeatApplyList.size())*100));
        // 捐赠率
        if(!CollectionUtils.isEmpty(adoptSeatApplyList)) {
          record.setAdoptRate(Math.round(Double.valueOf(e.getAdoptCount())/Double.valueOf(adoptSeatApplyList.size())*100));
        } else {
          record.setAdoptRate(0);
        }
      } else {
        AdoptSeatApplyEntity adoptSeatpplyEntity = adoptSeatApplyList.get(0);
        record.setSex(SexStatus.statusName(adoptSeatpplyEntity.getSex()));
        record.setAge(adoptSeatpplyEntity.getAge());
        record.setTel(adoptSeatpplyEntity.getTel());
        record.setProvince(adoptSeatpplyEntity.getProvince());
        record.setCity(adoptSeatpplyEntity.getCity());
        record.setStrict(adoptSeatpplyEntity.getStrict());
        record.setPcdName(commonService.getStrictName(adoptSeatpplyEntity.getProvince() , adoptSeatpplyEntity.getCity(), adoptSeatpplyEntity.getStrict()));
        record.setDonateRate(0);
        if(CollectionUtils.isEmpty(adoptSeatApplyList)) {
          record.setAdoptRate(0);
        } else {
          record.setAdoptRate(Math.round(Double.valueOf(e.getAdoptCount())/Double.valueOf(adoptSeatApplyList.size())*100));
        }
      }
      return record;
    });
    return dataList;
  }

  /**
   * Excel导出（用户统计）
   * @param os
   * @param condition
   * @return
   */
  public void exportUserStatistics(ByteArrayOutputStream os, StatisticsSearchCondition condition) {

    condition.setPageNumber(0);
    condition.setPageSize(-1);
    ListResponse<StatisticsUserRecord> listResponse = new ListResponse<>();
    List<StatisticsUserRecord> dataList = getStatisticsUserRecords(condition, listResponse);

    List<StatisticsUserExcel> userExcelList = new ArrayList<>();
    dataList.forEach(user->{
      StatisticsUserExcel userExcel = new StatisticsUserExcel();
      BeanUtils.copyProperties(user,userExcel);
      userExcelList.add(userExcel);
    });

    EasyExcel.write(os, StatisticsUserExcel.class).sheet(0).doWrite(userExcelList);

  }

  /**
   * Excel导出（座椅统计）
   * @param os
   * @param condition
   * @return
   */
  public void exportSeatStatistics(ByteArrayOutputStream os, StatisticsSearchCondition condition) {

    condition.setPageNumber(0);
    condition.setPageSize(-1);
    ListResponse<StatisticsSeatRecord> listResponse = new ListResponse<>();
    List<StatisticsSeatRecord> dataList = getStatisticsSeatRecords(condition, listResponse);

    List<StatisticsSeatExcel> excelDataList = new ArrayList<>();
    dataList.forEach(data->{
      StatisticsSeatExcel excelData = new StatisticsSeatExcel();
      BeanUtils.copyProperties(data, excelData);
      excelDataList.add(excelData);
    });
    EasyExcel.write(os, StatisticsSeatExcel.class).sheet(0).doWrite(excelDataList);
  }

  /**
   * Excel导出（款项统计）
   * @param os
   * @param condition
   * @return
   */
  public void exportPaymentStatistics(ByteArrayOutputStream os, StatisticsSearchCondition condition) {

    condition.setPageNumber(0);
    condition.setPageSize(-1);
    ListResponse<StatisticsSeatRecord> listResponse = new ListResponse<>();
    List<StatisticsSeatRecord> dataList = getStatisticsSeatRecords(condition, listResponse);

    List<StatisticsPaymentExcel> excelDataList = new ArrayList<>();
    dataList.forEach(data->{
      StatisticsPaymentExcel excelData = new StatisticsPaymentExcel();
      BeanUtils.copyProperties(data, excelData);
      excelDataList.add(excelData);
    });
    EasyExcel.write(os, StatisticsPaymentExcel.class).sheet(0).doWrite(excelDataList);
  }

  /**
   * Excel导出（认养用户）
   * @param os
   * @param condition
   * @return
   */
  public void exportUserAdoptStatistics(ByteArrayOutputStream os, StatisticsSearchCondition condition) {

    condition.setPageNumber(0);
    condition.setPageSize(-1);
    ListResponse<StatisticsUserRecord> listResponse = new ListResponse<>();
    List<StatisticsUserRecord> dataList = getUserAdoptRecords(condition, listResponse);

    List<StatisticsUserAdoptExcel> excelDataList = new ArrayList<>();
    dataList.forEach(data->{
      StatisticsUserAdoptExcel excelData = new StatisticsUserAdoptExcel();
      BeanUtils.copyProperties(data, excelData);
      excelDataList.add(excelData);
    });
    EasyExcel.write(os, StatisticsUserAdoptExcel.class).sheet(0).doWrite(excelDataList);
  }

  /**
   * Excel导出（认养座椅）
   * @param os
   * @param condition
   * @return
   */
  public void exportSeatAdoptStatistics(ByteArrayOutputStream os, StatisticsSearchCondition condition) {

    condition.setPageNumber(0);
    condition.setPageSize(-1);
    ListResponse<StatisticsSeatRecord> listResponse = new ListResponse<>();
    List<StatisticsSeatRecord> dataList = getSeatAdoptRecords(condition, listResponse);

    List<StatisticsUserAdoptExcel> excelDataList = new ArrayList<>();
    dataList.forEach(data->{
      StatisticsUserAdoptExcel excelData = new StatisticsUserAdoptExcel();
      BeanUtils.copyProperties(data, excelData);
      excelDataList.add(excelData);
    });
    EasyExcel.write(os, StatisticsUserAdoptExcel.class).sheet(0).doWrite(excelDataList);
  }

  /**
   * Excel导出（用户统计）
   * @param os
   * @param condition
   * @return
   */
  public void exportUserAllStatistics(ByteArrayOutputStream os, StatisticsSearchCondition condition) {

    condition.setPageNumber(0);
    condition.setPageSize(-1);
    ListResponse<StatisticsUserRecord> listResponse = new ListResponse<>();
    List<StatisticsUserRecord> dataList = getStatisticsUserAllRecords(condition, listResponse);

    List<StatisticsUserAllExcel> userExcelList = new ArrayList<>();
    dataList.forEach(user->{
      StatisticsUserAllExcel userExcel = new StatisticsUserAllExcel();
      BeanUtils.copyProperties(user,userExcel);
      userExcelList.add(userExcel);
    });

    EasyExcel.write(os, StatisticsUserAllExcel.class).sheet(0).doWrite(userExcelList);

  }
}
