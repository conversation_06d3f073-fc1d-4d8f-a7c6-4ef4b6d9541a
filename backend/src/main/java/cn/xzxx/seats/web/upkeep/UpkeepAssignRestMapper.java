package cn.xzxx.seats.web.upkeep;

import cn.xzxx.seats.common.base.IBasePagingMapper;
import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.repository.entity.UpkeepAssignEntity;
import cn.xzxx.seats.web.adopt.dto.AdoptApplyRecord;
import cn.xzxx.seats.web.adopt.dto.AdoptRecord;
import cn.xzxx.seats.web.adopt.dto.AdoptSearchCondition;
import cn.xzxx.seats.web.adopt.dto.AdoptUpkeepRecord;
import cn.xzxx.seats.web.adopt.entity.AdoptApplyRecordEntity;
import cn.xzxx.seats.web.adopt.entity.AdoptRecordEntity;
import cn.xzxx.seats.web.adopt.entity.AdoptUpkeepRecordEntity;
import cn.xzxx.seats.web.upkeep.dto.UpkeepAssignRecord;
import cn.xzxx.seats.web.upkeep.dto.UpkeepAssignSearchCondition;
import cn.xzxx.seats.web.upkeep.dto.UpkeepAssignSeatRecord;
import cn.xzxx.seats.web.upkeep.entity.UpkeepAssignRecordEntity;
import cn.xzxx.seats.web.upkeep.entity.UpkeepAssignSeatRecordEntity;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.function.Function;

@Mapper
public interface UpkeepAssignRestMapper extends IBasePagingMapper {

  default List<UpkeepAssignRecord> selectDataList(UpkeepAssignSearchCondition condition, ListResponse<UpkeepAssignRecord> listResponse,
                                                  Function<UpkeepAssignRecordEntity, UpkeepAssignRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectList(page, condition));
  }

  Page<UpkeepAssignRecordEntity> selectList(Page<UpkeepAssignRecordEntity> page, @Param(value = "param") UpkeepAssignSearchCondition condition);


  default List<UpkeepAssignSeatRecord> selectDataSeatList(UpkeepAssignSearchCondition condition, ListResponse<UpkeepAssignSeatRecord> listResponse,
                                                          Function<UpkeepAssignSeatRecordEntity, UpkeepAssignSeatRecord> convert) {
    return selectWithPage(condition, listResponse, convert, (page) -> selectSeatList(page, condition));
  }

  Page<UpkeepAssignSeatRecordEntity> selectSeatList(Page<UpkeepAssignSeatRecordEntity> page, @Param(value = "param") UpkeepAssignSearchCondition condition);

}
