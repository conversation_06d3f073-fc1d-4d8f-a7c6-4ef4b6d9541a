package cn.xzxx.seats.web.upkeep;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.web.upkeep.dto.UpkeepAssignSearchCondition;
import cn.xzxx.seats.web.upkeep.dto.UpkeepForm;
import cn.xzxx.seats.web.upkeep.dto.UpkeepParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
public class UpkeepAssignController {

  @Autowired
  private UpkeepAssignService service;


  public static final Logger logger = LoggerFactory.getLogger(UpkeepAssignController.class);

  @GetMapping("/upkeep-assign/list")
  public BaseResponse listInit(UpkeepAssignSearchCondition condition) {

    return service.listInit(condition);
  }

  @GetMapping("/upkeep-assign/init")
  public BaseResponse init(UpkeepParam param) {

    return service.init(param);
  }

  @PostMapping("/upkeep-assign/edit")
  public BaseResponse edit(UpkeepForm adoptForm) {

    return service.edit(adoptForm);
  }

  @GetMapping("/upkeep-assign/list-seat")
  public BaseResponse listInitSeat(UpkeepAssignSearchCondition condition) {

    return service.listInitSeat(condition);
  }

  @DeleteMapping("/upkeep-assign/delete-seat/{id}")
  public BaseResponse deleteUpkeep(@PathVariable String id) {

    return service.deleteUpkeep(id);
  }


  @GetMapping("/upkeep-assign/init-seat")
  public BaseResponse initSeat(String id) {

    return service.initUpkeep(id);
  }
}
