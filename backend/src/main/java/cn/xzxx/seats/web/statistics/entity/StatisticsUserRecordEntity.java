package cn.xzxx.seats.web.statistics.entity;

import cn.xzxx.seats.common.base.BaseRecordEntity;
import lombok.Data;

@Data
public class StatisticsUserRecordEntity extends BaseRecordEntity {

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 性别(1-男 0-女)
     */
    private Integer sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 捐赠总次数
     */
    private Integer donateCount;

    /**
     * 捐赠总金额
     */
    private Integer donateTotalAmount;

    /**
     * 认养总次数
     */
    private Integer adoptCount;

}
