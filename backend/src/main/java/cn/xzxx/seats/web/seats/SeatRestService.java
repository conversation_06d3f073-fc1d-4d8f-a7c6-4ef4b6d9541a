package cn.xzxx.seats.web.seats;

import cn.hutool.core.collection.CollUtil;
import cn.xzxx.seats.code.SeatStatus;
import cn.xzxx.seats.common.base.UploadFiles;
import cn.xzxx.seats.common.base.UploadFiles.RawFile;
import cn.xzxx.seats.common.exception.BusinessException;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.response.ListResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.common.utils.BeanCopyUtils;
import cn.xzxx.seats.common.utils.DateUtils;
import cn.xzxx.seats.common.utils.ListUtils;
import cn.xzxx.seats.component.FileUploadComponent;
import cn.xzxx.seats.repository.entity.*;
import cn.xzxx.seats.repository.mapper.AdoptUpkeepUserMapper;
import cn.xzxx.seats.repository.mapper.SeatImageMapper;
import cn.xzxx.seats.repository.mapper.SeatInfoMapper;
import cn.xzxx.seats.web.seats.dto.SeatForm;
import cn.xzxx.seats.web.seats.dto.SeatModel;
import cn.xzxx.seats.web.seats.dto.SeatRecord;
import cn.xzxx.seats.web.seats.dto.SeatSearchCondition;
import cn.xzxx.seats.web.upkeep.dto.UpkeepUser;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class SeatRestService {

  @Autowired
  private SeatRestMapper seatRestMapper;

  @Autowired
  private SeatInfoMapper seatInfoMapper;

  @Autowired
  private SeatImageMapper seatImageMapper;

  @Autowired
  private FileUploadComponent fileUploadComponent;

  @Resource
  private AdoptUpkeepUserMapper adoptUpkeepUserMapper;

  public BaseResponse listSearch(SeatSearchCondition condition) {

    ListResponse<SeatRecord> listResponse = new ListResponse<>();
    List<SeatRecord> dataList = seatRestMapper.selectDataList(condition, listResponse, e -> {
      SeatRecord record = new SeatRecord();
      BeanUtils.copyProperties(e, record);
      record.setStatusText(SeatStatus.statusName(e.getStatus()));
      record.setStartTime(DateUtils.localDateTimeFormat(e.getStartTime()));
      record.setEndTime(DateUtils.localDateTimeFormat(e.getEndTime()));
      record.setImage(fileUploadComponent.getImageUrl(e.getImage()));
      return record;
    });

    return listResponse;
  }

  public BaseResponse get(String id) {

    DataResponse<SeatModel> dataResponse = new DataResponse<>();

    SeatInfoEntity entity = seatInfoMapper.selectById(id);

    SeatModel model = new SeatModel();
    model.setId(Integer.valueOf(id));
    model.setSeatNo(entity.getSeatNo());
    model.setSeatName(entity.getName());
    model.setSeatSize(entity.getSize());
    model.setMaterial(entity.getMaterial());
    model.setIntroduce(entity.getIntroduce());
    model.setPrice(entity.getPrice());
    model.setStatus(entity.getStatus());
    model.setStatusText(SeatStatus.statusName(entity.getStatus()));

    String image = entity.getImage();
    UploadFiles files = new UploadFiles();
    files.addFile(image, "", fileUploadComponent.getImageUrl(image));
    model.setImage(files);
    List<SeatImageEntity> imageEntityList = seatImageMapper
      .selectList(new QueryWrapper<SeatImageEntity>().eq("seat_id", id));
    if (ListUtils.isNotEmpty(imageEntityList)) {
      UploadFiles uploadFiles = new UploadFiles();

      for (SeatImageEntity e : imageEntityList) {
        uploadFiles.addFile(e.getImage(), "", fileUploadComponent.getImageUrl(e.getImage()));
      }
      model.setImageList(uploadFiles);
    }
    return dataResponse.setValue(model);
  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse edit(boolean add, SeatForm form) {
    SeatInfoEntity entity;

    UploadFiles image = form.getImage();
    UploadFiles imageList = form.getImageList();
    if (add) {
      entity = new SeatInfoEntity();
    } else {
      entity = seatInfoMapper.selectById(form.getId());
      if (entity == null) {
        return MessageResponse.newInstance("该座椅不存在，请返回列表刷新后再操作。");
      }
    }

    entity.setSeatNo(form.getSeatNo());
    entity.setName(form.getSeatName());
    entity.setSize(form.getSeatSize());
    entity.setMaterial(form.getMaterial());
    entity.setIntroduce(form.getIntroduce());
    entity.setStatus(add || !form.isDeploy() ? SeatStatus.NO_PUBLISH.status() : SeatStatus.PUBLISH.status());
    entity.setPrice(form.getPrice());
    entity.setStartTime(null); // TODO
    entity.setEndTime(null);

    List<RawFile> addedFiles = image.addedFiles();
    if (ListUtils.isNotEmpty(addedFiles)) {
      RawFile rawFile = addedFiles.get(0);
      String fileUrl = fileUploadComponent.add(rawFile);
      entity.setImage(fileUrl);
    }
    if (!add) {
      List<RawFile> rawFiles = image.deletedFiles();
      if (ListUtils.isNotEmpty(rawFiles)) {
        fileUploadComponent.delete(rawFiles.get(0));
      }
    }
    if (add) {
      seatInfoMapper.insert(entity);
    } else {
      seatInfoMapper.updateById(entity);
    }

    if (imageList != null) {
      List<RawFile> addedImageList = imageList.addedFiles();
      List<RawFile> deletedImageList = imageList.deletedFiles();

      if (ListUtils.isNotEmpty(addedImageList)) {
        for (RawFile rawFile : addedImageList) {
          String fileUrl = fileUploadComponent.add(rawFile);
          SeatImageEntity imageEntity = new SeatImageEntity();
          imageEntity.setSeatId(entity.getId());
          imageEntity.setImage(fileUrl);
          imageEntity.setRemarks("");
          seatImageMapper.insert(imageEntity);
        }
      }

      if (ListUtils.isNotEmpty(deletedImageList)) {
        for (RawFile rawFile : deletedImageList) {
          String fileId = rawFile.getFileId();
          fileUploadComponent.delete(rawFile);
          seatImageMapper.delete(new QueryWrapper<SeatImageEntity>().eq("image", fileId));
        }
      }
    }
    return new BaseResponse();
  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse delete(String id) {
    SeatInfoEntity entity = seatInfoMapper.selectById(id);
    if (entity == null) {
      throw new BusinessException("该座椅已被删除，请刷新页面后再操作。");
    }

    List<SeatImageEntity> imageEntityList = seatImageMapper
      .selectList(new QueryWrapper<SeatImageEntity>().eq("seat_id", id));

    seatImageMapper.delete(new QueryWrapper<SeatImageEntity>().eq("seat_id", id));
    seatInfoMapper.deleteById(id);

    String image = entity.getImage();
    fileUploadComponent.delete(image);

    for (SeatImageEntity imageEntity : imageEntityList) {
      fileUploadComponent.delete(imageEntity.getImage());
    }
    return MessageResponse.newInstance().addSuccessMessage("座椅删除成功！");
  }

  @Transactional(rollbackFor = Throwable.class)
  public BaseResponse cancel(String id) {

    SeatInfoEntity entity = seatInfoMapper.selectById(id);
    if(entity == null || entity.getStatus() != 1) {
      throw new BusinessException("该座椅信息不在发布状态，无法取消发布，请确认。");
    }
    // 认养取消发布
    entity.setStatus(0);
    seatInfoMapper.updateById(entity);

    return MessageResponse.newInstance().addSuccessMessage("取消发布成功！");
  }

  public BaseResponse addUser(UpkeepUser bo) {
    List<AdoptUpkeepUserEntity> list = new LambdaQueryChainWrapper<>(adoptUpkeepUserMapper)
            .eq(AdoptUpkeepUserEntity::getTelephone, bo.getTelephone())
            .list();
    if (CollUtil.isEmpty(list)) {
      AdoptUpkeepUserEntity adoptUpkeepUserEntity = new AdoptUpkeepUserEntity();
      BeanCopyUtils.copy(bo, adoptUpkeepUserEntity);
      adoptUpkeepUserMapper.insert(adoptUpkeepUserEntity);
    } else {
      AdoptUpkeepUserEntity adoptUpkeepUserEntity = list.get(0);
      BeanCopyUtils.copy(bo, adoptUpkeepUserEntity);
      adoptUpkeepUserMapper.updateById(adoptUpkeepUserEntity);
    }
    return MessageResponse.newInstance().addSuccessMessage("养护人员新增成功！");
  }

  public BaseResponse deleteUser(String id) {
    int count = adoptUpkeepUserMapper.deleteById(id);
    if (count > 0) {
      return MessageResponse.newInstance().addSuccessMessage("养护人员删除成功！");
    } else {
      throw new BusinessException("人员不存在，无法删除！");
    }
  }
}
