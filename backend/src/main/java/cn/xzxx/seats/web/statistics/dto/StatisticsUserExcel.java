package cn.xzxx.seats.web.statistics.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER,verticalAlignment = VerticalAlignment.CENTER)//标题样式,垂直水平居中
@HeadFontStyle(fontName = "微软雅黑",fontHeightInPoints = 11,bold = false)//表头字体样式
@HeadRowHeight(value = 25)//表头行高
@ContentFontStyle(fontName = "微软雅黑",fontHeightInPoints = 11)//内容字体样式
@ContentRowHeight(value = 20)//内容行高
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER,verticalAlignment = VerticalAlignment.CENTER)//内容样式,垂直水平居中
public class StatisticsUserExcel {

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名",index = 0)
    private String name;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号",index = 1)
    @ColumnWidth(value = 30)
    private String idCardNo;

    /**
     * 性别(1-男 0-女)
     */
    @ExcelProperty(value = "性别",index = 2)
    private String sex;

    /**
     * 年龄
     */
    @ExcelProperty(value = "年龄",index = 3)
    private Integer age;

    /**
     * 手机号
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "手机号",index = 4)
    private String tel;

    /**
     * 所在省
     */
    @ExcelProperty(value = "所在省",index = 5)
    private String province;

    /**
     * 所在城市
     */
    @ColumnWidth(value = 12)
    @ExcelProperty(value = "所在城市",index = 6)
    private String city;

    /**
     * 所在区
     */
    @ExcelProperty(value = "所在区",index = 7)
    private String strict;

    /**
     * 捐赠总次数
     */
    @ColumnWidth(value = 15)
    @ExcelProperty(value = "捐赠总次数",index = 8)
    private Integer donateCount;

    /**
     * 捐赠总金额
     */
    @ColumnWidth(value = 15)
    @ExcelProperty(value = "捐赠总金额",index = 9)
    private Integer donateTotalAmount;

    /**
     * 总体捐赠履约率
     */
    @ColumnWidth(value = 18)
    @ExcelProperty(value = "总体捐赠履约率",index = 10)
    private double donateRate;
}
