package cn.xzxx.seats.web.statistics.entity;

import cn.xzxx.seats.common.base.BaseRecordEntity;
import lombok.Data;

@Data
public class StatisticsSeatRecordEntity extends BaseRecordEntity {

    /**
     * 所在省
     */
    private String province;

    /**
     * 所在城市
     */
    private String city;

    /**
     * 所在区
     */
    private String strict;

    /**
     * 街道
     */
    private String street;

    /**
     * 发布座椅数量
     */
    private Integer seatCount;

    /**
     * 捐赠总数量
     */
    private Integer donateCountTotal;

    /**
     * 捐赠总金额
     */
    private Integer payedPriceTotal;

    /**
     * 认养总数量
     */
    private Integer adoptCountTotal;
}
