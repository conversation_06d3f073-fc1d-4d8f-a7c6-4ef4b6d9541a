package cn.xzxx.seats.web.adopt;

import cn.xzxx.seats.common.message.MessageConstants;
import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.MessageResponse;
import cn.xzxx.seats.config.security.ResponseSupport;
import cn.xzxx.seats.web.adopt.dto.AdoptApplyForm;
import cn.xzxx.seats.web.adopt.dto.AdoptExcel;
import cn.xzxx.seats.web.adopt.dto.AdoptForm;
import cn.xzxx.seats.web.adopt.dto.AdoptSearchCondition;
import cn.xzxx.seats.web.common.CommonService;
import cn.xzxx.seats.web.donate.dto.DonateApplyForm;
import cn.xzxx.seats.web.donate.dto.DonateForm;
import cn.xzxx.seats.web.donate.dto.DonateParam;
import cn.xzxx.seats.web.orcode.QrCodeController;
import cn.xzxx.seats.web.seats.SeatRestService;
import cn.xzxx.seats.web.upkeep.dto.UpkeepUser;
import com.alibaba.excel.EasyExcel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;

@RestController
public class AdoptRestController {

  @Autowired
  private AdoptRestService service;

  @Autowired
  private CommonService commonService;

  @Autowired
  private SeatRestService seatRestService;

  public static final Logger logger = LoggerFactory.getLogger(AdoptRestController.class);

  @GetMapping("/adopt/list")
  public BaseResponse listInit(AdoptSearchCondition condition) {

    return service.listInit(condition);
  }

  @GetMapping("/adopt/init")
  public BaseResponse init(DonateParam param) {

    return service.init(param);
  }

  @PostMapping("/adopt/add")
  public BaseResponse add(AdoptForm adoptForm) {

    return service.edit(true, adoptForm);
  }

  @PostMapping("/adopt/edit")
  public BaseResponse edit(AdoptForm adoptForm) {

    return service.edit(false, adoptForm);
  }

  @GetMapping("/adopt-apply/list")
  public BaseResponse applyListInit(AdoptSearchCondition condition) {

    return service.applyListInit(condition);
  }

  @GetMapping("/adopt-apply/init")
  public BaseResponse initApply(String id) {

    return service.initApply(id);
  }

  @PostMapping("/adopt-apply/add")
  public BaseResponse addApply(AdoptApplyForm adoptApplyForm) {

    return service.editApply(true, adoptApplyForm);
  }

  @PostMapping("/adopt-apply/edit")
  public BaseResponse editApply(AdoptApplyForm adoptApplyForm) {

    return service.editApply(false, adoptApplyForm);
  }

//  /**
//   * 维护列表
//   * @param condition
//   * @return
//   */
//  @GetMapping("/adopt-upkeep/list")
//  public BaseResponse upkeepListInit(AdoptSearchCondition condition) {
//
//    return service.upkeepListInit(condition);
//  }
//
//  @GetMapping("/adopt-upkeep/init")
//  public BaseResponse initUpkeep(String id) {
//
//    return service.initUpkeep(id);
//  }

  @PostMapping("/adopt/updStatus")
  public BaseResponse updStatus(DonateForm donateForm) {

    return service.updStatus(donateForm);
  }

  @PostMapping("/adopt-apply/updStatus")
  public BaseResponse updStatusApply(DonateApplyForm form) {

    return service.updStatusApply(form);
  }

  @GetMapping("/adopt/export")
  public ResponseEntity<byte[]> adoptExport(AdoptSearchCondition condition) {

    try {
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      service.exportAdopt(os, condition);
      String name = commonService.getExcelName("认养信息");
      HttpHeaders headers = commonService.getHttpHeaders(name, os);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0081);
    }
  }

  @GetMapping("/adopt-apply/export")
  public ResponseEntity<byte[]> adoptApplyExport(AdoptSearchCondition condition) {

    try {
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      service.exportAdoptApply(os, condition);
      String name = commonService.getExcelName("认养申请统计");
      HttpHeaders headers = commonService.getHttpHeaders(name, os);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0081);
    }
  }

  @GetMapping("/adopt-upkeep/export")
  public ResponseEntity<byte[]> adoptApplyUpkeepExport(AdoptSearchCondition condition) {

    try {
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      service.exportAdoptUpkeepApply(os, condition);
      String name = commonService.getExcelName("维护统计");
      HttpHeaders headers = commonService.getHttpHeaders(name, os);
      return new ResponseEntity<>(os.toByteArray(), headers, HttpStatus.OK);
    } catch (Exception e) {
      return ResponseSupport.messageResponseEntity(MessageConstants.MESSAGE_E0081);
    }
  }

  @DeleteMapping("/adopt/delete/{id}")
  public BaseResponse delete(@PathVariable String id) {
    return service.delete(id);
  }

  @DeleteMapping("/adopt-apply/delete/{id}")
  public BaseResponse deleteApply(@PathVariable String id) {
    return service.deleteApply(id);
  }

  @DeleteMapping("/adopt-upkeep/delete/{id}")
  public BaseResponse deleteUpkeep(@PathVariable String id) {
    return service.deleteUpkeep(id);
  }

  @GetMapping("/adopt/cancel/{id}")
  public BaseResponse cancel(@PathVariable String id) {
    return service.cancel(id);
  }

  @GetMapping("/adopt/seat/{id}")
  public BaseResponse getSeat(@PathVariable String id) {
    return seatRestService.get(id);
  }

  @PostMapping(value = { "/import/adopt" })
  public BaseResponse adoptImport(MultipartFile file) {

    try {
      EasyExcel.read(file.getInputStream(), AdoptExcel.class,
              new ReadDataListener(service)).sheet().doRead();

    } catch (Exception e) {
      logger.error(e.getMessage(), e);
    }
    return MessageResponse.newInstance().addSuccessMessage("认养信息导入成功！");
  }

  /**
   * 养护人新增
   */
  @PostMapping("/adopt-upkeep-user/add")
  public BaseResponse addUser(@RequestBody @Validated UpkeepUser bo) {

    return seatRestService.addUser(bo);
  }

  /**
   * 养护人删除
   */
  @DeleteMapping("/adopt-upkeep-user/delete/{id}")
  public BaseResponse deleteUser(@PathVariable String id) {

    return seatRestService.deleteUser(id);
  }
}
