package cn.xzxx.seats.web.auth;

import cn.xzxx.seats.common.response.BaseResponse;
import cn.xzxx.seats.common.response.DataResponse;
import cn.xzxx.seats.common.response.PulldownItem;
import cn.xzxx.seats.common.utils.DateUtils;
import cn.xzxx.seats.component.PulldownComponent;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

/**
 * Authentication.
 */
@RestController
public class LoginAuthController {

  @Value("${build.version}")
  private String buildVersion;

  @Value("${build.timestamp}")
  private String buildTimestamp;

  @Autowired
  private PulldownComponent pulldownComponent;

  @GetMapping(path = { "/" })
  public ModelAndView index() {
    return new ModelAndView("/index.html");
  }

  /**
   * healthCheck
   *
   * @return response ok
   */
  @GetMapping(value = { "/health/check" }, produces = { "application/json" })
  public BaseResponse healthCheck() {
    return new BaseResponse();
  }

  /**
   * Session Check
   *
   * @return response ok
   */
  @GetMapping(value = { "/session/check" }, produces = { "application/json" })
  public BaseResponse sessionCheck() {
    return new BaseResponse();
  }

  @GetMapping("/version")
  @ResponseBody
  public BaseResponse version() {

    return DataResponse.of("version", buildVersion, "timestamp", DateUtils.convertUtcTimeToTime(buildTimestamp));
  }

  @GetMapping(value = {"/area/cascader"})
  public BaseResponse getAreaPulldown() {
    List<PulldownItem> areaPulldown = pulldownComponent.getAreaPulldown();
    return new DataResponse<>().setValue(areaPulldown);
  }

  @GetMapping("/authority")
  @ResponseBody
  public String authority() {
    Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    if (principal instanceof UserDetails) {
      String username = ((UserDetails) principal).getUsername();
      return "{ \"user\": \"" + username + "\", " + "\"principal\": \"" + principal.toString()
          + "\" }";
    } else {
      String username = principal.toString();
      return "{ \"user\": \"" + username + "\", " + "\"timestamp\": \"" + buildTimestamp + "\" }";
    }
  }

}
