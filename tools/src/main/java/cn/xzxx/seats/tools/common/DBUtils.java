package cn.xzxx.seats.tools.common;

import com.baomidou.mybatisplus.generator.config.DataSourceConfig;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class DBUtils {
  public static List<String> getTables(DataSourceConfig config, String schema) throws Exception {
    List<String> tableNms = new ArrayList<>();
    Class.forName("com.mysql.cj.jdbc.Driver");
    try (Connection connection = DriverManager
        .getConnection(config.getUrl(), config.getUsername(), config.getPassword())) {
      String sql =
          "select table_name from information_schema.tables where table_schema='" + schema + "'";
      Statement statement = connection.createStatement();
      ResultSet result = statement.executeQuery(sql);
      while (result.next()) {
        tableNms.add(result.getString("TABLE_NAME"));
      }
    }
    return tableNms;
  }
}
