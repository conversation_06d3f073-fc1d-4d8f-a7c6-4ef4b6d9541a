package cn.xzxx.seats.tools;

import cn.xzxx.seats.tools.common.DBUtils;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MapperGenerator {

  private static final String STR_PROJECT_PACKAGE_NAME = "cn.xzxx.seats";

  public static void main(String[] args) throws Exception {
    // 代码生成器
    AutoGenerator mpg = new AutoGenerator();

    // 全局配置
    GlobalConfig gc = new GlobalConfig();
    String projectPath = System.getProperty("user.dir") + "/backend";
    gc.setOutputDir(projectPath + "/src/main/java");
    gc.setOpen(false);
    gc.setBaseResultMap(true);
    gc.setBaseColumnList(true);
    gc.setFileOverride(true);
    mpg.setGlobalConfig(gc);

    // 数据源配置
    DataSourceConfig dsc = new DataSourceConfig();
    dsc.setUrl("*************************************************************************************");
    dsc.setDriverName("com.mysql.cj.jdbc.Driver");
    dsc.setUsername("root");
    dsc.setPassword("Xuzhong2022");
    mpg.setDataSource(dsc);

    // 包配置
    PackageConfig pc = new PackageConfig();
    pc.setModuleName("repository");
    pc.setParent(STR_PROJECT_PACKAGE_NAME);
    mpg.setPackageInfo(pc);

    // 策略配置
    StrategyConfig strategy = new StrategyConfig();
    strategy.setNaming(NamingStrategy.underline_to_camel);
    strategy.setColumnNaming(NamingStrategy.underline_to_camel);
    strategy.setTableFillList(Arrays.asList(new TableFill("created_at", FieldFill.INSERT), new TableFill("updated_at", FieldFill.INSERT_UPDATE),
      new TableFill("updated_by", FieldFill.INSERT_UPDATE)));
    strategy.setEntityLombokModel(true);
    List<String> list = DBUtils.getTables(dsc, "seats_share");
    String[] tablses = list.toArray(new String[list.size()]);

    strategy.setInclude("adopt_seat_apply");
    mpg.setStrategy(strategy);

    //配置 自定义模板
    configCustomizedCodeTemplate(mpg);
    configInjection(mpg);

    mpg.execute();
  }

  /**
   * 自定义模板
   *
   * @param mpg
   */
  private static void configCustomizedCodeTemplate(AutoGenerator mpg) {
    // 自定义模板
    TemplateConfig templateConfig = new TemplateConfig().setEntity(null)//指定Entity生成使用自定义模板
      .setMapper("templates/mapper.java") //
      .setController(null)//不生成Controller
      .setService(null).setServiceImpl(null).setXml(null);
    mpg.setTemplate(templateConfig);
  }

  /**
   * 配置自定义参数/属性
   *
   * @param mpg
   */
  private static void configInjection(AutoGenerator mpg) {
    // 自定义配置
    InjectionConfig cfg = new InjectionConfig() {
      @Override
      public void initMap() {
        //  Map<String, Object> map = new HashMap<>();
        //  map.put("abc", this.getConfig().getGlobalConfig().getAuthor() + "-mp");
        //  this.setMap(map);
      }
    };
    // 自定义配置
    List<FileOutConfig> focList = new ArrayList<>();
    focList.add(new FileOutConfig("/templates/entity.java.vm") {
      @Override
      public String outputFile(TableInfo tableInfo) {
        // 自定义输入文件名称
        return mpg.getGlobalConfig().getOutputDir() + "/" + mpg.getPackageInfo().getParent().replace(".", "/") + "/entity" + "/" + tableInfo
          .getEntityName() + "Entity" + StringPool.DOT_JAVA;
      }
    });
    focList.add(new FileOutConfig("/templates/mapper.xml.vm") {
      @Override
      public String outputFile(TableInfo tableInfo) {
        // 自定义输入文件名称
        return mpg.getGlobalConfig().getOutputDir() + "/../resources/mapper/single/" + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
      }
    });
    cfg.setFileOutConfigList(focList);
    mpg.setCfg(cfg);
  }
}
